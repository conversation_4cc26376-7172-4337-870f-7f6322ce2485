-- Enable Row Level Security
-- ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- Create profiles table
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT,
    phone TEXT,
    full_name TEXT,
    avatar_url TEXT,
    auth_method TEXT DEFAULT 'email' CHECK (auth_method IN ('email', 'phone', 'wechat', 'guest')),
    wechat_openid TEXT,
    wechat_unionid TEXT,
    wechat_nickname TEXT,
    device_fingerprint TEXT,
    guest_expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT profiles_contact_check CHECK (
        (email IS NOT NULL) OR
        (phone IS NOT NULL) OR
        (wechat_openid IS NOT NULL) OR
        (device_fingerprint IS NOT NULL)
    )
);

-- Create memoirs table
CREATE TABLE IF NOT EXISTS public.memoirs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    template_type TEXT NOT NULL,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create chapters table
CREATE TABLE IF NOT EXISTS public.chapters (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    memoir_id UUID REFERENCES public.memoirs(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    order_index INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create content_blocks table
CREATE TABLE IF NOT EXISTS public.content_blocks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE NOT NULL,
    content TEXT NOT NULL,
    content_type TEXT DEFAULT 'text' CHECK (content_type IN ('text', 'voice_transcription', 'ai_processed')),
    order_index INTEGER NOT NULL,
    voice_recording_url TEXT,
    ai_processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create photo_albums table
CREATE TABLE IF NOT EXISTS public.photo_albums (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    template_type TEXT NOT NULL,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create album_photos table
CREATE TABLE IF NOT EXISTS public.album_photos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    album_id UUID REFERENCES public.photo_albums(id) ON DELETE CASCADE NOT NULL,
    photo_url TEXT NOT NULL,
    caption TEXT,
    voice_narration_url TEXT,
    order_index INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.memoirs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chapters ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_blocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.photo_albums ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.album_photos ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for profiles
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Create RLS policies for memoirs
CREATE POLICY "Users can view own memoirs" ON public.memoirs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own memoirs" ON public.memoirs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own memoirs" ON public.memoirs
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own memoirs" ON public.memoirs
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for chapters
CREATE POLICY "Users can view own chapters" ON public.chapters
    FOR SELECT USING (
        auth.uid() IN (
            SELECT user_id FROM public.memoirs WHERE id = memoir_id
        )
    );

CREATE POLICY "Users can create chapters for own memoirs" ON public.chapters
    FOR INSERT WITH CHECK (
        auth.uid() IN (
            SELECT user_id FROM public.memoirs WHERE id = memoir_id
        )
    );

CREATE POLICY "Users can update own chapters" ON public.chapters
    FOR UPDATE USING (
        auth.uid() IN (
            SELECT user_id FROM public.memoirs WHERE id = memoir_id
        )
    );

CREATE POLICY "Users can delete own chapters" ON public.chapters
    FOR DELETE USING (
        auth.uid() IN (
            SELECT user_id FROM public.memoirs WHERE id = memoir_id
        )
    );

-- Create RLS policies for content_blocks
CREATE POLICY "Users can view own content blocks" ON public.content_blocks
    FOR SELECT USING (
        auth.uid() IN (
            SELECT m.user_id FROM public.memoirs m
            JOIN public.chapters c ON c.memoir_id = m.id
            WHERE c.id = chapter_id
        )
    );

CREATE POLICY "Users can create content blocks for own chapters" ON public.content_blocks
    FOR INSERT WITH CHECK (
        auth.uid() IN (
            SELECT m.user_id FROM public.memoirs m
            JOIN public.chapters c ON c.memoir_id = m.id
            WHERE c.id = chapter_id
        )
    );

CREATE POLICY "Users can update own content blocks" ON public.content_blocks
    FOR UPDATE USING (
        auth.uid() IN (
            SELECT m.user_id FROM public.memoirs m
            JOIN public.chapters c ON c.memoir_id = m.id
            WHERE c.id = chapter_id
        )
    );

CREATE POLICY "Users can delete own content blocks" ON public.content_blocks
    FOR DELETE USING (
        auth.uid() IN (
            SELECT m.user_id FROM public.memoirs m
            JOIN public.chapters c ON c.memoir_id = m.id
            WHERE c.id = chapter_id
        )
    );

-- Create RLS policies for photo_albums
CREATE POLICY "Users can view own photo albums" ON public.photo_albums
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own photo albums" ON public.photo_albums
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own photo albums" ON public.photo_albums
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own photo albums" ON public.photo_albums
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for album_photos
CREATE POLICY "Users can view own album photos" ON public.album_photos
    FOR SELECT USING (
        auth.uid() IN (
            SELECT user_id FROM public.photo_albums WHERE id = album_id
        )
    );

CREATE POLICY "Users can create photos for own albums" ON public.album_photos
    FOR INSERT WITH CHECK (
        auth.uid() IN (
            SELECT user_id FROM public.photo_albums WHERE id = album_id
        )
    );

CREATE POLICY "Users can update own album photos" ON public.album_photos
    FOR UPDATE USING (
        auth.uid() IN (
            SELECT user_id FROM public.photo_albums WHERE id = album_id
        )
    );

CREATE POLICY "Users can delete own album photos" ON public.album_photos
    FOR DELETE USING (
        auth.uid() IN (
            SELECT user_id FROM public.photo_albums WHERE id = album_id
        )
    );

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_phone ON public.profiles(phone);
CREATE INDEX IF NOT EXISTS idx_profiles_wechat_openid ON public.profiles(wechat_openid);
CREATE INDEX IF NOT EXISTS idx_profiles_wechat_unionid ON public.profiles(wechat_unionid);
CREATE INDEX IF NOT EXISTS idx_profiles_device_fingerprint ON public.profiles(device_fingerprint);
CREATE INDEX IF NOT EXISTS idx_profiles_auth_method ON public.profiles(auth_method);
CREATE INDEX IF NOT EXISTS idx_profiles_guest_expires_at ON public.profiles(guest_expires_at);
CREATE INDEX IF NOT EXISTS idx_memoirs_user_id ON public.memoirs(user_id);
CREATE INDEX IF NOT EXISTS idx_chapters_memoir_id ON public.chapters(memoir_id);
CREATE INDEX IF NOT EXISTS idx_chapters_order ON public.chapters(memoir_id, order_index);
CREATE INDEX IF NOT EXISTS idx_content_blocks_chapter_id ON public.content_blocks(chapter_id);
CREATE INDEX IF NOT EXISTS idx_content_blocks_order ON public.content_blocks(chapter_id, order_index);
CREATE INDEX IF NOT EXISTS idx_photo_albums_user_id ON public.photo_albums(user_id);
CREATE INDEX IF NOT EXISTS idx_album_photos_album_id ON public.album_photos(album_id);
CREATE INDEX IF NOT EXISTS idx_album_photos_order ON public.album_photos(album_id, order_index);

-- Create function to handle user profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (
        id,
        email,
        phone,
        full_name,
        auth_method,
        wechat_openid,
        wechat_unionid,
        wechat_nickname,
        device_fingerprint,
        guest_expires_at
    )
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'phone', NEW.phone),
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'wechat_nickname', ''),
        COALESCE(NEW.raw_user_meta_data->>'auth_method', 'email'),
        NEW.raw_user_meta_data->>'wechat_openid',
        NEW.raw_user_meta_data->>'wechat_unionid',
        NEW.raw_user_meta_data->>'wechat_nickname',
        NEW.raw_user_meta_data->>'device_fingerprint',
        CASE
            WHEN NEW.raw_user_meta_data->>'auth_method' = 'guest'
            THEN (NOW() + INTERVAL '7 days')::TIMESTAMP WITH TIME ZONE
            ELSE NULL
        END
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create profile on user signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_memoirs_updated_at BEFORE UPDATE ON public.memoirs
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_chapters_updated_at BEFORE UPDATE ON public.chapters
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_content_blocks_updated_at BEFORE UPDATE ON public.content_blocks
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_photo_albums_updated_at BEFORE UPDATE ON public.photo_albums
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_album_photos_updated_at BEFORE UPDATE ON public.album_photos
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Create function to clean up expired guest data
CREATE OR REPLACE FUNCTION public.cleanup_expired_guests()
RETURNS INTEGER AS $$
DECLARE
    expired_count INTEGER := 0;
    guest_record RECORD;
BEGIN
    -- Find expired guest users
    FOR guest_record IN
        SELECT id FROM public.profiles
        WHERE auth_method = 'guest'
        AND guest_expires_at < NOW()
    LOOP
        -- Delete related data first (due to foreign key constraints)
        DELETE FROM public.content_blocks
        WHERE chapter_id IN (
            SELECT c.id FROM public.chapters c
            JOIN public.memoirs m ON c.memoir_id = m.id
            WHERE m.user_id = guest_record.id
        );

        DELETE FROM public.chapters
        WHERE memoir_id IN (
            SELECT id FROM public.memoirs
            WHERE user_id = guest_record.id
        );

        DELETE FROM public.album_photos
        WHERE album_id IN (
            SELECT id FROM public.photo_albums
            WHERE user_id = guest_record.id
        );

        DELETE FROM public.memoirs WHERE user_id = guest_record.id;
        DELETE FROM public.photo_albums WHERE user_id = guest_record.id;
        DELETE FROM public.profiles WHERE id = guest_record.id;

        expired_count := expired_count + 1;
    END LOOP;

    RETURN expired_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to be called by cron job or manually
CREATE OR REPLACE FUNCTION public.schedule_guest_cleanup()
RETURNS TEXT AS $$
DECLARE
    cleaned_count INTEGER;
BEGIN
    SELECT public.cleanup_expired_guests() INTO cleaned_count;
    RETURN 'Cleaned up ' || cleaned_count || ' expired guest accounts';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
