# 我的回忆录 - AI智能回忆录创作平台

专为中老年用户设计的AI智能回忆录创作平台，支持语音输入，轻松记录人生故事。

## 功能特点

### 🎤 语音优先设计
- 支持语音转文字，无需打字
- 优化中文语音识别
- 支持多次录制同一话题
- 智能语音质量检测

### 🤖 AI智能整理
- 自动整理语音内容
- 生成连贯的回忆录文本
- 智能章节组织
- 内容润色和优化

### 📱 移动端优化
- 响应式设计，手机优先
- 大按钮，清晰界面
- 适合中老年用户操作
- 支持离线使用

### 🔐 多种登录方式
- 手机号短信验证登录
- 微信一键登录
- 访客模式免注册体验
- 首次验证自动注册
- 安全便捷的认证体验

### 📖 多种模板
- 人生历程模板
- 家族传承模板
- 职业传奇模板
- 旅行回忆模板

### 📸 多媒体支持
- 照片上传和管理
- 语音解说功能
- 生成精美相册
- 多种导出格式

## 技术栈

- **前端**: Next.js 15, React 18, TypeScript
- **样式**: Tailwind CSS
- **数据库**: Supabase (PostgreSQL)
- **认证**: Supabase Auth
- **存储**: Supabase Storage
- **语音识别**: Web Speech API
- **AI处理**: OpenAI API
- **图标**: Lucide React

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd mymemo
```

### 2. 安装依赖

```bash
npm install
```

### 3. 设置环境变量

复制 `.env.local` 文件并填入正确的配置：

```bash
cp .env.local .env.local.example
```

编辑 `.env.local` 文件：

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Gemini API Configuration (for AI content processing)
# 如果不配置，将使用演示模式
GEMINI_API_KEY=your_gemini_api_key

# App Configuration
NEXT_PUBLIC_APP_URL=https://localhost:3000
```

### 4. 设置 Supabase

1. 在 [Supabase](https://supabase.com) 创建新项目
2. 在 SQL 编辑器中运行 `supabase-schema.sql` 文件
3. 在项目设置中获取 URL 和 anon key
4. 更新 `.env.local` 文件

### 5. 启动开发服务器

```bash
npm run dev
```

访问 [https://localhost:3000](https://localhost:3000) 查看应用。

**注意**:
- 应用已配置为使用HTTPS，这是语音识别功能正常工作的必要条件。首次访问时，浏览器可能会显示安全警告，请点击"高级"并选择"继续访问"。
- AI功能：如果未配置Gemini API密钥，应用将使用演示模式，仍可体验完整的语音录制和内容整理流程。
- 认证功能：支持手机号短信验证、微信登录和访客模式，开发环境下验证码会在控制台显示。

## Supabase 设置详细步骤

### 1. 创建 Supabase 项目

1. 访问 [supabase.com](https://supabase.com)
2. 点击 "Start your project"
3. 创建新组织（如果需要）
4. 创建新项目，选择地区（建议选择离用户最近的地区）
5. 等待项目初始化完成

### 2. 配置数据库

1. 在 Supabase 控制台，进入 "SQL Editor"
2. 创建新查询
3. 复制 `supabase-schema.sql` 文件的内容
4. 粘贴并运行 SQL 脚本
5. 确认所有表和策略都已创建

### 3. 配置认证

1. 进入 "Authentication" > "Settings"
2. 在 "Site URL" 中添加：`http://localhost:3000`
3. 在 "Redirect URLs" 中添加：`http://localhost:3000/auth/callback`
4. 保存设置

### 4. 配置存储

1. 进入 "Storage"
2. 创建新 bucket：`memoir-files`
3. 设置为 public（用于存储用户上传的图片和音频）
4. 配置 RLS 策略确保用户只能访问自己的文件

### 5. 获取项目配置

1. 进入 "Settings" > "API"
2. 复制 "Project URL"
3. 复制 "anon public" key
4. 更新 `.env.local` 文件

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # React 组件
│   ├── auth/              # 认证相关组件
│   ├── memoir/            # 回忆录相关组件
│   ├── voice/             # 语音相关组件
│   └── ui/                # 通用 UI 组件
├── lib/                   # 工具库
│   ├── supabase.ts        # Supabase 客户端
│   ├── voice-recognition.ts # 语音识别
│   └── memoir-templates.ts # 回忆录模板
└── types/                 # TypeScript 类型定义
```

## 开发指南

### 添加新的回忆录模板

1. 编辑 `src/lib/memoir-templates.ts`
2. 添加新的模板对象到 `memoirTemplates` 数组
3. 包含章节和引导问题

### 自定义语音识别

1. 编辑 `src/lib/voice-recognition.ts`
2. 调整语言设置和识别参数
3. 添加错误处理逻辑

### 添加新的 UI 组件

1. 在 `src/components/ui/` 创建新组件
2. 使用 Tailwind CSS 进行样式设计
3. 确保移动端响应式设计

## 部署

### Vercel 部署（推荐）

1. 将代码推送到 GitHub
2. 在 Vercel 中导入项目
3. 配置环境变量
4. 部署

### 其他平台

项目支持部署到任何支持 Next.js 的平台，如 Netlify、Railway 等。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License

## 认证功能详细说明

### 手机号登录
- 支持中国大陆手机号格式验证
- 短信验证码登录，首次验证自动注册
- 开发环境下验证码在控制台显示
- 生产环境需配置阿里云短信服务

### 微信登录
- 支持微信OAuth授权登录
- 自动获取微信用户信息
- 首次登录自动创建账户
- 开发环境使用模拟数据

### 访客模式
- 无需注册即可体验所有核心功能
- 访客数据保存7天，可随时升级
- 功能限制：最多2个回忆录，每个回忆录最多5个章节
- 支持数据迁移到正式账户

### 环境变量配置

```env
# 微信登录配置
NEXT_PUBLIC_WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
NEXT_PUBLIC_WECHAT_REDIRECT_URI=https://localhost:3000/auth/wechat/callback

# 短信服务配置
ALIYUN_ACCESS_KEY_ID=your_aliyun_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_aliyun_access_key_secret
SMS_SIGN_NAME=我的回忆录
SMS_TEMPLATE_CODE=SMS_123456789
```

## 支持

如有问题，请创建 Issue 或联系开发团队。
