{"name": "mymemo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --experimental-https", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.50.2", "clsx": "^2.1.1", "framer-motion": "^12.19.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.523.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.58.1", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}