'use client'

import { useState, useRef, useEffect } from 'react'
import { Mic, Play, Pause, Square, RotateCcw } from 'lucide-react'
import { getVoiceRecognition, isVoiceRecognitionSupported } from '@/lib/voice-recognition'

interface VoiceRecorderProps {
  onTranscriptChange?: (transcript: string) => void
  onRecordingComplete?: (transcript: string, audioBlob?: Blob) => void
  placeholder?: string
  className?: string
}

export default function VoiceRecorder({
  onTranscriptChange,
  onRecordingComplete,
  placeholder = "点击麦克风开始录音...",
  className = ""
}: VoiceRecorderProps) {
  const [isRecording, setIsRecording] = useState(false)
  const [transcript, setTranscript] = useState('')
  const [interimTranscript, setInterimTranscript] = useState('')
  const [isPlaying, setIsPlaying] = useState(false)
  const [error, setError] = useState('')
  const [isSupported, setIsSupported] = useState(false)
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioChunksRef = useRef<Blob[]>([])
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const voiceRecognition = useRef(getVoiceRecognition())

  useEffect(() => {
    setIsSupported(isVoiceRecognitionSupported())
  }, [])

  const startRecording = async () => {
    if (!isSupported) {
      setError('您的浏览器不支持语音识别功能')
      return
    }

    setError('')
    setTranscript('')
    setInterimTranscript('')
    audioChunksRef.current = []

    try {
      // Start audio recording
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      mediaRecorderRef.current = new MediaRecorder(stream)
      
      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data)
        }
      }

      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' })

        // Use the current transcript state
        setTranscript(currentTranscript => {
          onRecordingComplete?.(currentTranscript, audioBlob)
          return currentTranscript
        })

        // Create audio URL for playback
        const audioUrl = URL.createObjectURL(audioBlob)
        if (audioRef.current) {
          audioRef.current.src = audioUrl
        }
      }

      mediaRecorderRef.current.start()

      // Start voice recognition
      voiceRecognition.current.startListening(
        (result) => {
          if (result.isFinal) {
            setTranscript(prev => {
              const newTranscript = prev + result.transcript
              onTranscriptChange?.(newTranscript)
              return newTranscript
            })
            setInterimTranscript('')
          } else {
            setInterimTranscript(result.transcript)
          }
        },
        (error) => {
          setError(error)
          setIsRecording(false)
          // Stop media recorder if error occurs
          if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
            mediaRecorderRef.current.stop()
            mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop())
          }
        },
        () => {
          setIsRecording(false)
        }
      )

      setIsRecording(true)
    } catch (err) {
      setError('无法访问麦克风，请检查权限设置')
      console.error('Error starting recording:', err)
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop())
    }
    
    voiceRecognition.current.stopListening()
    setIsRecording(false)
  }

  const playRecording = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause()
        setIsPlaying(false)
      } else {
        audioRef.current.play()
        setIsPlaying(true)
      }
    }
  }

  const resetRecording = () => {
    setTranscript('')
    setInterimTranscript('')
    setError('')
    if (audioRef.current) {
      audioRef.current.src = ''
    }
    onTranscriptChange?.('')
  }

  const displayText = transcript + (interimTranscript ? ` ${interimTranscript}` : '')

  return (
    <div className={`bg-white rounded-2xl shadow-lg p-6 ${className}`}>
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">语音录制</h3>
        <p className="text-sm text-gray-600">
          {isSupported ? '点击麦克风开始录音，说话时请保持清晰' : '您的浏览器不支持语音识别'}
        </p>
      </div>

      {/* Transcript Display */}
      <div className="mb-6">
        <div className="min-h-[120px] p-4 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
          {displayText ? (
            <p className="text-gray-900 leading-relaxed">
              {transcript}
              {interimTranscript && (
                <span className="text-gray-500 italic">{interimTranscript}</span>
              )}
            </p>
          ) : (
            <p className="text-gray-500 italic">{placeholder}</p>
          )}
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      {/* Controls */}
      <div className="flex items-center justify-center space-x-4">
        {/* Record Button */}
        <button
          onClick={isRecording ? stopRecording : startRecording}
          disabled={!isSupported}
          className={`w-16 h-16 rounded-full flex items-center justify-center transition-all duration-200 ${
            isRecording
              ? 'bg-red-500 hover:bg-red-600 animate-pulse'
              : 'bg-blue-500 hover:bg-blue-600'
          } ${
            !isSupported ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'
          } text-white shadow-lg`}
        >
          {isRecording ? (
            <Square className="w-8 h-8" />
          ) : (
            <Mic className="w-8 h-8" />
          )}
        </button>

        {/* Play Button */}
        {transcript && audioRef.current && (
          <button
            onClick={playRecording}
            className="w-12 h-12 rounded-full bg-green-500 hover:bg-green-600 text-white flex items-center justify-center transition-all duration-200 hover:scale-105 shadow-lg"
          >
            {isPlaying ? (
              <Pause className="w-6 h-6" />
            ) : (
              <Play className="w-6 h-6 ml-1" />
            )}
          </button>
        )}

        {/* Reset Button */}
        {transcript && (
          <button
            onClick={resetRecording}
            className="w-12 h-12 rounded-full bg-gray-500 hover:bg-gray-600 text-white flex items-center justify-center transition-all duration-200 hover:scale-105 shadow-lg"
          >
            <RotateCcw className="w-6 h-6" />
          </button>
        )}
      </div>

      {/* Recording Status */}
      {isRecording && (
        <div className="mt-4 text-center">
          <div className="inline-flex items-center space-x-2 text-red-600">
            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium">正在录音...</span>
          </div>
        </div>
      )}

      {/* Audio Element */}
      <audio
        ref={audioRef}
        onEnded={() => setIsPlaying(false)}
        className="hidden"
      />
    </div>
  )
}
