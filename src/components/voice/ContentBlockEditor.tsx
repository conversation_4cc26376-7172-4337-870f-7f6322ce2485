'use client'

import { useState, useEffect } from 'react'
import {
  Edit3,
  Save,
  Mic,
  FileText,
  Image as ImageIcon,
  Clock,
  User,
  MapPin,
  Calendar
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Modal, ModalContent } from '@/components/ui/Modal'
import { SimpleVoiceButton } from '@/components/ui/VoiceButton'
import VoiceContentCreator from './VoiceContentCreator'
import { type ContentBlock } from '@/types/memoir'
import { cn } from '@/lib/utils'

export interface ContentBlockEditorProps {
  readonly block: ContentBlock
  readonly onSave: (block: ContentBlock) => void
  readonly onCancel: () => void
  readonly chapterTitle?: string
  readonly prompts?: string[]
}

export default function ContentBlockEditor({
  block,
  onSave,
  onCancel,
  chapterTitle,
  prompts = []
}: ContentBlockEditorProps) {
  const [editedBlock, setEditedBlock] = useState<ContentBlock>(block)
  const [showVoiceCreator, setShowVoiceCreator] = useState(false)
  const [isEditing, setIsEditing] = useState(false)

  useEffect(() => {
    setEditedBlock(block)
  }, [block])

  const handleSave = () => {
    onSave(editedBlock)
  }

  const handleVoiceContent = (content: string, audioUrl?: string) => {
    setEditedBlock(prev => ({
      ...prev,
      content,
      metadata: {
        ...prev.metadata,
        audioUrl,
        lastModified: new Date().toISOString()
      }
    }))
    setShowVoiceCreator(false)
  }

  const handleContentChange = (content: string) => {
    setEditedBlock(prev => ({
      ...prev,
      content,
      metadata: {
        ...prev.metadata,
        lastModified: new Date().toISOString()
      }
    }))
  }

  const handleMetadataChange = (key: string, value: string) => {
    setEditedBlock(prev => ({
      ...prev,
      metadata: {
        ...prev.metadata,
        [key]: value
      }
    }))
  }

  const getBlockIcon = (type: ContentBlock['type']) => {
    switch (type) {
      case 'text':
        return <FileText className="w-5 h-5" />
      case 'audio':
        return <Mic className="w-5 h-5" />
      case 'image':
        return <ImageIcon className="w-5 h-5" />
      default:
        return <FileText className="w-5 h-5" />
    }
  }

  const getBlockTypeLabel = (type: ContentBlock['type']) => {
    switch (type) {
      case 'text':
        return '文字内容'
      case 'audio':
        return '语音内容'
      case 'image':
        return '图片内容'
      default:
        return '内容'
    }
  }

  return (
    <>
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getBlockIcon(editedBlock.type)}
              <span>编辑{getBlockTypeLabel(editedBlock.type)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <SimpleVoiceButton
                isRecording={false}
                onClick={() => setShowVoiceCreator(true)}
                className="w-10 h-10"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(!isEditing)}
                icon={<Edit3 className="w-4 h-4" />}
              >
                {isEditing ? '预览' : '编辑'}
              </Button>
            </div>
          </CardTitle>
          {chapterTitle && (
            <p className="text-gray-600">章节：{chapterTitle}</p>
          )}
        </CardHeader>

        <CardContent className="space-y-6">
          {/* 内容编辑区域 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              内容
            </label>
            {isEditing ? (
              <textarea
                value={editedBlock.content}
                onChange={(e) => handleContentChange(e.target.value)}
                rows={8}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg leading-relaxed"
                placeholder="在这里输入或编辑内容..."
              />
            ) : (
              <div className="min-h-[200px] p-4 border border-gray-300 rounded-lg bg-gray-50">
                <div className="text-lg leading-relaxed whitespace-pre-wrap">
                  {editedBlock.content || (
                    <span className="text-gray-400">暂无内容</span>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* 元数据编辑 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="时间"
              value={editedBlock.metadata?.time || ''}
              onChange={(e) => handleMetadataChange('time', e.target.value)}
              placeholder="例如：1980年春天"
              leftIcon={<Clock className="w-5 h-5" />}
            />
            
            <Input
              label="地点"
              value={editedBlock.metadata?.location || ''}
              onChange={(e) => handleMetadataChange('location', e.target.value)}
              placeholder="例如：北京市朝阳区"
              leftIcon={<MapPin className="w-5 h-5" />}
            />
            
            <Input
              label="相关人物"
              value={editedBlock.metadata?.people || ''}
              onChange={(e) => handleMetadataChange('people', e.target.value)}
              placeholder="例如：张三、李四"
              leftIcon={<User className="w-5 h-5" />}
            />
            
            <Input
              label="日期"
              type="date"
              value={editedBlock.metadata?.date || ''}
              onChange={(e) => handleMetadataChange('date', e.target.value)}
              leftIcon={<Calendar className="w-5 h-5" />}
            />
          </div>

          {/* 音频播放 */}
          {editedBlock.metadata?.audioUrl && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                录音
              </label>
              <div className="flex items-center space-x-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <Mic className="w-5 h-5 text-blue-600" />
                <audio 
                  controls 
                  src={editedBlock.metadata.audioUrl}
                  className="flex-1"
                >
                  您的浏览器不支持音频播放
                </audio>
              </div>
            </div>
          )}

          {/* 内容统计 */}
          <div className="flex items-center justify-between text-sm text-gray-500 pt-4 border-t border-gray-200">
            <div className="flex items-center space-x-4">
              <span>字数：{editedBlock.content.length}</span>
              {editedBlock.metadata?.lastModified && (
                <span>
                  最后修改：{new Date(editedBlock.metadata.lastModified).toLocaleString('zh-CN')}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <div className={cn(
                'w-2 h-2 rounded-full',
                editedBlock.content.length > 0 ? 'bg-green-500' : 'bg-gray-300'
              )} />
              <span>
                {editedBlock.content.length > 0 ? '有内容' : '空白'}
              </span>
            </div>
          </div>
        </CardContent>

        {/* 操作按钮 */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <Button
            variant="outline"
            onClick={onCancel}
          >
            取消
          </Button>
          
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowVoiceCreator(true)}
              icon={<Mic className="w-5 h-5" />}
            >
              语音录制
            </Button>
            
            <Button
              onClick={handleSave}
              disabled={!editedBlock.content.trim()}
              icon={<Save className="w-5 h-5" />}
            >
              保存更改
            </Button>
          </div>
        </div>
      </Card>

      {/* 语音创建模态框 */}
      <Modal
        isOpen={showVoiceCreator}
        onClose={() => setShowVoiceCreator(false)}
        title="语音内容创建"
        size="xl"
      >
        <ModalContent>
          <VoiceContentCreator
            onSaveContent={handleVoiceContent}
            onCancel={() => setShowVoiceCreator(false)}
            initialContent={editedBlock.content}
            chapterTitle={chapterTitle}
            prompts={prompts}
          />
        </ModalContent>
      </Modal>
    </>
  )
}
