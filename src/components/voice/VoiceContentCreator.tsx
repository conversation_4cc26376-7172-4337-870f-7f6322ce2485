'use client'

import { useState, useEffect, useRef } from 'react'
import {
  Mic,
  Save,
  Trash2,
  Refresh<PERSON><PERSON>,
  CheckCircle,
  AlertCircle,
  Volume2
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { VoiceButton } from '@/components/ui/VoiceButton'
import { VoiceRecordingService, type TranscriptionResult, type VoiceRecordingResult } from '@/lib/voice-recording'
import { cn } from '@/lib/utils'

export interface VoiceContentCreatorProps {
  readonly onSaveContent: (content: string, audioUrl?: string) => void
  readonly onCancel?: () => void
  readonly initialContent?: string
  readonly chapterTitle?: string
  readonly prompts?: string[]
}

export default function VoiceContentCreator({
  onSaveContent,
  onCancel,
  initialContent = '',
  chapterTitle,
  prompts = []
}: VoiceContentCreatorProps) {
  const [isRecording, setIsRecording] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [transcription, setTranscription] = useState(initialContent)
  const [interimTranscription, setInterimTranscription] = useState('')
  const [recordingResult, setRecordingResult] = useState<VoiceRecordingResult | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [currentPromptIndex, setCurrentPromptIndex] = useState(0)
  const [recordingDuration, setRecordingDuration] = useState(0)

  const voiceService = useRef<VoiceRecordingService | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const recordingTimer = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    // 初始化语音服务
    voiceService.current = new VoiceRecordingService()
    
    voiceService.current.setTranscriptionCallback((result: TranscriptionResult) => {
      if (result.isFinal) {
        setTranscription(prev => prev + result.text + ' ')
        setInterimTranscription('')
      } else {
        setInterimTranscription(result.text)
      }
    })

    voiceService.current.setRecordingStateCallback((recording: boolean) => {
      setIsRecording(recording)
    })

    return () => {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current)
      }
    }
  }, [])

  // 录制计时器
  useEffect(() => {
    if (isRecording) {
      recordingTimer.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1)
      }, 1000)
    } else {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current)
      }
      setRecordingDuration(0)
    }

    return () => {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current)
      }
    }
  }, [isRecording])

  const handleStartRecording = async () => {
    try {
      setError(null)
      setIsProcessing(true)
      
      if (!VoiceRecordingService.isSupported()) {
        throw new Error('您的浏览器不支持语音录制功能')
      }

      const hasPermission = await VoiceRecordingService.requestMicrophonePermission()
      if (!hasPermission) {
        throw new Error('需要麦克风权限才能录制语音')
      }

      await voiceService.current?.startRecording()
      setIsProcessing(false)
    } catch (err) {
      setError(err instanceof Error ? err.message : '录制启动失败')
      setIsProcessing(false)
    }
  }

  const handleStopRecording = async () => {
    try {
      setIsProcessing(true)
      const result = await voiceService.current?.stopRecording()
      if (result) {
        setRecordingResult(result)
      }
      setIsProcessing(false)
    } catch (err) {
      setError(err instanceof Error ? err.message : '录制停止失败')
      setIsProcessing(false)
    }
  }

  const handlePlayback = () => {
    if (!recordingResult) return

    if (audioRef.current) {
      audioRef.current.pause()
      audioRef.current = null
    }

    audioRef.current = new Audio(recordingResult.audioUrl)
    audioRef.current.play()
    setIsPlaying(true)

    audioRef.current.onended = () => {
      setIsPlaying(false)
    }
  }

  const handlePausePlayback = () => {
    if (audioRef.current) {
      audioRef.current.pause()
      setIsPlaying(false)
    }
  }

  const handleSave = () => {
    const finalContent = transcription.trim()
    if (finalContent) {
      onSaveContent(finalContent, recordingResult?.audioUrl)
    }
  }

  const handleClear = () => {
    setTranscription('')
    setInterimTranscription('')
    setRecordingResult(null)
    setError(null)
    if (audioRef.current) {
      audioRef.current.pause()
      audioRef.current = null
    }
    setIsPlaying(false)
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const nextPrompt = () => {
    if (currentPromptIndex < prompts.length - 1) {
      setCurrentPromptIndex(prev => prev + 1)
    }
  }

  const prevPrompt = () => {
    if (currentPromptIndex > 0) {
      setCurrentPromptIndex(prev => prev - 1)
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-3">
            <Mic className="w-6 h-6 text-blue-600" />
            <span>语音内容创建</span>
          </CardTitle>
          {chapterTitle && (
            <p className="text-gray-600">章节：{chapterTitle}</p>
          )}
        </CardHeader>
      </Card>

      {/* 引导问题 */}
      {prompts.length > 0 && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">引导问题</h3>
              <span className="text-sm text-gray-500">
                {currentPromptIndex + 1} / {prompts.length}
              </span>
            </div>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <p className="text-blue-900 text-lg leading-relaxed">
                {prompts[currentPromptIndex]}
              </p>
            </div>
            
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                size="sm"
                onClick={prevPrompt}
                disabled={currentPromptIndex === 0}
              >
                上一个
              </Button>
              
              <div className="flex space-x-2">
                {prompts.map((_, index) => (
                  <div
                    key={index}
                    className={cn(
                      'w-2 h-2 rounded-full',
                      index === currentPromptIndex ? 'bg-blue-600' : 'bg-gray-300'
                    )}
                  />
                ))}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={nextPrompt}
                disabled={currentPromptIndex === prompts.length - 1}
              >
                下一个
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 语音录制区域 */}
      <Card>
        <CardContent className="p-8">
          <div className="text-center">
            <VoiceButton
              onStartRecording={handleStartRecording}
              onStopRecording={handleStopRecording}
              onPlayback={handlePlayback}
              onPausePlayback={handlePausePlayback}
              isRecording={isRecording}
              isPlaying={isPlaying}
              hasRecording={!!recordingResult}
              disabled={isProcessing}
              size="xl"
            />
            
            {isRecording && (
              <div className="mt-4">
                <p className="text-lg font-mono text-red-600">
                  {formatDuration(recordingDuration)}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 转录文本显示 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Volume2 className="w-5 h-5" />
            <span>语音转文字</span>
            {VoiceRecordingService.isSpeechRecognitionSupported() ? (
              <CheckCircle className="w-5 h-5 text-green-600" />
            ) : (
              <AlertCircle className="w-5 h-5 text-orange-600" />
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="min-h-[200px] p-4 border border-gray-300 rounded-lg bg-gray-50">
            <div className="text-lg leading-relaxed">
              <span className="text-gray-900">{transcription}</span>
              {interimTranscription && (
                <span className="text-gray-500 italic">{interimTranscription}</span>
              )}
              {!transcription && !interimTranscription && (
                <span className="text-gray-400">
                  开始录制后，您的语音将自动转换为文字显示在这里...
                </span>
              )}
            </div>
          </div>
          
          {!VoiceRecordingService.isSpeechRecognitionSupported() && (
            <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
              <p className="text-sm text-orange-800">
                您的浏览器不支持实时语音转文字功能，但仍可以录制语音。
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Card variant="outlined">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2 text-red-600">
              <AlertCircle className="w-5 h-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 操作按钮 */}
      <div className="flex items-center justify-between space-x-4">
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={handleClear}
            disabled={!transcription && !recordingResult}
            icon={<Trash2 className="w-5 h-5" />}
          >
            清空
          </Button>
          
          <Button
            variant="outline"
            onClick={() => setTranscription('')}
            disabled={!transcription}
            icon={<RefreshCw className="w-5 h-5" />}
          >
            重新录制
          </Button>
        </div>
        
        <div className="flex space-x-3">
          {onCancel && (
            <Button
              variant="outline"
              onClick={onCancel}
            >
              取消
            </Button>
          )}
          
          <Button
            onClick={handleSave}
            disabled={!transcription.trim()}
            loading={isProcessing}
            icon={<Save className="w-5 h-5" />}
          >
            保存内容
          </Button>
        </div>
      </div>
    </div>
  )
}
