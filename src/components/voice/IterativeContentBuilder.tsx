'use client'

import { useState, useEffect } from 'react'
import {
  Plus,
  Mic,
  FileText,
  ArrowUp,
  ArrowDown,
  Trash2,
  Save,
  <PERSON>bulb,
  Target
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Modal, ModalContent } from '@/components/ui/Modal'
import VoiceContentCreator from './VoiceContentCreator'
import ContentBlockEditor from './ContentBlockEditor'
import { type ContentBlock, type Chapter } from '@/types/memoir'

export interface IterativeContentBuilderProps {
  readonly chapter: Chapter
  readonly onUpdateChapter: (chapter: Chapter) => void
  readonly onSave: () => void
  readonly prompts?: string[]
}

export default function IterativeContentBuilder({
  chapter,
  onUpdateChapter,
  onSave,
  prompts = []
}: IterativeContentBuilderProps) {
  const [contentBlocks, setContentBlocks] = useState<ContentBlock[]>(chapter.contentBlocks || [])
  const [showVoiceCreator, setShowVoiceCreator] = useState(false)
  const [editingBlock, setEditingBlock] = useState<ContentBlock | null>(null)
  const [currentPromptIndex, setCurrentPromptIndex] = useState(0)

  useEffect(() => {
    setContentBlocks(chapter.contentBlocks || [])
  }, [chapter])

  const createNewBlock = (content: string, audioUrl?: string): ContentBlock => {
    return {
      id: `block_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'text',
      content,
      order: contentBlocks.length,
      metadata: {
        audioUrl,
        createdAt: new Date().toISOString(),
        lastModified: new Date().toISOString()
      }
    }
  }

  const handleAddVoiceContent = (content: string, audioUrl?: string) => {
    const newBlock = createNewBlock(content, audioUrl)
    const updatedBlocks = [...contentBlocks, newBlock]
    setContentBlocks(updatedBlocks)
    updateChapter(updatedBlocks)
    setShowVoiceCreator(false)
  }

  const handleEditBlock = (block: ContentBlock) => {
    setEditingBlock(block)
  }

  const handleSaveBlock = (updatedBlock: ContentBlock) => {
    const updatedBlocks = contentBlocks.map(block => 
      block.id === updatedBlock.id ? updatedBlock : block
    )
    setContentBlocks(updatedBlocks)
    updateChapter(updatedBlocks)
    setEditingBlock(null)
  }

  const handleDeleteBlock = (blockId: string) => {
    const updatedBlocks = contentBlocks.filter(block => block.id !== blockId)
    // 重新排序
    const reorderedBlocks = updatedBlocks.map((block, index) => ({
      ...block,
      order: index
    }))
    setContentBlocks(reorderedBlocks)
    updateChapter(reorderedBlocks)
  }

  const handleMoveBlock = (blockId: string, direction: 'up' | 'down') => {
    const currentIndex = contentBlocks.findIndex(block => block.id === blockId)
    if (currentIndex === -1) return

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1
    if (newIndex < 0 || newIndex >= contentBlocks.length) return

    const updatedBlocks = [...contentBlocks]
    const [movedBlock] = updatedBlocks.splice(currentIndex, 1)
    updatedBlocks.splice(newIndex, 0, movedBlock)

    // 重新排序
    const reorderedBlocks = updatedBlocks.map((block, index) => ({
      ...block,
      order: index
    }))

    setContentBlocks(reorderedBlocks)
    updateChapter(reorderedBlocks)
  }

  const updateChapter = (blocks: ContentBlock[]) => {
    const updatedChapter: Chapter = {
      ...chapter,
      contentBlocks: blocks,
      wordCount: blocks.reduce((total, block) => total + block.content.length, 0),
      lastModified: new Date().toISOString()
    }
    onUpdateChapter(updatedChapter)
  }

  const handleAddTextBlock = () => {
    const newBlock = createNewBlock('')
    setEditingBlock(newBlock)
  }

  const getCurrentPrompts = () => {
    if (prompts.length === 0) return []
    const startIndex = currentPromptIndex
    return prompts.slice(startIndex, startIndex + 3) // 显示3个相关问题
  }

  const nextPrompts = () => {
    if (currentPromptIndex + 3 < prompts.length) {
      setCurrentPromptIndex(prev => prev + 3)
    }
  }

  const prevPrompts = () => {
    if (currentPromptIndex > 0) {
      setCurrentPromptIndex(prev => Math.max(0, prev - 3))
    }
  }

  const totalWords = contentBlocks.reduce((total, block) => total + block.content.length, 0)
  const hasContent = contentBlocks.some(block => block.content.trim().length > 0)

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* 章节信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <FileText className="w-6 h-6 text-blue-600" />
              <div>
                <h2 className="text-xl font-bold">{chapter.title}</h2>
                <p className="text-sm text-gray-600 font-normal">{chapter.description}</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500">总字数</div>
              <div className="text-2xl font-bold text-blue-600">{totalWords}</div>
            </div>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* 引导问题 */}
      {prompts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Lightbulb className="w-5 h-5 text-yellow-600" />
              <span>写作提示</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {getCurrentPrompts().map((prompt, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <Target className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <p className="text-yellow-900 leading-relaxed">{prompt}</p>
                </div>
              ))}
            </div>
            
            {prompts.length > 3 && (
              <div className="flex items-center justify-between mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={prevPrompts}
                  disabled={currentPromptIndex === 0}
                >
                  上一组
                </Button>
                
                <span className="text-sm text-gray-500">
                  {Math.floor(currentPromptIndex / 3) + 1} / {Math.ceil(prompts.length / 3)}
                </span>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={nextPrompts}
                  disabled={currentPromptIndex + 3 >= prompts.length}
                >
                  下一组
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 快速添加按钮 */}
      <div className="flex items-center justify-center space-x-4">
        <Button
          onClick={() => setShowVoiceCreator(true)}
          icon={<Mic className="w-5 h-5" />}
          size="lg"
        >
          语音录制
        </Button>
        
        <Button
          variant="outline"
          onClick={handleAddTextBlock}
          icon={<Plus className="w-5 h-5" />}
          size="lg"
        >
          添加文字
        </Button>
      </div>

      {/* 内容块列表 */}
      <div className="space-y-4">
        {contentBlocks.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">开始创建内容</h3>
              <p className="text-gray-600 mb-6">
                点击上方按钮开始录制语音或添加文字内容
              </p>
              <div className="flex items-center justify-center space-x-4">
                <Button
                  onClick={() => setShowVoiceCreator(true)}
                  icon={<Mic className="w-5 h-5" />}
                >
                  开始录制
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          contentBlocks
            .sort((a, b) => a.order - b.order)
            .map((block, index) => (
              <Card key={block.id} className="relative">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    {/* 序号 */}
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                      {index + 1}
                    </div>
                    
                    {/* 内容 */}
                    <div className="flex-1 min-w-0">
                      <div className="text-lg leading-relaxed whitespace-pre-wrap">
                        {block.content || (
                          <span className="text-gray-400 italic">空白内容块</span>
                        )}
                      </div>
                      
                      {/* 元数据 */}
                      {(block.metadata?.time || block.metadata?.location) && (
                        <div className="flex items-center space-x-4 mt-3 text-sm text-gray-500">
                          {block.metadata.time && (
                            <span>时间：{block.metadata.time}</span>
                          )}
                          {block.metadata.location && (
                            <span>地点：{block.metadata.location}</span>
                          )}
                        </div>
                      )}
                      
                      {/* 音频 */}
                      {block.metadata?.audioUrl && (
                        <div className="mt-3">
                          <audio 
                            controls 
                            src={block.metadata.audioUrl}
                            className="w-full max-w-md"
                          >
                            您的浏览器不支持音频播放
                          </audio>
                        </div>
                      )}
                    </div>
                    
                    {/* 操作按钮 */}
                    <div className="flex-shrink-0 flex flex-col space-y-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditBlock(block)}
                        className="p-2"
                      >
                        <FileText className="w-4 h-4" />
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleMoveBlock(block.id, 'up')}
                        disabled={index === 0}
                        className="p-2"
                      >
                        <ArrowUp className="w-4 h-4" />
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleMoveBlock(block.id, 'down')}
                        disabled={index === contentBlocks.length - 1}
                        className="p-2"
                      >
                        <ArrowDown className="w-4 h-4" />
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteBlock(block.id)}
                        className="p-2 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
        )}
      </div>

      {/* 保存按钮 */}
      {hasContent && (
        <div className="flex items-center justify-center pt-6">
          <Button
            onClick={onSave}
            size="lg"
            icon={<Save className="w-5 h-5" />}
          >
            保存章节
          </Button>
        </div>
      )}

      {/* 语音创建模态框 */}
      <Modal
        isOpen={showVoiceCreator}
        onClose={() => setShowVoiceCreator(false)}
        title="语音内容创建"
        size="xl"
      >
        <ModalContent>
          <VoiceContentCreator
            onSaveContent={handleAddVoiceContent}
            onCancel={() => setShowVoiceCreator(false)}
            chapterTitle={chapter.title}
            prompts={getCurrentPrompts()}
          />
        </ModalContent>
      </Modal>

      {/* 内容块编辑模态框 */}
      {editingBlock && (
        <Modal
          isOpen={true}
          onClose={() => setEditingBlock(null)}
          title="编辑内容块"
          size="xl"
        >
          <ModalContent>
            <ContentBlockEditor
              block={editingBlock}
              onSave={handleSaveBlock}
              onCancel={() => setEditingBlock(null)}
              chapterTitle={chapter.title}
              prompts={getCurrentPrompts()}
            />
          </ModalContent>
        </Modal>
      )}
    </div>
  )
}
