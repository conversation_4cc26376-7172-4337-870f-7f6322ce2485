'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { Heart, Phone, MessageSquare, Loader2, User<PERSON>he<PERSON> } from 'lucide-react'
import {
  validateChinesePhoneNumber,
  formatPhoneNumber,
  getCleanPhoneNumber,
  VerificationCodeManager,
  type VerificationCodeState
} from '@/lib/phone-validation'
import { createWeChatAuthService, isWeChatBrowser, type WeChatUserInfo } from '@/lib/wechat-auth'
import { GuestAuthService } from '@/lib/guest-auth'

export default function AuthComponent() {
  // 手机号登录状态
  const [phone, setPhone] = useState('')
  const [verificationCode, setVerificationCode] = useState('')
  const [codeState, setCodeState] = useState<VerificationCodeState>({
    sent: false,
    countdown: 0,
    canResend: true
  })

  // 通用状态
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [authMethod, setAuthMethod] = useState<'phone' | 'wechat' | 'guest'>('phone')

  // 验证码管理器
  const [codeManager] = useState(() => new VerificationCodeManager(setCodeState))

  useEffect(() => {
    return () => {
      codeManager.destroy()
    }
  }, [codeManager])

  // 发送短信验证码
  const handleSendSMS = async () => {
    if (!validateChinesePhoneNumber(phone)) {
      setMessage('请输入有效的中国大陆手机号')
      return
    }

    setLoading(true)
    setMessage('')

    try {
      const response = await fetch('/api/auth/send-sms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone: getCleanPhoneNumber(phone) }),
      })

      const data = await response.json()

      if (data.success) {
        setMessage(data.message)
        codeManager.startCountdown(60)
      } else {
        setMessage(data.error || '发送验证码失败')
      }
    } catch (error) {
      console.error('发送验证码失败:', error)
      setMessage('发送验证码失败，请检查网络连接')
    } finally {
      setLoading(false)
    }
  }

  // 验证短信验证码并登录
  const handleVerifySMS = async () => {
    if (!validateChinesePhoneNumber(phone)) {
      setMessage('请输入有效的中国大陆手机号')
      return
    }

    if (!verificationCode || verificationCode.length !== 6) {
      setMessage('请输入6位验证码')
      return
    }

    setLoading(true)
    setMessage('')

    try {
      const response = await fetch('/api/auth/verify-sms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phone: getCleanPhoneNumber(phone),
          code: verificationCode
        }),
      })

      const data = await response.json()

      if (data.success) {
        setMessage(data.isNewUser ? '注册成功！' : '登录成功！')
        // 这里应该处理登录成功后的跳转
        if (data.sessionUrl) {
          window.location.href = data.sessionUrl
        }
      } else {
        setMessage(data.error || '验证失败')
      }
    } catch (error) {
      console.error('验证失败:', error)
      setMessage('验证失败，请检查网络连接')
    } finally {
      setLoading(false)
    }
  }

  // 微信登录
  const handleWeChatLogin = async () => {
    setLoading(true)
    setMessage('')

    try {
      const wechatService = createWeChatAuthService()

      if ('simulateWeChatLogin' in wechatService) {
        // 模拟微信登录
        const result = await wechatService.simulateWeChatLogin()

        if (result.success && result.userInfo) {
          await handleWeChatUserInfo(result.userInfo)
        } else {
          setMessage(result.error || '微信登录失败')
        }
      } else {
        // 真实微信登录
        wechatService.redirectToWeChatAuth()
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      setMessage('微信登录失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 处理微信用户信息
  const handleWeChatUserInfo = async (userInfo: WeChatUserInfo) => {
    try {
      // 检查用户是否已存在
      const { data: existingUser } = await supabase
        .from('profiles')
        .select('*')
        .eq('wechat_openid', userInfo.openid)
        .single()

      // let userId: string

      if (existingUser) {
        // userId = existingUser.id
      } else {
        // 创建新用户
        const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
          email: `${userInfo.openid}@wechat.local`,
          email_confirm: true,
          user_metadata: {
            auth_method: 'wechat',
            wechat_openid: userInfo.openid,
            wechat_unionid: userInfo.unionid,
            wechat_nickname: userInfo.nickname,
            full_name: userInfo.nickname
          }
        })

        if (createError ?? !newUser.user) {
          throw new Error('创建用户失败')
        }

        // userId = newUser.user.id
      }

      setMessage('微信登录成功！')
      // 这里应该处理登录成功后的跳转
    } catch (error) {
      console.error('处理微信用户信息失败:', error)
      setMessage('微信登录失败，请重试')
    }
  }

  // 访客登录
  const handleGuestLogin = async () => {
    setLoading(true)
    setMessage('')

    try {
      const deviceFingerprint = GuestAuthService.generateDeviceFingerprint()

      const response = await fetch('/api/auth/guest-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ deviceFingerprint }),
      })

      const data = await response.json()

      if (data.success) {
        setMessage(data.message)
        // 保存访客信息到本地存储
        GuestAuthService.createGuestUser()

        // 设置访客状态到localStorage，供主页面识别
        localStorage.setItem('guest_mode', 'true')
        localStorage.setItem('guest_user_id', data.userId)

        // 直接跳转到主页面
        setTimeout(() => {
          window.location.href = '/'
        }, 1000)
      } else {
        setMessage(data.error ?? '访客登录失败')
      }
    } catch {
      setMessage('访客登录失败，请检查网络连接')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full mb-4">
            <Heart className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">我的回忆录</h1>
          <p className="text-gray-600 text-lg">用声音记录人生，用AI整理回忆</p>
        </div>

        {/* Auth Form */}
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 text-center mb-2">登录</h2>
            <p className="text-gray-600 text-center">选择您喜欢的登录方式</p>
          </div>

          {/* Login Method Selector */}
          <div className="mb-6">
            <div className="grid grid-cols-3 bg-gray-100 rounded-lg p-1 gap-1">
              <button
                type="button"
                onClick={() => setAuthMethod('phone')}
                className={`py-3 px-2 rounded-md text-xs font-medium transition-colors flex flex-col items-center justify-center space-y-1 ${
                  authMethod === 'phone'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Phone className="w-4 h-4" />
                <span>手机号</span>
              </button>
              <button
                type="button"
                onClick={() => setAuthMethod('wechat')}
                className={`py-3 px-2 rounded-md text-xs font-medium transition-colors flex flex-col items-center justify-center space-y-1 ${
                  authMethod === 'wechat'
                    ? 'bg-white text-green-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <MessageSquare className="w-4 h-4" />
                <span>微信</span>
              </button>
              <button
                type="button"
                onClick={() => setAuthMethod('guest')}
                className={`py-3 px-2 rounded-md text-xs font-medium transition-colors flex flex-col items-center justify-center space-y-1 ${
                  authMethod === 'guest'
                    ? 'bg-white text-orange-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <UserCheck className="w-4 h-4" />
                <span>访客</span>
              </button>
            </div>
          </div>

          {/* Phone Login Form */}
          {authMethod === 'phone' && (
            <div className="space-y-4">
              {/* Phone Number Input */}
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                  手机号
                </label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    id="phone"
                    type="tel"
                    value={phone}
                    onChange={(e) => setPhone(formatPhoneNumber(e.target.value))}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
                    placeholder="请输入手机号"
                    maxLength={13}
                  />
                </div>
              </div>

              {/* Verification Code Input */}
              <div>
                <label htmlFor="code" className="block text-sm font-medium text-gray-700 mb-2">
                  验证码
                </label>
                <div className="flex space-x-3">
                  <div className="flex-1 relative">
                    <MessageSquare className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      id="code"
                      type="text"
                      value={verificationCode}
                      onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
                      placeholder="6位验证码"
                      maxLength={6}
                    />
                  </div>
                  <button
                    type="button"
                    onClick={handleSendSMS}
                    disabled={loading || !codeState.canResend || !validateChinesePhoneNumber(phone)}
                    className="px-4 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors whitespace-nowrap"
                  >
                    {codeState.sent && !codeState.canResend
                      ? `${codeState.countdown}s`
                      : codeState.sent
                        ? '重新发送'
                        : '获取验证码'
                    }
                  </button>
                </div>
              </div>

              {/* Login Button */}
              <button
                type="button"
                onClick={handleVerifySMS}
                disabled={loading || !validateChinesePhoneNumber(phone) || verificationCode.length !== 6}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-4 rounded-lg font-medium text-lg hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <Loader2 className="animate-spin h-5 w-5 mr-2" />
                    验证中...
                  </div>
                ) : (
                  '登录'
                )}
              </button>
            </div>
          )}

          {/* WeChat Login */}
          {authMethod === 'wechat' && (
            <div className="space-y-4">
              <div className="text-center py-8">
                <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MessageSquare className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">微信登录</h3>
                <p className="text-gray-600 mb-6">
                  {isWeChatBrowser()
                    ? '点击下方按钮使用微信账号登录'
                    : '请在微信中打开此页面进行登录'
                  }
                </p>

                <button
                  type="button"
                  onClick={handleWeChatLogin}
                  disabled={loading || (!isWeChatBrowser() && process.env.NODE_ENV === 'production')}
                  className="w-full bg-green-500 text-white py-3 px-4 rounded-lg font-medium text-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                >
                  {loading ? (
                    <div className="flex items-center justify-center">
                      <Loader2 className="animate-spin h-5 w-5 mr-2" />
                      登录中...
                    </div>
                  ) : (
                    <div className="flex items-center justify-center">
                      <MessageSquare className="w-5 h-5 mr-2" />
                      {process.env.NODE_ENV === 'development' ? '微信登录（演示）' : '微信登录'}
                    </div>
                  )}
                </button>
              </div>
            </div>
          )}

          {/* Guest Login */}
          {authMethod === 'guest' && (
            <div className="space-y-4">
              <div className="text-center py-8">
                <div className="w-20 h-20 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <UserCheck className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">访客体验</h3>
                <p className="text-gray-600 mb-6">
                  无需注册，立即体验所有功能。访客数据保存7天，
                  期间可随时绑定手机或微信永久保存。
                </p>

                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
                  <h4 className="text-sm font-medium text-orange-900 mb-2">访客模式特点</h4>
                  <ul className="text-xs text-orange-800 space-y-1 text-left">
                    <li>• 可创建最多2个回忆录</li>
                    <li>• 每个回忆录最多5个章节</li>
                    <li>• 支持语音录制和AI整理</li>
                    <li>• 数据保存7天，可随时升级</li>
                  </ul>
                </div>

                <button
                  type="button"
                  onClick={handleGuestLogin}
                  disabled={loading}
                  className="w-full bg-gradient-to-r from-orange-500 to-red-500 text-white py-3 px-4 rounded-lg font-medium text-lg hover:from-orange-600 hover:to-red-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                >
                  {loading ? (
                    <div className="flex items-center justify-center">
                      <Loader2 className="animate-spin h-5 w-5 mr-2" />
                      创建访客账户...
                    </div>
                  ) : (
                    <div className="flex items-center justify-center">
                      <UserCheck className="w-5 h-5 mr-2" />
                      免注册立即体验
                    </div>
                  )}
                </button>
              </div>
            </div>
          )}

          {/* Message Display */}
          {message && (
            <div className={`mt-4 p-3 rounded-lg text-sm ${
              message.includes('成功')
                ? 'bg-green-50 text-green-700 border border-green-200'
                : 'bg-red-50 text-red-700 border border-red-200'
            }`}>
              {message}
            </div>
          )}

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              {authMethod === 'phone'
                ? '首次验证即自动注册，安全便捷'
                : authMethod === 'wechat'
                  ? '微信登录，一键注册'
                  : '访客模式，无需注册即可体验'
              }
            </p>
          </div>
        </div>

        {/* Features */}
        <div className="mt-8 grid grid-cols-1 gap-4 text-center">
          <div className="bg-white/50 backdrop-blur-sm rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-1">🎤 语音输入</h3>
            <p className="text-sm text-gray-600">说话即可记录，无需打字</p>
          </div>
          <div className="bg-white/50 backdrop-blur-sm rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-1">🤖 AI整理</h3>
            <p className="text-sm text-gray-600">智能整理内容，生成精美回忆录</p>
          </div>
          <div className="bg-white/50 backdrop-blur-sm rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-1">📱 手机优先</h3>
            <p className="text-sm text-gray-600">专为手机设计，随时随地记录</p>
          </div>
        </div>

        {/* Development Notice */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <h4 className="text-sm font-medium text-amber-900 mb-2">🔧 开发模式提示</h4>
            <div className="text-xs text-amber-800 space-y-1">
              <p>• 短信验证码将在控制台显示</p>
              <p>• 微信登录使用模拟数据</p>
              <p>• 生产环境将使用真实的短信和微信服务</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
