'use client'

import { useState, useEffect } from 'react'
import { Mi<PERSON>, MicOff, <PERSON>, Play, Pause } from 'lucide-react'
import { cn } from '@/lib/utils'

export interface VoiceButtonProps {
  readonly onStartRecording: () => void
  readonly onStopRecording: () => void
  readonly onPlayback?: () => void
  readonly onPausePlayback?: () => void
  readonly isRecording?: boolean
  readonly isPlaying?: boolean
  readonly hasRecording?: boolean
  readonly disabled?: boolean
  readonly size?: 'md' | 'lg' | 'xl'
  readonly className?: string
}

export function VoiceButton({
  onStartRecording,
  onStopRecording,
  onPlayback,
  onPausePlayback,
  isRecording = false,
  isPlaying = false,
  hasRecording = false,
  disabled = false,
  size = 'xl',
  className
}: VoiceButtonProps) {
  const [recordingTime, setRecordingTime] = useState(0)

  // Recording timer
  useEffect(() => {
    let interval: NodeJS.Timeout
    
    if (isRecording) {
      interval = setInterval(() => {
        setRecordingTime(prev => prev + 1)
      }, 1000)
    } else {
      setRecordingTime(0)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isRecording])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const sizes = {
    md: {
      button: 'w-20 h-20',
      icon: 'w-8 h-8',
      text: 'text-base'
    },
    lg: {
      button: 'w-24 h-24',
      icon: 'w-10 h-10',
      text: 'text-lg'
    },
    xl: {
      button: 'w-32 h-32',
      icon: 'w-12 h-12',
      text: 'text-xl'
    }
  }

  const currentSize = sizes[size]

  const handleMainAction = () => {
    if (disabled) return
    
    if (isRecording) {
      onStopRecording()
    } else {
      onStartRecording()
    }
  }

  const handlePlayback = () => {
    if (disabled || !hasRecording) return
    
    if (isPlaying) {
      onPausePlayback?.()
    } else {
      onPlayback?.()
    }
  }

  return (
    <div className={cn('flex flex-col items-center space-y-4', className)}>
      {/* Main Recording Button */}
      <div className="relative">
        <button
          onClick={handleMainAction}
          disabled={disabled}
          className={cn(
            'rounded-full flex items-center justify-center transition-all duration-200',
            'focus:outline-none focus:ring-4 focus:ring-offset-2',
            'disabled:opacity-50 disabled:cursor-not-allowed',
            'active:scale-95 transform',
            currentSize.button,
            isRecording 
              ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500 shadow-lg animate-pulse' 
              : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 shadow-lg hover:shadow-xl'
          )}
        >
          {isRecording ? (
            <Square className={cn(currentSize.icon, 'text-white')} />
          ) : (
            <Mic className={cn(currentSize.icon, 'text-white')} />
          )}
        </button>
        
        {/* Recording indicator */}
        {isRecording && (
          <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full animate-ping" />
        )}
      </div>

      {/* Status Text */}
      <div className="text-center">
        {isRecording ? (
          <div className="space-y-1">
            <p className={cn('font-bold text-red-600', currentSize.text)}>
              录制中...
            </p>
            <p className="text-lg font-mono text-gray-600">
              {formatTime(recordingTime)}
            </p>
          </div>
        ) : hasRecording ? (
          <p className={cn('font-medium text-green-600', currentSize.text)}>
            录制完成
          </p>
        ) : (
          <p className={cn('font-medium text-gray-600', currentSize.text)}>
            点击开始录制
          </p>
        )}
      </div>

      {/* Playback Controls */}
      {hasRecording && !isRecording && (
        <div className="flex items-center space-x-4">
          <button
            onClick={handlePlayback}
            disabled={disabled}
            className={cn(
              'flex items-center justify-center w-12 h-12 rounded-full',
              'bg-gray-600 hover:bg-gray-700 text-white',
              'focus:outline-none focus:ring-2 focus:ring-gray-500',
              'disabled:opacity-50 disabled:cursor-not-allowed',
              'transition-all duration-200 active:scale-95'
            )}
          >
            {isPlaying ? (
              <Pause className="w-5 h-5" />
            ) : (
              <Play className="w-5 h-5 ml-0.5" />
            )}
          </button>
          
          <span className="text-sm text-gray-500">
            {isPlaying ? '播放中' : '点击播放'}
          </span>
        </div>
      )}

      {/* Instructions */}
      {!hasRecording && !isRecording && (
        <div className="text-center max-w-xs">
          <p className="text-sm text-gray-500 leading-relaxed">
            按住录制按钮开始说话，松开停止录制。请在安静的环境中录制以获得最佳效果。
          </p>
        </div>
      )}
    </div>
  )
}

// Simplified voice button for inline use
export function SimpleVoiceButton({
  isRecording,
  onClick,
  disabled = false,
  className
}: {
  readonly isRecording: boolean
  readonly onClick: () => void
  readonly disabled?: boolean
  readonly className?: string
}) {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        'flex items-center justify-center w-12 h-12 rounded-full transition-all duration-200',
        'focus:outline-none focus:ring-2 focus:ring-offset-2',
        'disabled:opacity-50 disabled:cursor-not-allowed',
        'active:scale-95 transform',
        isRecording 
          ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500 animate-pulse' 
          : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
        className
      )}
    >
      {isRecording ? (
        <MicOff className="w-5 h-5 text-white" />
      ) : (
        <Mic className="w-5 h-5 text-white" />
      )}
    </button>
  )
}
