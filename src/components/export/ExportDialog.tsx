'use client'

import { useState } from 'react'
import {
  Download,
  FileText,
  File,
  Image,
  Settings,
  Check,
  Loader2
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Modal, ModalContent } from '@/components/ui/Modal'
import { ExportService, type ExportOptions, type ExportData } from '@/lib/export-service'
import { cn } from '@/lib/utils'

export interface ExportDialogProps {
  readonly isOpen: boolean
  readonly onClose: () => void
  readonly data: ExportData
}

export default function ExportDialog({
  isOpen,
  onClose,
  data
}: ExportDialogProps) {
  const [selectedFormat, setSelectedFormat] = useState<ExportOptions['format']>('pdf')
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    includeImages: true,
    includeAudio: false,
    includeMetadata: true,
    template: 'default',
    pageSize: 'A4',
    fontSize: 'medium',
    margins: 'normal',
    coverPage: true,
    tableOfContents: true
  })
  const [isExporting, setIsExporting] = useState(false)
  const [showAdvanced, setShowAdvanced] = useState(false)

  // 格式选项
  const formatOptions = [
    {
      format: 'pdf' as const,
      name: 'PDF文档',
      description: '适合打印和正式分享',
      icon: <FileText className="w-6 h-6" />,
      color: 'text-red-600'
    },
    {
      format: 'html' as const,
      name: 'HTML网页',
      description: '可在浏览器中查看',
      icon: <File className="w-6 h-6" />,
      color: 'text-blue-600'
    },
    {
      format: 'docx' as const,
      name: 'Word文档',
      description: '可在Microsoft Word中编辑',
      icon: <FileText className="w-6 h-6" />,
      color: 'text-blue-700'
    },
    {
      format: 'txt' as const,
      name: '纯文本',
      description: '简单的文本格式',
      icon: <File className="w-6 h-6" />,
      color: 'text-gray-600'
    },
    {
      format: 'markdown' as const,
      name: 'Markdown',
      description: '适合技术文档',
      icon: <File className="w-6 h-6" />,
      color: 'text-green-600'
    }
  ]

  // 处理导出
  const handleExport = async () => {
    setIsExporting(true)
    
    try {
      const result = await ExportService.exportContent(data, {
        ...exportOptions,
        format: selectedFormat
      })
      
      if (result.success && result.data) {
        ExportService.downloadFile(result)
        onClose()
      } else {
        alert(result.error ?? '导出失败')
      }
    } catch (error) {
      alert(error instanceof Error ? error.message : '导出失败')
    } finally {
      setIsExporting(false)
    }
  }

  // 更新导出选项
  const updateOption = <K extends keyof ExportOptions>(
    key: K,
    value: ExportOptions[K]
  ) => {
    setExportOptions(prev => ({ ...prev, [key]: value }))
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="导出内容"
      size="lg"
    >
      <ModalContent>
        <div className="space-y-6 p-6">
          {/* 格式选择 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">选择导出格式</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {formatOptions.map((option) => (
                <button
                  key={option.format}
                  onClick={() => setSelectedFormat(option.format)}
                  className={cn(
                    'p-4 border rounded-lg text-left transition-all duration-200 hover:shadow-md',
                    selectedFormat === option.format
                      ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                      : 'border-gray-200 hover:border-gray-300'
                  )}
                >
                  <div className="flex items-start space-x-3">
                    <div className={option.color}>
                      {option.icon}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">
                        {option.name}
                      </div>
                      <div className="text-sm text-gray-600 mt-1">
                        {option.description}
                      </div>
                    </div>
                    {selectedFormat === option.format && (
                      <Check className="w-5 h-5 text-blue-600" />
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* 基础选项 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">包含内容</h3>
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={exportOptions.includeImages}
                  onChange={(e) => updateOption('includeImages', e.target.checked)}
                  className="mr-3 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                {/* eslint-disable-next-line jsx-a11y/alt-text */}
                <Image className="w-4 h-4 mr-2 text-gray-500" />
                包含图片
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={exportOptions.includeMetadata}
                  onChange={(e) => updateOption('includeMetadata', e.target.checked)}
                  className="mr-3 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <FileText className="w-4 h-4 mr-2 text-gray-500" />
                包含文档信息
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={exportOptions.coverPage}
                  onChange={(e) => updateOption('coverPage', e.target.checked)}
                  className="mr-3 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <File className="w-4 h-4 mr-2 text-gray-500" />
                生成封面页
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={exportOptions.tableOfContents}
                  onChange={(e) => updateOption('tableOfContents', e.target.checked)}
                  className="mr-3 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <FileText className="w-4 h-4 mr-2 text-gray-500" />
                生成目录
              </label>
            </div>
          </div>

          {/* 高级选项 */}
          <div>
            <button
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="flex items-center text-blue-600 hover:text-blue-700 transition-colors"
            >
              <Settings className="w-4 h-4 mr-2" />
              高级选项
              <span className="ml-2 text-sm">
                {showAdvanced ? '收起' : '展开'}
              </span>
            </button>
            
            {showAdvanced && (
              <div className="mt-4 space-y-4 p-4 bg-gray-50 rounded-lg">
                {/* 模板选择 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    文档模板
                  </label>
                  <select
                    value={exportOptions.template}
                    onChange={(e) => updateOption('template', e.target.value as ExportOptions['template'])}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="default">默认</option>
                    <option value="classic">经典</option>
                    <option value="modern">现代</option>
                    <option value="elegant">优雅</option>
                  </select>
                </div>

                {/* 页面设置 */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      页面大小
                    </label>
                    <select
                      value={exportOptions.pageSize}
                      onChange={(e) => updateOption('pageSize', e.target.value as ExportOptions['pageSize'])}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="A4">A4</option>
                      <option value="A5">A5</option>
                      <option value="Letter">Letter</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      字体大小
                    </label>
                    <select
                      value={exportOptions.fontSize}
                      onChange={(e) => updateOption('fontSize', e.target.value as ExportOptions['fontSize'])}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="small">小</option>
                      <option value="medium">中</option>
                      <option value="large">大</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      页边距
                    </label>
                    <select
                      value={exportOptions.margins}
                      onChange={(e) => updateOption('margins', e.target.value as ExportOptions['margins'])}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="narrow">窄</option>
                      <option value="normal">正常</option>
                      <option value="wide">宽</option>
                    </select>
                  </div>
                </div>

                {/* 水印 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    水印文字（可选）
                  </label>
                  <input
                    type="text"
                    value={exportOptions.watermark || ''}
                    onChange={(e) => updateOption('watermark', e.target.value || undefined)}
                    placeholder="输入水印文字"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            )}
          </div>

          {/* 预览信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">导出预览</CardTitle>
            </CardHeader>
            <CardContent className="text-sm text-gray-600">
              <div className="space-y-2">
                <div>格式：{formatOptions.find(f => f.format === selectedFormat)?.name}</div>
                <div>标题：{data.title}</div>
                <div>作者：{data.author}</div>
                {data.metadata && (
                  <>
                    <div>字数：{data.metadata.wordCount}</div>
                    <div>章节：{data.chapters?.length || 0} 个</div>
                    <div>照片：{data.photos?.length || 0} 张</div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isExporting}
            >
              取消
            </Button>
            
            <Button
              onClick={handleExport}
              disabled={isExporting}
              icon={isExporting ? <Loader2 className="w-4 h-4 animate-spin" /> : <Download className="w-4 h-4" />}
            >
              {isExporting ? '导出中...' : '开始导出'}
            </Button>
          </div>
        </div>
      </ModalContent>
    </Modal>
  )
}
