'use client'

import { useState } from 'react'
import Image from 'next/image'
import {
  Share2,
  Link,
  Mail,
  Printer,
  Copy,
  MessageCircle,
  Download,
  Check,
  Loader2
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Modal, ModalContent } from '@/components/ui/Modal'
import { Input } from '@/components/ui/Input'
import { SharingService, type ShareOptions, type ShareData } from '@/lib/sharing-service'
import { cn } from '@/lib/utils'

export interface ShareDialogProps {
  readonly isOpen: boolean
  readonly onClose: () => void
  readonly data: ShareData
  readonly contentId: string
}

export default function ShareDialog({
  isOpen,
  onClose,
  data,
  contentId
}: ShareDialogProps) {
  const [shareOptions, setShareOptions] = useState<ShareOptions>({
    type: 'link',
    privacy: 'private',
    allowComments: false,
    allowDownload: true
  })
  const [shareResult, setShareResult] = useState<{ shareUrl?: string; qrCode?: string } | null>(null)
  const [isSharing, setIsSharing] = useState(false)
  const [copiedUrl, setCopiedUrl] = useState(false)
  const [customMessage, setCustomMessage] = useState('')

  // 分享方式选项
  const shareTypes = [
    {
      type: 'link' as const,
      name: '生成链接',
      description: '创建可分享的链接',
      icon: <Link className="w-5 h-5" />,
      color: 'text-blue-600'
    },
    {
      type: 'email' as const,
      name: '邮件分享',
      description: '通过邮件发送',
      icon: <Mail className="w-5 h-5" />,
      color: 'text-green-600'
    },
    {
      type: 'print' as const,
      name: '打印',
      description: '打印纸质版本',
      icon: <Printer className="w-5 h-5" />,
      color: 'text-gray-600'
    }
  ]

  // 社交平台选项
  const socialPlatforms = [
    { platform: 'wechat', name: '微信', color: 'bg-green-500' },
    { platform: 'weibo', name: '微博', color: 'bg-red-500' },
    { platform: 'qq', name: 'QQ', color: 'bg-blue-500' }
  ]

  // 生成分享链接
  const handleGenerateLink = async () => {
    setIsSharing(true)
    
    try {
      const result = await SharingService.generateShareLink(contentId, shareOptions)
      
      if (result.success) {
        setShareResult({
          shareUrl: result.shareUrl,
          qrCode: result.qrCode
        })
      } else {
        alert(result.error || '生成分享链接失败')
      }
    } catch (error) {
      alert(error instanceof Error ? error.message : '生成分享链接失败')
    } finally {
      setIsSharing(false)
    }
  }

  // 社交媒体分享
  const handleSocialShare = async (platform: string) => {
    const result = await SharingService.shareToSocial(data, platform as NonNullable<ShareOptions['platform']>)
    if (!result.success) {
      alert(result.error || '分享失败')
    }
  }

  // 邮件分享
  const handleEmailShare = () => {
    const result = SharingService.shareByEmail(data, {
      ...shareOptions,
      customMessage
    })
    if (!result.success) {
      alert(result.error || '邮件分享失败')
    }
  }

  // 打印分享
  const handlePrint = () => {
    const result = SharingService.printContent(data)
    if (!result.success) {
      alert(result.error || '打印失败')
    }
  }

  // 复制链接
  const handleCopyLink = async () => {
    if (shareResult?.shareUrl) {
      const success = await SharingService.copyToClipboard(shareResult.shareUrl)
      if (success) {
        setCopiedUrl(true)
        setTimeout(() => setCopiedUrl(false), 2000)
      } else {
        alert('复制失败')
      }
    }
  }

  // 原生分享
  const handleNativeShare = async () => {
    const result = await SharingService.nativeShare(data)
    if (!result.success) {
      alert(result.error || '分享失败')
    }
  }

  // 更新分享选项
  const updateOption = <K extends keyof ShareOptions>(
    key: K,
    value: ShareOptions[K]
  ) => {
    setShareOptions(prev => ({ ...prev, [key]: value }))
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="分享内容"
      size="lg"
    >
      <ModalContent>
        <div className="space-y-6 p-6">
          {/* 分享方式选择 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">选择分享方式</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {shareTypes.map((type) => (
                <button
                  key={type.type}
                  onClick={() => updateOption('type', type.type)}
                  className={cn(
                    'p-4 border rounded-lg text-left transition-all duration-200 hover:shadow-md',
                    shareOptions.type === type.type
                      ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                      : 'border-gray-200 hover:border-gray-300'
                  )}
                >
                  <div className="flex items-start space-x-3">
                    <div className={type.color}>
                      {type.icon}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">
                        {type.name}
                      </div>
                      <div className="text-sm text-gray-600 mt-1">
                        {type.description}
                      </div>
                    </div>
                    {shareOptions.type === type.type && (
                      <Check className="w-5 h-5 text-blue-600" />
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* 链接分享选项 */}
          {shareOptions.type === 'link' && (
            <div className="space-y-4">
              {/* 隐私设置 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  隐私设置
                </label>
                <div className="space-y-2">
                  {(['public', 'private', 'family'] as const).map(privacy => (
                    <label key={privacy} className="flex items-center">
                      <input
                        type="radio"
                        name="privacy"
                        value={privacy}
                        checked={shareOptions.privacy === privacy}
                        onChange={(e) => updateOption('privacy', e.target.value as ShareOptions['privacy'])}
                        className="mr-3 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                      />
                      <span className="text-sm">
                        {privacy === 'public' && '公开 - 任何人都可以访问'}
                        {privacy === 'private' && '私密 - 仅限有链接的人访问'}
                        {privacy === 'family' && '家庭 - 仅限家庭成员访问'}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              {/* 访问控制 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    过期时间
                  </label>
                  <input
                    type="datetime-local"
                    value={shareOptions.expiresAt || ''}
                    onChange={(e) => updateOption('expiresAt', e.target.value || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    访问密码（可选）
                  </label>
                  <input
                    type="password"
                    value={shareOptions.password || ''}
                    onChange={(e) => updateOption('password', e.target.value || undefined)}
                    placeholder="设置访问密码"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* 权限设置 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  访问权限
                </label>
                <div className="space-y-3">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={shareOptions.allowComments}
                      onChange={(e) => updateOption('allowComments', e.target.checked)}
                      className="mr-3 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <MessageCircle className="w-4 h-4 mr-2 text-gray-500" />
                    允许评论
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={shareOptions.allowDownload}
                      onChange={(e) => updateOption('allowDownload', e.target.checked)}
                      className="mr-3 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <Download className="w-4 h-4 mr-2 text-gray-500" />
                    允许下载
                  </label>
                </div>
              </div>

              {/* 生成链接按钮 */}
              <Button
                onClick={handleGenerateLink}
                disabled={isSharing}
                className="w-full"
                icon={isSharing ? <Loader2 className="w-4 h-4 animate-spin" /> : <Link className="w-4 h-4" />}
              >
                {isSharing ? '生成中...' : '生成分享链接'}
              </Button>

              {/* 分享结果 */}
              {shareResult && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">分享链接已生成</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* 链接 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        分享链接
                      </label>
                      <div className="flex items-center space-x-2">
                        <Input
                          value={shareResult.shareUrl}
                          readOnly
                          className="flex-1"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleCopyLink}
                          icon={copiedUrl ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                        >
                          {copiedUrl ? '已复制' : '复制'}
                        </Button>
                      </div>
                    </div>

                    {/* 二维码 */}
                    {shareResult.qrCode && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          二维码
                        </label>
                        <div className="flex justify-center">
                          <Image
                            src={shareResult.qrCode}
                            alt="分享二维码"
                            width={128}
                            height={128}
                            className="w-32 h-32 border border-gray-200 rounded"
                          />
                        </div>
                      </div>
                    )}

                    {/* 社交媒体分享 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        快速分享到
                      </label>
                      <div className="flex space-x-2">
                        {socialPlatforms.map(platform => (
                          <button
                            key={platform.platform}
                            onClick={() => handleSocialShare(platform.platform)}
                            className={cn(
                              'px-3 py-2 text-white text-sm rounded transition-colors',
                              platform.color,
                              'hover:opacity-80'
                            )}
                          >
                            {platform.name}
                          </button>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {/* 邮件分享选项 */}
          {shareOptions.type === 'email' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  自定义消息
                </label>
                <textarea
                  value={customMessage}
                  onChange={(e) => setCustomMessage(e.target.value)}
                  placeholder="添加一些个人消息..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <Button
                onClick={handleEmailShare}
                className="w-full"
                icon={<Mail className="w-4 h-4" />}
              >
                打开邮件客户端
              </Button>
            </div>
          )}

          {/* 打印选项 */}
          {shareOptions.type === 'print' && (
            <div className="space-y-4">
              <div className="text-sm text-gray-600">
                <p>点击下方按钮将打开打印预览窗口，您可以：</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>选择打印机进行打印</li>
                  <li>保存为PDF文件</li>
                  <li>调整打印设置</li>
                </ul>
              </div>
              
              <Button
                onClick={handlePrint}
                className="w-full"
                icon={<Printer className="w-4 h-4" />}
              >
                打开打印预览
              </Button>
            </div>
          )}

          {/* 原生分享（移动设备） */}
          {typeof navigator !== 'undefined' && 'share' in navigator && (
            <div className="border-t border-gray-200 pt-4">
              <Button
                onClick={handleNativeShare}
                variant="outline"
                className="w-full"
                icon={<Share2 className="w-4 h-4" />}
              >
                使用系统分享
              </Button>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={onClose}
            >
              关闭
            </Button>
          </div>
        </div>
      </ModalContent>
    </Modal>
  )
}
