'use client'

import { useState } from 'react'
import { 
  Download, 
  Share2, 
  FileText, 
  Link, 
  Mail, 
  Printer,
  BarChart3,
  Clock,
  Users
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import ExportDialog from './ExportDialog'
import ShareDialog from './ShareDialog'
import { type ExportData } from '@/lib/export-service'
import { type ShareData } from '@/lib/sharing-service'
import { cn } from '@/lib/utils'

export interface ExportSharePanelProps {
  readonly data: ExportData & ShareData
  readonly contentId: string
  readonly className?: string
}

export default function ExportSharePanel({
  data,
  contentId,
  className
}: ExportSharePanelProps) {
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [showShareDialog, setShowShareDialog] = useState(false)

  // 快速操作选项
  const quickActions = [
    {
      action: 'export-pdf',
      name: '导出PDF',
      description: '生成PDF文档',
      icon: <FileText className="w-5 h-5" />,
      color: 'text-red-600 bg-red-50 hover:bg-red-100',
      onClick: () => setShowExportDialog(true)
    },
    {
      action: 'share-link',
      name: '分享链接',
      description: '生成分享链接',
      icon: <Link className="w-5 h-5" />,
      color: 'text-blue-600 bg-blue-50 hover:bg-blue-100',
      onClick: () => setShowShareDialog(true)
    },
    {
      action: 'email',
      name: '邮件发送',
      description: '通过邮件分享',
      icon: <Mail className="w-5 h-5" />,
      color: 'text-green-600 bg-green-50 hover:bg-green-100',
      onClick: () => setShowShareDialog(true)
    },
    {
      action: 'print',
      name: '打印',
      description: '打印纸质版本',
      icon: <Printer className="w-5 h-5" />,
      color: 'text-gray-600 bg-gray-50 hover:bg-gray-100',
      onClick: () => window.print()
    }
  ]

  // 统计信息
  const stats = [
    {
      label: '总字数',
      value: data.metadata?.wordCount || 0,
      icon: <FileText className="w-4 h-4" />,
      color: 'text-blue-600'
    },
    {
      label: '阅读时间',
      value: `${data.metadata?.readingTime || 0} 分钟`,
      icon: <Clock className="w-4 h-4" />,
      color: 'text-green-600'
    },
    {
      label: '章节数',
      value: data.chapters?.length || 0,
      icon: <BarChart3 className="w-4 h-4" />,
      color: 'text-purple-600'
    },
    {
      label: '照片数',
      value: data.photos?.length || 0,
      icon: <Users className="w-4 h-4" />,
      color: 'text-orange-600'
    }
  ]

  return (
    <div className={cn('space-y-6', className)}>
      {/* 内容概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="w-5 h-5 text-blue-600" />
            <span>内容概览</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* 基本信息 */}
            <div>
              <h3 className="text-lg font-medium text-gray-900">{data.title}</h3>
              <p className="text-sm text-gray-600">作者：{data.author}</p>
              {data.metadata?.createdAt && (
                <p className="text-sm text-gray-600">
                  创建时间：{new Date(data.metadata.createdAt).toLocaleString('zh-CN')}
                </p>
              )}
            </div>

            {/* 统计信息 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {stats.map((stat, index) => (
                <div key={index} className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className={cn('flex items-center justify-center mb-2', stat.color)}>
                    {stat.icon}
                  </div>
                  <div className="text-lg font-semibold text-gray-900">{stat.value}</div>
                  <div className="text-xs text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>

            {/* 内容预览 */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">内容预览</h4>
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600 line-clamp-3">
                  {data.content.substring(0, 200)}
                  {data.content.length > 200 && '...'}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 快速操作 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Share2 className="w-5 h-5 text-green-600" />
              <span>导出与分享</span>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {quickActions.map((action) => (
              <button
                key={action.action}
                onClick={action.onClick}
                className={cn(
                  'p-4 border border-gray-200 rounded-lg text-left transition-all duration-200 hover:shadow-md',
                  action.color
                )}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    {action.icon}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">
                      {action.name}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      {action.description}
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 主要操作按钮 */}
      <div className="flex flex-col sm:flex-row gap-3">
        <Button
          onClick={() => setShowExportDialog(true)}
          className="flex-1"
          size="lg"
          icon={<Download className="w-5 h-5" />}
        >
          导出文档
        </Button>
        
        <Button
          onClick={() => setShowShareDialog(true)}
          variant="outline"
          className="flex-1"
          size="lg"
          icon={<Share2 className="w-5 h-5" />}
        >
          分享内容
        </Button>
      </div>

      {/* 使用提示 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">使用提示</CardTitle>
        </CardHeader>
        <CardContent className="text-sm text-gray-600">
          <div className="space-y-2">
            <div className="flex items-start space-x-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
              <p><strong>PDF导出：</strong>适合打印和正式分享，保持格式完整</p>
            </div>
            <div className="flex items-start space-x-2">
              <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
              <p><strong>分享链接：</strong>可设置访问权限和过期时间，便于在线查看</p>
            </div>
            <div className="flex items-start space-x-2">
              <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
              <p><strong>邮件分享：</strong>直接通过邮件客户端发送给指定联系人</p>
            </div>
            <div className="flex items-start space-x-2">
              <div className="w-1.5 h-1.5 bg-gray-500 rounded-full mt-2 flex-shrink-0"></div>
              <p><strong>打印功能：</strong>可选择打印机或保存为PDF文件</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 对话框 */}
      <ExportDialog
        isOpen={showExportDialog}
        onClose={() => setShowExportDialog(false)}
        data={data}
      />
      
      <ShareDialog
        isOpen={showShareDialog}
        onClose={() => setShowShareDialog(false)}
        data={data}
        contentId={contentId}
      />
    </div>
  )
}
