'use client'

import { useState, useEffect } from 'react'
import { User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import Link from 'next/link'
import {
  BookOpen,
  Camera,
  Settings,
  LogOut,
  Plus,
  Heart,
  Clock,
  Star,
  Mic,
  X
} from 'lucide-react'
import GuestStatusBar from '@/components/guest/GuestStatusBar'
import GuestUpgradeModal from '@/components/guest/GuestUpgradeModal'
import { GuestAuthService, type GuestUser } from '@/lib/guest-auth'
import TemplateSelector from '@/components/memoir/TemplateSelector'
import { MemoirTemplate } from '@/lib/memoir-templates'

interface WelcomeScreenProps {
  readonly user: User
  readonly onCreateMemoir: () => void
}

export default function WelcomeScreen({ user, onCreateMemoir }: WelcomeScreenProps) {
  const [loading, setLoading] = useState(false)
  const [isGuest, setIsGuest] = useState(false)
  const [guestUser, setGuestUser] = useState<GuestUser | null>(null)
  const [showUpgradeModal, setShowUpgradeModal] = useState(false)
  const [showTemplateSelector, setShowTemplateSelector] = useState(false)

  useEffect(() => {
    // 检查是否为访客用户
    const checkGuestStatus = async () => {
      if (user.user_metadata?.auth_method === 'guest') {
        setIsGuest(true)
        const currentGuest = GuestAuthService.getCurrentGuestUser()
        setGuestUser(currentGuest)
      }
    }

    checkGuestStatus()
  }, [user])

  const handleSignOut = async () => {
    setLoading(true)
    await supabase.auth.signOut()
    setLoading(false)
  }

  const handleUpgrade = () => {
    setShowUpgradeModal(true)
  }

  const handleUpgradeSuccess = () => {
    setShowUpgradeModal(false)
    // 页面会自动刷新到新用户会话
  }

  const handleCreateMemoir = () => {
    setShowTemplateSelector(true)
  }

  const handleSelectTemplate = (template: MemoirTemplate) => {
    setShowTemplateSelector(false)
    // Store template in localStorage and navigate to memoir creation
    localStorage.setItem('selected_template', JSON.stringify(template))
    // Navigate directly to memoir creation page
    window.location.href = `/memoir/create?template=${template.id}`
  }

  const userName = user.user_metadata?.full_name ??
                   user.user_metadata?.wechat_nickname ??
                   user.email?.split('@')[0] ??
                   (isGuest ? GuestAuthService.getGuestDisplayName(guestUser!) : '朋友')

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Guest Status Bar */}
      {isGuest && (
        <GuestStatusBar
          onUpgrade={handleUpgrade}
        />
      )}

      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
              <Heart className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">我的回忆录</h1>
              <p className="text-sm text-gray-600">
                欢迎回来，{userName}
                {isGuest && <span className="text-orange-600 ml-1">(访客模式)</span>}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {isGuest && (
              <button
                onClick={handleUpgrade}
                className="flex items-center space-x-1 px-3 py-1.5 bg-orange-600 text-white hover:bg-orange-700 rounded-lg transition-colors text-sm font-medium"
              >
                <Plus className="w-4 h-4" />
                <span>绑定</span>
              </button>
            )}
            <button
              onClick={handleSignOut}
              disabled={loading}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <LogOut className="w-5 h-5" />
            </button>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 py-8">
        {/* Welcome Message */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            开始记录您的人生故事
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            用语音轻松记录珍贵回忆，AI帮您整理成精美的回忆录。
            每一个故事都值得被记录，每一段回忆都值得被珍藏。
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          <button
            onClick={handleCreateMemoir}
            className="group bg-gradient-to-r from-blue-600 to-purple-600 text-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          >
            <div className="flex items-center justify-center mb-4">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center group-hover:bg-white/30 transition-colors">
                <Plus className="w-8 h-8" />
              </div>
            </div>
            <h3 className="text-2xl font-bold mb-2">创建新回忆录</h3>
            <p className="text-blue-100">选择模板，开始记录您的人生故事</p>
          </button>

          <button className="group bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-gray-200">
            <div className="flex items-center justify-center mb-4">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center">
                <Camera className="w-8 h-8 text-white" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">创建相册</h3>
            <p className="text-gray-600">上传照片，添加语音解说</p>
          </button>

          <Link
            href="/demo"
            className="group bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-gray-200 block"
          >
            <div className="flex items-center justify-center mb-4">
              <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center">
                <Mic className="w-8 h-8 text-white" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">语音演示</h3>
            <p className="text-gray-600">体验语音转文字功能</p>
          </Link>
        </div>

        {/* Recent Projects */}
        <div className="mb-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <Clock className="w-6 h-6 mr-2" />
            最近的项目
          </h3>
          <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <BookOpen className="w-8 h-8 text-gray-400" />
            </div>
            <h4 className="text-lg font-medium text-gray-900 mb-2">还没有项目</h4>
            <p className="text-gray-600 mb-6">创建您的第一个回忆录，开始记录珍贵的人生故事</p>
            <button
              onClick={onCreateMemoir}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              立即开始
            </button>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <div className="bg-white p-6 rounded-xl shadow-lg">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
              <BookOpen className="w-6 h-6 text-blue-600" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">多种模板</h4>
            <p className="text-gray-600">人生历程、家族传承、职业传奇等多种模板可选</p>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-lg">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
              <Star className="w-6 h-6 text-green-600" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">AI智能整理</h4>
            <p className="text-gray-600">语音转文字，智能整理成连贯的回忆录内容</p>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-lg">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
              <Settings className="w-6 h-6 text-purple-600" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">简单易用</h4>
            <p className="text-gray-600">专为中老年用户设计，大按钮，清晰界面</p>
          </div>
        </div>

        {/* Tips */}
        <div className="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-2xl p-6">
          <h4 className="text-lg font-semibold text-amber-900 mb-3">💡 使用小贴士</h4>
          <ul className="space-y-2 text-amber-800">
            <li>• 找一个安静的环境进行语音录制</li>
            <li>• 可以多次录制同一个话题，AI会帮您整理</li>
            <li>• 每次录制建议控制在2-3分钟内</li>
            <li>• 说话时语速适中，吐字清晰</li>
          </ul>
        </div>
      </main>

      {/* Guest Upgrade Modal */}
      {isGuest && guestUser && (
        <GuestUpgradeModal
          isOpen={showUpgradeModal}
          onClose={() => setShowUpgradeModal(false)}
          guestUserId={guestUser.id}
          onSuccess={handleUpgradeSuccess}
        />
      )}

      {/* Template Selector Modal */}
      {showTemplateSelector && (
        <div className="fixed inset-0 bg-white z-50 overflow-y-auto">
          <div className="min-h-screen">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h1 className="text-2xl font-bold text-gray-900">选择回忆录模板</h1>
              <button
                onClick={() => setShowTemplateSelector(false)}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            <TemplateSelector
              onSelectTemplate={handleSelectTemplate}
            />
          </div>
        </div>
      )}
    </div>
  )
}
