'use client'

import { useState, useEffect } from 'react'
import { User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import {
  BookOpen,
  Camera,
  Settings,
  LogOut,
  Plus,
  Heart,
  Clock,
  Star,
  X
} from 'lucide-react'
import GuestStatusBar from '@/components/guest/GuestStatusBar'
import GuestUpgradeModal from '@/components/guest/GuestUpgradeModal'
import { GuestAuthService, type GuestUser } from '@/lib/guest-auth'
import TemplateSelector from '@/components/memoir/TemplateSelector'
import { MemoirTemplate } from '@/lib/memoir-templates'
import { ProjectManager } from '@/lib/project-manager'
import { AlbumManager } from '@/lib/album-manager'

interface WelcomeScreenProps {
  readonly user: User
}

// 统一的项目显示类型
interface UnifiedProject {
  id: string
  title: string
  type: 'memoir' | 'album'
  templateName?: string
  lastModified: Date
  progress: number
  status: 'draft' | 'in_progress' | 'completed' | 'published' | 'archived'
  totalItems: number // 对于回忆录是章节数，对于相册是照片数
  description?: string
}

export default function WelcomeScreen({ user }: WelcomeScreenProps) {
  const [loading, setLoading] = useState(false)
  const [isGuest, setIsGuest] = useState(false)
  const [guestUser, setGuestUser] = useState<GuestUser | null>(null)
  const [showUpgradeModal, setShowUpgradeModal] = useState(false)
  const [showTemplateSelector, setShowTemplateSelector] = useState(false)
  const [recentProjects, setRecentProjects] = useState<UnifiedProject[]>([])
  const [projectsLoading, setProjectsLoading] = useState(true)

  useEffect(() => {
    // 检查是否为访客用户
    const checkGuestStatus = async () => {
      if (user.user_metadata?.auth_method === 'guest') {
        setIsGuest(true)
        const currentGuest = GuestAuthService.getCurrentGuestUser()
        setGuestUser(currentGuest)
      }
    }

    checkGuestStatus()
  }, [user])

  // 加载项目数据（合并回忆录和相册）
  useEffect(() => {
    const loadProjects = () => {
      try {
        // 获取回忆录项目
        const memoirProjects = ProjectManager.getProjectSummaries().map(project => ({
          id: project.id,
          title: project.title,
          type: 'memoir' as const,
          templateName: project.templateName,
          lastModified: project.lastModified,
          progress: project.progress,
          status: project.status,
          totalItems: 0, // 这里可以从项目数据中获取章节数
          description: `基于 ${project.templateName} 模板`
        }))

        // 获取相册项目
        const albumProjects = AlbumManager.getAlbumSummaries().map(album => ({
          id: album.id,
          title: album.title,
          type: 'album' as const,
          templateName: undefined,
          lastModified: album.lastModified,
          progress: album.totalPhotos > 0 ? 100 : 0, // 相册没有进度概念，有照片就算完成
          status: album.status,
          totalItems: album.totalPhotos,
          description: album.description || `包含 ${album.totalPhotos} 张照片`
        }))

        // 合并并按最后修改时间排序
        const allProjects = [...memoirProjects, ...albumProjects]
          .sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime())
          .slice(0, 6) // 只显示最近的6个项目

        setRecentProjects(allProjects)
      } catch (error) {
        console.error('Failed to load projects:', error)
      } finally {
        setProjectsLoading(false)
      }
    }

    loadProjects()

    // 监听localStorage变化，实时更新项目列表
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'memoir_projects' || e.key === 'memoir_albums') {
        loadProjects()
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  const handleSignOut = async () => {
    setLoading(true)

    try {
      // 如果是访客用户，清理访客数据
      if (isGuest) {
        // 使用专门的访客会话清理方法
        GuestAuthService.clearGuestSession()

        // 直接重新加载页面回到登录界面
        window.location.href = '/'
      } else {
        // 正常用户登出
        await supabase.auth.signOut()
      }
    } catch (error) {
      console.error('登出失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleUpgrade = () => {
    setShowUpgradeModal(true)
  }

  const handleUpgradeSuccess = () => {
    setShowUpgradeModal(false)
    // 页面会自动刷新到新用户会话
  }

  const handleCreateMemoir = () => {
    setShowTemplateSelector(true)
  }

  const handleSelectTemplate = (template: MemoirTemplate) => {
    setShowTemplateSelector(false)

    // 创建新项目记录
    const project = ProjectManager.createProject(
      `我的${template.name}`,
      template.id,
      template.name,
      template.chapters.length
    )

    // Store template and project info in localStorage
    localStorage.setItem('selected_template', JSON.stringify(template))
    localStorage.setItem('current_project_id', project.id)

    // 更新项目列表（重新加载所有项目）
    setTimeout(() => {
      // 获取回忆录项目
      const memoirProjects = ProjectManager.getProjectSummaries().map(project => ({
        id: project.id,
        title: project.title,
        type: 'memoir' as const,
        templateName: project.templateName,
        lastModified: project.lastModified,
        progress: project.progress,
        status: project.status,
        totalItems: 0,
        description: `基于 ${project.templateName} 模板`
      }))

      // 获取相册项目
      const albumProjects = AlbumManager.getAlbumSummaries().map(album => ({
        id: album.id,
        title: album.title,
        type: 'album' as const,
        templateName: undefined,
        lastModified: album.lastModified,
        progress: album.totalPhotos > 0 ? 100 : 0,
        status: album.status,
        totalItems: album.totalPhotos,
        description: album.description || `包含 ${album.totalPhotos} 张照片`
      }))

      // 合并并按最后修改时间排序
      const allProjects = [...memoirProjects, ...albumProjects]
        .sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime())
        .slice(0, 6)

      setRecentProjects(allProjects)
    }, 100) // 短暂延迟确保数据已保存

    // Navigate to memoir creation page
    window.location.href = `/memoir/create?template=${template.id}&project=${project.id}`
  }

  const userName = user.user_metadata?.full_name ??
                   user.user_metadata?.wechat_nickname ??
                   user.email?.split('@')[0] ??
                   (isGuest ? GuestAuthService.getGuestDisplayName(guestUser!) : '朋友')

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Guest Status Bar */}
      {isGuest && (
        <GuestStatusBar
          onUpgrade={handleUpgrade}
        />
      )}

      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
              <Heart className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">我的回忆录</h1>
              <p className="text-sm text-gray-600">
                欢迎回来，{userName}
                {isGuest && <span className="text-orange-600 ml-1">(访客模式)</span>}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {isGuest && (
              <button
                onClick={handleUpgrade}
                className="flex items-center space-x-1 px-3 py-1.5 bg-orange-600 text-white hover:bg-orange-700 rounded-lg transition-colors text-sm font-medium"
              >
                <Plus className="w-4 h-4" />
                <span>绑定</span>
              </button>
            )}
            <button
              onClick={handleSignOut}
              disabled={loading}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <LogOut className="w-5 h-5" />
            </button>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 py-8">
        {/* Welcome Message */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            开始记录您的人生故事
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            用语音轻松记录珍贵回忆，AI帮您整理成精美的回忆录。
            每一个故事都值得被记录，每一段回忆都值得被珍藏。
          </p>
        </div>

        {/* Quick Actions - Mobile-First Optimized */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 mb-12 max-w-4xl mx-auto">
          <button
            onClick={handleCreateMemoir}
            className="group bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 sm:p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 touch-target focus-enhanced"
          >
            <div className="flex items-center justify-center mb-4">
              <div className="w-14 h-14 sm:w-16 sm:h-16 bg-white/20 rounded-full flex items-center justify-center group-hover:bg-white/30 transition-colors">
                <Plus className="w-7 h-7 sm:w-8 sm:h-8" />
              </div>
            </div>
            <h3 className="text-xl sm:text-2xl font-bold mb-2">创建新回忆录</h3>
            <p className="text-blue-100 text-sm sm:text-base">选择模板，开始记录您的人生故事</p>
          </button>

          <button
            onClick={() => window.location.href = '/album/create'}
            className="group bg-white p-6 sm:p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-gray-200 touch-target focus-enhanced"
          >
            <div className="flex items-center justify-center mb-4">
              <div className="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center">
                <Camera className="w-7 h-7 sm:w-8 sm:h-8 text-white" />
              </div>
            </div>
            <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">创建相册</h3>
            <p className="text-gray-600 text-sm sm:text-base">上传照片，添加语音解说</p>
          </button>
        </div>

        {/* Recent Projects */}
        <div className="mb-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <Clock className="w-6 h-6 mr-2" />
            最近的项目
          </h3>

          {projectsLoading ? (
            <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">加载项目中...</p>
            </div>
          ) : recentProjects.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recentProjects.map((project) => (
                <div key={project.id} className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
                  <div className="flex items-start justify-between mb-4">
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                      project.type === 'memoir'
                        ? 'bg-gradient-to-r from-blue-600 to-purple-600'
                        : 'bg-gradient-to-r from-green-500 to-teal-500'
                    }`}>
                      {project.type === 'memoir' ? (
                        <BookOpen className="w-6 h-6 text-white" />
                      ) : (
                        <Camera className="w-6 h-6 text-white" />
                      )}
                    </div>
                    <div className="flex flex-col items-end space-y-1">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        project.status === 'completed' || project.status === 'published' ? 'bg-green-100 text-green-800' :
                        project.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {project.status === 'completed' || project.status === 'published' ? '已完成' :
                         project.status === 'in_progress' ? '进行中' : '草稿'}
                      </span>
                      <span className="text-xs text-gray-500">
                        {project.type === 'memoir' ? '回忆录' : '相册'}
                      </span>
                    </div>
                  </div>

                  <h4 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                    {project.title}
                  </h4>
                  <p className="text-sm text-gray-600 mb-3">
                    {project.description}
                  </p>

                  {/* Progress Bar */}
                  <div className="mb-4">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>进度</span>
                      <span>{project.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${project.progress}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">
                      {project.lastModified.toLocaleDateString()}
                    </span>
                    <button
                      onClick={() => {
                        if (project.type === 'memoir') {
                          localStorage.setItem('current_project_id', project.id)
                          window.location.href = `/memoir/create?project=${project.id}`
                        } else {
                          // 相册暂时跳转到相册创建页面，后续可以创建相册编辑页面
                          window.location.href = `/album/create?album=${project.id}`
                        }
                      }}
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      {project.type === 'memoir' ? '继续编辑' : '查看相册'}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <BookOpen className="w-8 h-8 text-gray-400" />
              </div>
              <h4 className="text-lg font-medium text-gray-900 mb-2">还没有项目</h4>
              <p className="text-gray-600 mb-6">创建您的第一个回忆录，开始记录珍贵的人生故事</p>
              <button
                onClick={handleCreateMemoir}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                立即开始
              </button>
            </div>
          )}
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <div className="bg-white p-6 rounded-xl shadow-lg">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
              <BookOpen className="w-6 h-6 text-blue-600" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">多种模板</h4>
            <p className="text-gray-600">人生历程、家族传承、职业传奇等多种模板可选</p>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-lg">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
              <Star className="w-6 h-6 text-green-600" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">AI智能整理</h4>
            <p className="text-gray-600">语音转文字，智能整理成连贯的回忆录内容</p>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-lg">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
              <Settings className="w-6 h-6 text-purple-600" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">简单易用</h4>
            <p className="text-gray-600">专为中老年用户设计，大按钮，清晰界面</p>
          </div>
        </div>


      </main>

      {/* Company Information Footer */}
      <footer className="bg-white border-t border-gray-200 py-6 mt-12">
        <div className="max-w-4xl mx-auto px-4">
          <div className="text-center space-y-2">
            <p className="text-sm text-gray-600">
              <span className="font-medium">北京暮忆留声文化有限公司</span>
            </p>
            <p className="text-sm text-gray-500">
              联系我们：{' '}
              <a
                href="tel:15810088428"
                className="text-blue-600 hover:text-blue-800 font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
                aria-label="拨打电话给王先生"
              >
                王先生 15810088428
              </a>
            </p>
            <p className="text-xs text-gray-400 mt-3">
              专为中老年用户设计的回忆录创作平台
            </p>
          </div>
        </div>
      </footer>

      {/* Guest Upgrade Modal */}
      {isGuest && guestUser && (
        <GuestUpgradeModal
          isOpen={showUpgradeModal}
          onClose={() => setShowUpgradeModal(false)}
          guestUserId={guestUser.id}
          onSuccess={handleUpgradeSuccess}
        />
      )}

      {/* Template Selector Modal */}
      {showTemplateSelector && (
        <div className="fixed inset-0 bg-white z-50 overflow-y-auto">
          <div className="min-h-screen">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h1 className="text-2xl font-bold text-gray-900">选择回忆录模板</h1>
              <button
                onClick={() => setShowTemplateSelector(false)}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            <TemplateSelector
              onSelectTemplate={handleSelectTemplate}
            />
          </div>
        </div>
      )}
    </div>
  )
}
