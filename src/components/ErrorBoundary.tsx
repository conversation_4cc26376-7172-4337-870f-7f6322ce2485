'use client'

import React, { Component, ReactNode } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, Bug } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'

interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
  errorId: string
}

interface ErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
}

export default class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo
    })

    // 调用错误回调
    this.props.onError?.(error, errorInfo)

    // 记录错误到控制台
    console.group('🚨 Error Boundary Caught Error')
    console.error('Error:', error)
    console.error('Error Info:', errorInfo)
    console.error('Component Stack:', errorInfo.componentStack)
    console.groupEnd()

    // 发送错误报告（在实际应用中）
    this.reportError(error, errorInfo)
  }

  // 报告错误
  private readonly reportError = (error: Error, errorInfo: React.ErrorInfo) => {
    const errorReport = {
      errorId: this.state.errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: 'guest', // 在实际应用中从认证状态获取
      sessionId: sessionStorage.getItem('sessionId') ?? 'unknown'
    }

    // 在实际应用中，这里会发送到错误监控服务
    console.log('Error Report:', errorReport)
    
    // 保存到本地存储用于调试
    try {
      const existingErrors = JSON.parse(localStorage.getItem('errorReports') ?? '[]')
      existingErrors.push(errorReport)
      // 只保留最近的10个错误
      const recentErrors = existingErrors.slice(-10)
      localStorage.setItem('errorReports', JSON.stringify(recentErrors))
    } catch (e) {
      console.error('Failed to save error report:', e)
    }
  }

  // 重试
  private readonly handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    })
  }

  // 返回首页
  private readonly handleGoHome = () => {
    window.location.href = '/'
  }

  // 复制错误信息
  private readonly handleCopyError = async () => {
    if (!this.state.error) return

    const errorText = `
错误ID: ${this.state.errorId}
错误信息: ${this.state.error.message}
发生时间: ${new Date().toLocaleString('zh-CN')}
页面地址: ${window.location.href}
用户代理: ${navigator.userAgent}

错误堆栈:
${this.state.error.stack}

组件堆栈:
${this.state.errorInfo?.componentStack}
    `.trim()

    try {
      await navigator.clipboard.writeText(errorText)
      alert('错误信息已复制到剪贴板')
    } catch (e) {
      console.error('Failed to copy error info:', e)
    }
  }

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback
      }

      // 默认错误界面
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-2xl">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3 text-red-600">
                <AlertTriangle className="w-6 h-6" />
                <span>应用遇到了问题</span>
              </CardTitle>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {/* 错误描述 */}
              <div className="text-center">
                <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Bug className="w-10 h-10 text-red-600" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  很抱歉，出现了意外错误
                </h2>
                <p className="text-gray-600 mb-4">
                  我们已经记录了这个问题，开发团队会尽快修复。
                  您可以尝试刷新页面或返回首页。
                </p>
              </div>

              {/* 错误详情 */}
              <div className="bg-gray-100 rounded-lg p-4">
                <div className="text-sm text-gray-700 mb-2">
                  <strong>错误ID:</strong> {this.state.errorId}
                </div>
                <div className="text-sm text-gray-700 mb-2">
                  <strong>错误信息:</strong> {this.state.error?.message}
                </div>
                <div className="text-sm text-gray-700">
                  <strong>发生时间:</strong> {new Date().toLocaleString('zh-CN')}
                </div>
              </div>

              {/* 技术详情（开发模式） */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <summary className="cursor-pointer text-sm font-medium text-red-800 mb-2">
                    技术详情（开发模式）
                  </summary>
                  <div className="text-xs text-red-700 space-y-2">
                    <div>
                      <strong>错误堆栈:</strong>
                      <pre className="mt-1 whitespace-pre-wrap bg-red-100 p-2 rounded text-xs overflow-auto">
                        {this.state.error.stack}
                      </pre>
                    </div>
                    {this.state.errorInfo?.componentStack && (
                      <div>
                        <strong>组件堆栈:</strong>
                        <pre className="mt-1 whitespace-pre-wrap bg-red-100 p-2 rounded text-xs overflow-auto">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </details>
              )}

              {/* 操作按钮 */}
              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={this.handleRetry}
                  className="flex-1"
                  icon={<RefreshCw className="w-4 h-4" />}
                >
                  重试
                </Button>
                
                <Button
                  onClick={this.handleGoHome}
                  variant="outline"
                  className="flex-1"
                  icon={<Home className="w-4 h-4" />}
                >
                  返回首页
                </Button>
                
                <Button
                  onClick={this.handleCopyError}
                  variant="outline"
                  className="flex-1"
                  icon={<Bug className="w-4 h-4" />}
                >
                  复制错误信息
                </Button>
              </div>

              {/* 用户反馈 */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="text-sm font-medium text-blue-800 mb-2">
                  帮助我们改进
                </h3>
                <p className="text-sm text-blue-700 mb-3">
                  如果这个问题持续出现，请联系我们的技术支持团队。
                  提供错误ID可以帮助我们更快地定位和解决问题。
                </p>
                <div className="text-xs text-blue-600">
                  <div>技术支持邮箱: <EMAIL></div>
                  <div>错误ID: {this.state.errorId}</div>
                </div>
              </div>

              {/* 预防措施建议 */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h3 className="text-sm font-medium text-yellow-800 mb-2">
                  预防措施
                </h3>
                <ul className="text-sm text-yellow-700 space-y-1">
                  <li>• 确保网络连接稳定</li>
                  <li>• 尝试清除浏览器缓存</li>
                  <li>• 使用最新版本的浏览器</li>
                  <li>• 避免同时打开过多标签页</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}

// 高阶组件：为组件添加错误边界
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

// 错误报告工具
export class ErrorReporter {
  static reportError(error: Error, context?: string): void {
    const errorReport = {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    console.error('Manual Error Report:', errorReport)
    
    // 在实际应用中发送到错误监控服务
    // 例如: Sentry, LogRocket, Bugsnag 等
  }

  static getStoredErrors(): unknown[] {
    try {
      return JSON.parse(localStorage.getItem('errorReports') || '[]')
    } catch {
      return []
    }
  }

  static clearStoredErrors(): void {
    localStorage.removeItem('errorReports')
  }
}
