'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft, ChevronRight } from 'lucide-react'
import { getAllMemoirTemplates, MemoirTemplate } from '@/lib/memoir-templates'

interface TemplateSelectionProps {
  onBack: () => void
}

export default function TemplateSelection({ onBack }: Readonly<TemplateSelectionProps>) {
  const router = useRouter()
  const [selectedTemplate, setSelectedTemplate] = useState<MemoirTemplate | null>(null)
  const templates = getAllMemoirTemplates()

  const handleTemplateSelect = (template: MemoirTemplate) => {
    setSelectedTemplate(template)
  }

  const handleStartMemoir = () => {
    if (selectedTemplate) {
      // Store template in localStorage for the memoir creation page
      localStorage.setItem('selected_template', JSON.stringify(selectedTemplate))
      // Navigate to memoir creation page
      router.push(`/memoir/create?template=${selectedTemplate.id}`)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-4 flex items-center">
          <button
            onClick={onBack}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors mr-4"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-xl font-bold text-gray-900">选择回忆录模板</h1>
            <p className="text-sm text-gray-600">选择最适合您的模板开始创作</p>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 py-8">
        {/* Template Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {templates.map((template) => (
            <button
              key={template.id}
              onClick={() => handleTemplateSelect(template)}
              className={`bg-white rounded-2xl shadow-lg p-6 cursor-pointer transition-all duration-300 transform hover:scale-105 hover:shadow-xl border-2 text-left w-full ${
                selectedTemplate?.id === template.id
                  ? 'border-blue-500 ring-2 ring-blue-200'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="text-4xl mb-2">{template.icon}</div>
                {selectedTemplate?.id === template.id && (
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                )}
              </div>
              
              <h3 className="text-xl font-bold text-gray-900 mb-2">{template.name}</h3>
              <p className="text-gray-600 mb-4">{template.description}</p>
              
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-700">包含章节：</h4>
                <div className="space-y-1">
                  {template.chapters.slice(0, 3).map((chapter, index) => (
                    <div key={`${template.id}-chapter-${index}-${chapter.title}`} className="flex items-center text-sm text-gray-600">
                      <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-2"></div>
                      {chapter.title}
                    </div>
                  ))}
                  {template.chapters.length > 3 && (
                    <div className="text-sm text-gray-500">
                      还有 {template.chapters.length - 3} 个章节...
                    </div>
                  )}
                </div>
              </div>
            </button>
          ))}
        </div>

        {/* Selected Template Details */}
        {selectedTemplate && (
          <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <div className="flex items-center mb-6">
              <div className="text-3xl mr-4">{selectedTemplate.icon}</div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900">{selectedTemplate.name}</h3>
                <p className="text-gray-600">{selectedTemplate.description}</p>
              </div>
            </div>

            <div className="mb-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">章节详情</h4>
              <div className="space-y-4">
                {selectedTemplate.chapters.map((chapter, index) => (
                  <div key={`${selectedTemplate.id}-detail-${index}-${chapter.title}`} className="border border-gray-200 rounded-lg p-4">
                    <h5 className="font-medium text-gray-900 mb-2">
                      第{index + 1}章：{chapter.title}
                    </h5>
                    <p className="text-gray-600 text-sm mb-3">{chapter.description}</p>
                    <div className="space-y-1">
                      <p className="text-xs font-medium text-gray-700">引导问题示例：</p>
                      {chapter.prompts.slice(0, 2).map((prompt, promptIndex) => (
                        <p key={`${chapter.title}-prompt-${promptIndex}-${prompt.substring(0, 20)}`} className="text-xs text-gray-600">
                          • {prompt}
                        </p>
                      ))}
                      {chapter.prompts.length > 2 && (
                        <p className="text-xs text-gray-500">
                          还有 {chapter.prompts.length - 2} 个引导问题...
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-center">
              <button
                onClick={handleStartMemoir}
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-medium text-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                开始创作回忆录
                <ChevronRight className="w-5 h-5 ml-2" />
              </button>
            </div>
          </div>
        )}

        {/* Help Text */}
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 text-center">
          <h4 className="text-lg font-semibold text-blue-900 mb-2">💡 选择建议</h4>
          <p className="text-blue-800">
            选择最符合您想要记录内容的模板。每个模板都包含精心设计的章节和引导问题，
            帮助您更好地组织和表达您的故事。您也可以在创作过程中根据需要调整内容。
          </p>
        </div>
      </main>
    </div>
  )
}
