'use client'

import { useState } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON> } from 'lucide-react'
import { getAIClient } from '@/lib/ai-client'

interface AIProcessorProps {
  transcript: string
  onProcessedContent?: (content: string) => void
  context?: {
    chapterTitle?: string
    previousContent?: string
    memoirType?: string
  }
  className?: string
}

export default function AIProcessor({
  transcript,
  onProcessedContent,
  context,
  className = ""
}: Readonly<AIProcessorProps>) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [processedContent, setProcessedContent] = useState('')
  const [error, setError] = useState('')

  const aiClient = getAIClient()

  const processContent = async () => {
    if (!transcript.trim()) {
      setError('请先录制一些内容')
      return
    }

    setIsProcessing(true)
    setError('')

    try {
      const processed = await aiClient.processTranscript({
        transcript,
        context
      })
      setProcessedContent(processed)
      onProcessedContent?.(processed)
    } catch (err) {
      console.error('AI processing error:', err)
      const errorMessage = err instanceof Error ? err.message : 'AI处理失败，请重试'
      setError(errorMessage)
    } finally {
      setIsProcessing(false)
    }
  }

  const resetContent = () => {
    setProcessedContent('')
    setError('')
  }

  return (
    <div className={`bg-white rounded-2xl shadow-lg p-6 ${className}`}>
      <div className="mb-6">
        <div className="flex items-center space-x-3 mb-2">
          <Sparkles className="w-6 h-6 text-purple-600" />
          <h3 className="text-lg font-semibold text-gray-900">AI内容整理</h3>
        </div>
        <p className="text-sm text-gray-600">
          使用AI将您的语音内容整理成流畅的回忆录文本
        </p>
      </div>

      {/* Original Transcript */}
      {transcript && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-2">原始录音内容：</h4>
          <div className="p-4 bg-gray-50 rounded-lg border">
            <p className="text-gray-900 text-sm leading-relaxed">{transcript}</p>
          </div>
        </div>
      )}

      {/* Context Information */}
      {context && (context.chapterTitle || context.memoirType) && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-2">上下文信息：</h4>
          <div className="space-y-2">
            {context.chapterTitle && (
              <div className="flex items-center space-x-2">
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                  章节：{context.chapterTitle}
                </span>
              </div>
            )}
            {context.memoirType && (
              <div className="flex items-center space-x-2">
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                  类型：{context.memoirType}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      {/* Processed Content */}
      {processedContent && (
        <div className="mb-6">
          <div className="flex items-center space-x-2 mb-3">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <h4 className="text-sm font-medium text-gray-700">AI整理结果：</h4>
          </div>
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-gray-900 leading-relaxed whitespace-pre-wrap">{processedContent}</p>
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="flex items-center justify-center space-x-4">
        {!processedContent ? (
          <button
            onClick={processContent}
            disabled={isProcessing || !transcript.trim()}
            className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              isProcessing || !transcript.trim()
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 hover:scale-105 shadow-lg'
            }`}
          >
            {isProcessing ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin" />
                <span>AI处理中...</span>
              </>
            ) : (
              <>
                <Sparkles className="w-5 h-5" />
                <span>开始AI整理</span>
              </>
            )}
          </button>
        ) : (
          <div className="flex space-x-3">
            <button
              onClick={resetContent}
              className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              重新整理
            </button>
            <button
              onClick={() => {
                navigator.clipboard.writeText(processedContent)
                // You could add a toast notification here
              }}
              className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
            >
              复制内容
            </button>
          </div>
        )}
      </div>

      {/* Processing Status */}
      {isProcessing && (
        <div className="mt-4 text-center">
          <div className="inline-flex items-center space-x-2 text-purple-600">
            <Loader2 className="w-4 h-4 animate-spin" />
            <span className="text-sm font-medium">AI正在分析和整理您的内容...</span>
          </div>
        </div>
      )}

      {/* Demo Mode Notice */}
      {processedContent && processedContent.includes('*注：当前使用演示模式处理内容*') && (
        <div className="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
          <h5 className="text-sm font-medium text-amber-900 mb-2">🔧 演示模式</h5>
          <p className="text-xs text-amber-800">
            当前使用本地演示模式处理内容。在生产环境中，将使用更强大的AI模型提供更好的内容整理效果。
          </p>
        </div>
      )}

      {/* Tips */}
      {!processedContent && !isProcessing && (
        <div className="mt-6 p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-200">
          <h5 className="text-sm font-medium text-purple-900 mb-2">💡 AI整理功能</h5>
          <ul className="text-xs text-purple-800 space-y-1">
            <li>• AI会保持您的原意和情感表达</li>
            <li>• 将口语化内容转换为流畅的书面语</li>
            <li>• 自动整理语句逻辑，使内容更连贯</li>
            <li>• 适当补充细节，增强表达效果</li>
          </ul>
        </div>
      )}
    </div>
  )
}
