'use client'

import { useEffect } from 'react'
import { OptimizationUtils } from '@/lib/optimization-utils'
import { PerformanceMonitor } from '@/lib/performance-monitor'

export default function PerformanceInitializer() {
  useEffect(() => {
    // 初始化性能优化
    OptimizationUtils.initializeOptimizations()
    
    // 开始性能监控
    const monitor = PerformanceMonitor.getInstance()
    monitor.startMonitoring()
    
    // 设置网络监控
    OptimizationUtils.setupNetworkMonitoring((online, speed) => {
      console.log(`Network status: ${online ? 'online' : 'offline'}, speed: ${speed}`)
      
      // 根据网络状态调整应用行为
      if (!online) {
        // 离线模式处理
        document.body.classList.add('offline-mode')
      } else {
        document.body.classList.remove('offline-mode')
      }
    })

    // 清理函数
    return () => {
      monitor.stopMonitoring()
    }
  }, [])

  // 这个组件不渲染任何内容
  return null
}
