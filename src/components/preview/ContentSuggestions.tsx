'use client'

import { useState } from 'react'
import { 
  Lightbulb, 
  CheckCircle, 
  X, 
  AlertTriangle, 
  Info, 
  Zap,
  Filter,
  RefreshCw
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { type ContentSuggestion } from '@/lib/content-preview'
import { cn } from '@/lib/utils'

export interface ContentSuggestionsProps {
  readonly suggestions: ContentSuggestion[]
  readonly onApplySuggestion: (suggestion: ContentSuggestion) => void
  readonly onDismissSuggestion: (suggestionId: string) => void
  readonly onRefreshSuggestions?: () => void
  readonly className?: string
}

export default function ContentSuggestions({
  suggestions,
  onApplySuggestion,
  onDismissSuggestion,
  onRefreshSuggestions,
  className
}: ContentSuggestionsProps) {
  const [filterType, setFilterType] = useState<string>('all')
  const [filterSeverity, setFilterSeverity] = useState<string>('all')

  // 过滤建议
  const filteredSuggestions = suggestions.filter(suggestion => {
    const typeMatch = filterType === 'all' || suggestion.type === filterType
    const severityMatch = filterSeverity === 'all' || suggestion.severity === filterSeverity
    return typeMatch && severityMatch
  })

  // 获取建议图标
  const getSuggestionIcon = (type: ContentSuggestion['type']) => {
    switch (type) {
      case 'grammar':
        return <AlertTriangle className="w-5 h-5 text-red-600" />
      case 'style':
        return <Zap className="w-5 h-5 text-yellow-600" />
      case 'structure':
        return <Info className="w-5 h-5 text-blue-600" />
      case 'enhancement':
        return <Lightbulb className="w-5 h-5 text-green-600" />
      default:
        return <Info className="w-5 h-5 text-gray-600" />
    }
  }

  // 获取严重程度颜色
  const getSeverityColor = (severity: ContentSuggestion['severity']) => {
    switch (severity) {
      case 'high':
        return 'border-red-200 bg-red-50'
      case 'medium':
        return 'border-yellow-200 bg-yellow-50'
      case 'low':
        return 'border-blue-200 bg-blue-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  // 获取类型标签
  const getTypeLabel = (type: ContentSuggestion['type']) => {
    switch (type) {
      case 'grammar':
        return '语法'
      case 'style':
        return '风格'
      case 'structure':
        return '结构'
      case 'enhancement':
        return '增强'
      default:
        return type
    }
  }

  // 获取严重程度标签
  const getSeverityLabel = (severity: ContentSuggestion['severity']) => {
    switch (severity) {
      case 'high':
        return '重要'
      case 'medium':
        return '中等'
      case 'low':
        return '建议'
      default:
        return severity
    }
  }

  if (suggestions.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-12 text-center">
          <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">内容很棒！</h3>
          <p className="text-gray-600">暂无优化建议，您的内容质量很好</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* 头部和筛选 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Lightbulb className="w-5 h-5 text-yellow-600" />
              <span>优化建议</span>
              <span className="text-sm font-normal text-gray-600">
                ({filteredSuggestions.length}/{suggestions.length})
              </span>
            </div>
            
            {onRefreshSuggestions && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRefreshSuggestions}
                icon={<RefreshCw className="w-4 h-4" />}
              >
                刷新
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="flex flex-wrap gap-4">
            {/* 类型筛选 */}
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-700">类型：</span>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="text-sm border border-gray-300 rounded px-2 py-1"
              >
                <option value="all">全部</option>
                <option value="grammar">语法</option>
                <option value="style">风格</option>
                <option value="structure">结构</option>
                <option value="enhancement">增强</option>
              </select>
            </div>
            
            {/* 严重程度筛选 */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700">重要性：</span>
              <select
                value={filterSeverity}
                onChange={(e) => setFilterSeverity(e.target.value)}
                className="text-sm border border-gray-300 rounded px-2 py-1"
              >
                <option value="all">全部</option>
                <option value="high">重要</option>
                <option value="medium">中等</option>
                <option value="low">建议</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 建议列表 */}
      <div className="space-y-4">
        {filteredSuggestions.map((suggestion) => (
          <Card
            key={suggestion.id}
            className={cn('border-l-4', getSeverityColor(suggestion.severity))}
          >
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  {/* 建议头部 */}
                  <div className="flex items-center space-x-3 mb-3">
                    {getSuggestionIcon(suggestion.type)}
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">
                        {suggestion.message}
                      </span>
                      <span className={cn(
                        'px-2 py-1 text-xs rounded-full',
                        suggestion.severity === 'high' ? 'bg-red-100 text-red-800' :
                        suggestion.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-blue-100 text-blue-800'
                      )}>
                        {getSeverityLabel(suggestion.severity)}
                      </span>
                      <span className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full">
                        {getTypeLabel(suggestion.type)}
                      </span>
                    </div>
                  </div>

                  {/* 建议内容 */}
                  <div className="space-y-3">
                    <p className="text-gray-700 leading-relaxed">
                      {suggestion.suggestion}
                    </p>
                    
                    {/* 原文和建议对比 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <div className="text-sm font-medium text-gray-700 mb-2">原文：</div>
                        <div className="p-3 bg-red-50 border border-red-200 rounded text-sm">
                          {suggestion.originalText}
                        </div>
                      </div>
                      
                      <div>
                        <div className="text-sm font-medium text-gray-700 mb-2">建议：</div>
                        <div className="p-3 bg-green-50 border border-green-200 rounded text-sm">
                          {suggestion.suggestedText}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center space-x-2 ml-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onApplySuggestion(suggestion)}
                    icon={<CheckCircle className="w-4 h-4" />}
                  >
                    应用
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDismissSuggestion(suggestion.id)}
                    icon={<X className="w-4 h-4" />}
                  >
                    忽略
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 空状态（筛选后无结果） */}
      {filteredSuggestions.length === 0 && suggestions.length > 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Filter className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">无匹配建议</h3>
            <p className="text-gray-600 mb-4">当前筛选条件下没有找到建议</p>
            <Button
              variant="outline"
              onClick={() => {
                setFilterType('all')
                setFilterSeverity('all')
              }}
            >
              清除筛选
            </Button>
          </CardContent>
        </Card>
      )}

      {/* 统计信息 */}
      {suggestions.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div className="flex items-center space-x-4">
                <span>总建议：{suggestions.length}</span>
                <span>重要：{suggestions.filter(s => s.severity === 'high').length}</span>
                <span>中等：{suggestions.filter(s => s.severity === 'medium').length}</span>
                <span>建议：{suggestions.filter(s => s.severity === 'low').length}</span>
              </div>
              
              <div className="flex items-center space-x-4">
                <span>语法：{suggestions.filter(s => s.type === 'grammar').length}</span>
                <span>风格：{suggestions.filter(s => s.type === 'style').length}</span>
                <span>结构：{suggestions.filter(s => s.type === 'structure').length}</span>
                <span>增强：{suggestions.filter(s => s.type === 'enhancement').length}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
