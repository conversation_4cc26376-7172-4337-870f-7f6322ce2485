'use client'

import { useState, useCallback, useEffect } from 'react'
import { 
  Layout, 
  Eye, 
  Edit3, 
  Lightbulb, 
  Save, 
  RefreshCw,
  Split,
  Maximize2,
  Minimize2
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import ContentPreview from './ContentPreview'
import ContentEditor from './ContentEditor'
import ContentSuggestions from './ContentSuggestions'
import { ContentPreviewService, type ContentSuggestion } from '@/lib/content-preview'
import { cn } from '@/lib/utils'

export interface ContentWorkspaceProps {
  readonly content: string
  readonly onContentChange: (content: string) => void
  readonly onSave?: (content: string) => void
  readonly title?: string
  readonly author?: string
  readonly chapter?: string
  readonly className?: string
}

type ViewMode = 'split' | 'editor' | 'preview' | 'suggestions'

export default function ContentWorkspace({
  content,
  onContentChange,
  onSave,
  title,
  author,
  chapter,
  className
}: ContentWorkspaceProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('split')
  const [suggestions, setSuggestions] = useState<ContentSuggestion[]>([])
  const [dismissedSuggestions, setDismissedSuggestions] = useState<Set<string>>(new Set())
  const [isFullscreen, setIsFullscreen] = useState(false)

  // 刷新建议
  const refreshSuggestions = useCallback(() => {
    if (content.trim()) {
      const previewData = ContentPreviewService.generatePreview(content)
      setSuggestions(previewData.suggestions.filter(s => !dismissedSuggestions.has(s.id)))
    } else {
      setSuggestions([])
    }
  }, [content, dismissedSuggestions])

  // 应用建议
  const handleApplySuggestion = useCallback((suggestion: ContentSuggestion) => {
    const newContent = ContentPreviewService.applySuggestion(content, suggestion)
    onContentChange(newContent)
    
    // 移除已应用的建议
    setSuggestions(prev => prev.filter(s => s.id !== suggestion.id))
    setDismissedSuggestions(prev => new Set([...prev, suggestion.id]))
  }, [content, onContentChange])

  // 忽略建议
  const handleDismissSuggestion = useCallback((suggestionId: string) => {
    setSuggestions(prev => prev.filter(s => s.id !== suggestionId))
    setDismissedSuggestions(prev => new Set([...prev, suggestionId]))
  }, [])

  // 自动刷新建议
  useEffect(() => {
    refreshSuggestions()
  }, [content, refreshSuggestions])

  // 获取视图模式按钮样式
  const getViewModeButtonVariant = (mode: ViewMode) => {
    return viewMode === mode ? 'primary' : 'outline'
  }

  // 渲染内容区域
  const renderContent = () => {
    switch (viewMode) {
      case 'editor':
        return (
          <ContentEditor
            content={content}
            onContentChange={onContentChange}
            onSave={onSave}
            title={title}
            showPreview={false}
          />
        )
      
      case 'preview':
        return (
          <ContentPreview
            content={content}
            title={title}
            author={author}
            chapter={chapter}
          />
        )
      
      case 'suggestions':
        return (
          <ContentSuggestions
            suggestions={suggestions}
            onApplySuggestion={handleApplySuggestion}
            onDismissSuggestion={handleDismissSuggestion}
            onRefreshSuggestions={refreshSuggestions}
          />
        )
      
      case 'split':
      default:
        return (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ContentEditor
              content={content}
              onContentChange={onContentChange}
              onSave={onSave}
              title="编辑器"
              showPreview={false}
            />
            
            <ContentPreview
              content={content}
              title={title}
              author={author}
              chapter={chapter}
            />
          </div>
        )
    }
  }

  return (
    <div className={cn(
      'space-y-6',
      isFullscreen && 'fixed inset-0 z-50 bg-white p-6 overflow-auto',
      className
    )}>
      {/* 工具栏 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Layout className="w-5 h-5 text-blue-600" />
              <span>内容工作区</span>
              {title && <span className="text-sm font-normal text-gray-600">- {title}</span>}
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsFullscreen(!isFullscreen)}
                icon={isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
              >
                {isFullscreen ? '退出全屏' : '全屏'}
              </Button>
              
              {onSave && (
                <Button
                  size="sm"
                  onClick={() => onSave(content)}
                  disabled={!content.trim()}
                  icon={<Save className="w-4 h-4" />}
                >
                  保存
                </Button>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="flex flex-wrap items-center gap-3">
            {/* 视图模式切换 */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700">视图：</span>
              
              <Button
                variant={getViewModeButtonVariant('split')}
                size="sm"
                onClick={() => setViewMode('split')}
                icon={<Split className="w-4 h-4" />}
              >
                分屏
              </Button>
              
              <Button
                variant={getViewModeButtonVariant('editor')}
                size="sm"
                onClick={() => setViewMode('editor')}
                icon={<Edit3 className="w-4 h-4" />}
              >
                编辑
              </Button>
              
              <Button
                variant={getViewModeButtonVariant('preview')}
                size="sm"
                onClick={() => setViewMode('preview')}
                icon={<Eye className="w-4 h-4" />}
              >
                预览
              </Button>
              
              <Button
                variant={getViewModeButtonVariant('suggestions')}
                size="sm"
                onClick={() => setViewMode('suggestions')}
                icon={<Lightbulb className="w-4 h-4" />}
              >
                建议 {suggestions.length > 0 && `(${suggestions.length})`}
              </Button>
            </div>
            
            <div className="w-px h-6 bg-gray-300" />
            
            {/* 快速操作 */}
            <Button
              variant="outline"
              size="sm"
              onClick={refreshSuggestions}
              disabled={!content.trim()}
              icon={<RefreshCw className="w-4 h-4" />}
            >
              刷新建议
            </Button>
            
            {/* 统计信息 */}
            <div className="flex items-center space-x-4 text-sm text-gray-600 ml-auto">
              <span>字数：{content.length}</span>
              <span>建议：{suggestions.length}</span>
              {content.trim() && (
                <span>
                  阅读时间：{Math.ceil(ContentPreviewService.generatePreview(content).readingTime)} 分钟
                </span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 主要内容区域 */}
      <div className="min-h-[600px]">
        {renderContent()}
      </div>

      {/* 建议快速访问（当不在建议视图时显示） */}
      {viewMode !== 'suggestions' && suggestions.length > 0 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Lightbulb className="w-5 h-5 text-yellow-600" />
                <div>
                  <div className="font-medium text-yellow-900">
                    发现 {suggestions.length} 个优化建议
                  </div>
                  <div className="text-sm text-yellow-700">
                    包括 {suggestions.filter(s => s.severity === 'high').length} 个重要建议
                  </div>
                </div>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setViewMode('suggestions')}
                className="border-yellow-300 text-yellow-700 hover:bg-yellow-100"
              >
                查看建议
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 空状态 */}
      {!content.trim() && (
        <Card>
          <CardContent className="p-12 text-center">
            <Edit3 className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">开始创作</h3>
            <p className="text-gray-600 mb-6">
              在编辑器中输入内容，实时预览和优化建议将自动显示
            </p>
            <Button
              onClick={() => setViewMode('editor')}
              icon={<Edit3 className="w-5 h-5" />}
            >
              开始编辑
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
