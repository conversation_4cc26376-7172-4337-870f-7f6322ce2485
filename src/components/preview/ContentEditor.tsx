'use client'

import { useState, useRef, useEffect } from 'react'
import {
  Edit3,
  Save,
  X,
  Undo,
  Redo,
  Bold,
  Italic,
  List,
  Quote,
  Type,
  Eye
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'

export interface ContentEditorProps {
  readonly content: string
  readonly onContentChange: (content: string) => void
  readonly onSave?: (content: string) => void
  readonly onCancel?: () => void
  readonly title?: string
  readonly placeholder?: string
  readonly readOnly?: boolean
  readonly showPreview?: boolean
  readonly className?: string
}

interface EditHistory {
  readonly content: string
  readonly timestamp: number
}

export default function ContentEditor({
  content,
  onContentChange,
  onSave,
  onCancel,
  title,
  placeholder = '开始输入您的内容...',
  readOnly = false,
  showPreview = true,
  className
}: ContentEditorProps) {
  const [isEditing, setIsEditing] = useState(!readOnly)
  const [currentContent, setCurrentContent] = useState(content)
  const [history, setHistory] = useState<EditHistory[]>([{ content, timestamp: Date.now() }])
  const [historyIndex, setHistoryIndex] = useState(0)
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [selectionStart, setSelectionStart] = useState(0)
  const [selectionEnd, setSelectionEnd] = useState(0)

  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // 同步外部内容变化
  useEffect(() => {
    if (content !== currentContent) {
      setCurrentContent(content)
      const newHistory = history.slice(0, historyIndex + 1)
      newHistory.push({ content, timestamp: Date.now() })

      if (newHistory.length > 50) {
        newHistory.shift()
      } else {
        setHistoryIndex(historyIndex + 1)
      }

      setHistory(newHistory)
    }
  }, [content, currentContent, history, historyIndex])

  // 添加到历史记录
  const addToHistory = (newContent: string) => {
    const newHistory = history.slice(0, historyIndex + 1)
    newHistory.push({ content: newContent, timestamp: Date.now() })
    
    // 限制历史记录数量
    if (newHistory.length > 50) {
      newHistory.shift()
    } else {
      setHistoryIndex(historyIndex + 1)
    }
    
    setHistory(newHistory)
  }

  // 处理内容变化
  const handleContentChange = (newContent: string) => {
    setCurrentContent(newContent)
    onContentChange(newContent)
    
    // 防抖添加到历史记录
    const timeoutId = setTimeout(() => {
      addToHistory(newContent)
    }, 1000)

    return () => clearTimeout(timeoutId)
  }

  // 撤销
  const handleUndo = () => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1
      const previousContent = history[newIndex].content
      setCurrentContent(previousContent)
      onContentChange(previousContent)
      setHistoryIndex(newIndex)
    }
  }

  // 重做
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1
      const nextContent = history[newIndex].content
      setCurrentContent(nextContent)
      onContentChange(nextContent)
      setHistoryIndex(newIndex)
    }
  }

  // 获取选中文本
  const getSelectedText = () => {
    if (textareaRef.current) {
      const start = textareaRef.current.selectionStart
      const end = textareaRef.current.selectionEnd
      return currentContent.substring(start, end)
    }
    return ''
  }

  // 插入文本
  const insertText = (textToInsert: string, wrapSelection = false) => {
    if (!textareaRef.current) return

    const start = textareaRef.current.selectionStart
    const end = textareaRef.current.selectionEnd
    const selectedText = currentContent.substring(start, end)
    
    let newText: string
    let newCursorPos: number

    if (wrapSelection && selectedText) {
      newText = textToInsert + selectedText + textToInsert
      newCursorPos = start + textToInsert.length + selectedText.length + textToInsert.length
    } else {
      newText = textToInsert
      newCursorPos = start + textToInsert.length
    }

    const newContent = currentContent.substring(0, start) + newText + currentContent.substring(end)
    
    handleContentChange(newContent)
    
    // 恢复光标位置
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus()
        textareaRef.current.setSelectionRange(newCursorPos, newCursorPos)
      }
    }, 0)
  }

  // 格式化操作
  const formatBold = () => insertText('**', true)
  const formatItalic = () => insertText('*', true)
  const formatQuote = () => {
    const lines = getSelectedText().split('\n')
    const quotedLines = lines.map(line => `> ${line}`).join('\n')
    insertText(quotedLines)
  }
  const formatList = () => {
    const lines = getSelectedText().split('\n')
    const listLines = lines.map((line, index) => `${index + 1}. ${line}`).join('\n')
    insertText(listLines)
  }

  // 保存
  const handleSave = () => {
    onSave?.(currentContent)
    setIsEditing(false)
  }

  // 取消
  const handleCancel = () => {
    setCurrentContent(content)
    onContentChange(content)
    setIsEditing(false)
    onCancel?.()
  }

  // 更新选择范围
  const handleSelectionChange = () => {
    if (textareaRef.current) {
      setSelectionStart(textareaRef.current.selectionStart)
      setSelectionEnd(textareaRef.current.selectionEnd)
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Edit3 className="w-5 h-5 text-blue-600" />
            <span>{title ?? '内容编辑'}</span>
            {currentContent.length > 0 && (
              <span className="text-sm font-normal text-gray-600">
                {currentContent.length} 字符
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {showPreview && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsPreviewMode(!isPreviewMode)}
                icon={isPreviewMode ? <Edit3 className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              >
                {isPreviewMode ? '编辑' : '预览'}
              </Button>
            )}
            
            {!readOnly && (
              <>
                {isEditing ? (
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCancel}
                      icon={<X className="w-4 h-4" />}
                    >
                      取消
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleSave}
                      icon={<Save className="w-4 h-4" />}
                    >
                      保存
                    </Button>
                  </div>
                ) : (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(true)}
                    icon={<Edit3 className="w-4 h-4" />}
                  >
                    编辑
                  </Button>
                )}
              </>
            )}
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="p-0">
        {isEditing && !isPreviewMode && (
          /* 编辑工具栏 */
          <div className="flex items-center space-x-2 p-4 border-b border-gray-200 bg-gray-50">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleUndo}
              disabled={historyIndex <= 0}
              icon={<Undo className="w-4 h-4" />}
              title="撤销"
            />
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRedo}
              disabled={historyIndex >= history.length - 1}
              icon={<Redo className="w-4 h-4" />}
              title="重做"
            />
            
            <div className="w-px h-6 bg-gray-300 mx-2" />
            
            <Button
              variant="ghost"
              size="sm"
              onClick={formatBold}
              icon={<Bold className="w-4 h-4" />}
              title="粗体"
            />
            
            <Button
              variant="ghost"
              size="sm"
              onClick={formatItalic}
              icon={<Italic className="w-4 h-4" />}
              title="斜体"
            />
            
            <Button
              variant="ghost"
              size="sm"
              onClick={formatQuote}
              icon={<Quote className="w-4 h-4" />}
              title="引用"
            />
            
            <Button
              variant="ghost"
              size="sm"
              onClick={formatList}
              icon={<List className="w-4 h-4" />}
              title="列表"
            />
            
            <div className="flex-1" />
            
            <span className="text-sm text-gray-500">
              {selectionStart !== selectionEnd && 
                `已选择 ${selectionEnd - selectionStart} 字符`
              }
            </span>
          </div>
        )}

        {/* 内容区域 */}
        <div className="p-6">
          {isEditing && !isPreviewMode ? (
            /* 编辑模式 */
            <textarea
              ref={textareaRef}
              value={currentContent}
              onChange={(e) => handleContentChange(e.target.value)}
              onSelect={handleSelectionChange}
              placeholder={placeholder}
              className="w-full min-h-[400px] p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-lg leading-relaxed"
              style={{ fontFamily: 'inherit' }}
            />
          ) : (
            /* 预览模式 */
            <div className="min-h-[400px]">
              {currentContent ? (
                <div className="prose prose-lg max-w-none">
                  <div className="whitespace-pre-wrap text-lg leading-relaxed">
                    {currentContent}
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-64 text-gray-500">
                  <div className="text-center">
                    <Type className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                    <p className="text-lg">暂无内容</p>
                    <p className="text-sm">点击编辑按钮开始创作</p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* 状态栏 */}
        {isEditing && (
          <div className="flex items-center justify-between px-6 py-3 border-t border-gray-200 bg-gray-50 text-sm text-gray-600">
            <div className="flex items-center space-x-4">
              <span>字符数：{currentContent.length}</span>
              <span>行数：{currentContent.split('\n').length}</span>
              <span>段落数：{currentContent.split('\n\n').filter(p => p.trim()).length}</span>
            </div>
            
            <div className="flex items-center space-x-4">
              <span>历史记录：{historyIndex + 1}/{history.length}</span>
              <span>最后修改：{new Date(history[historyIndex]?.timestamp || Date.now()).toLocaleTimeString('zh-CN')}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
