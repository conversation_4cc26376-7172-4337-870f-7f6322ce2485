'use client'

import { useState, useMemo } from 'react'
import { 
  <PERSON>, 
  Settings, 
  Download, 
  Share2, 
  Clock, 
  FileText,
  Palette,
  Type,
  MoreHorizontal
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Modal, ModalContent } from '@/components/ui/Modal'
import { ContentPreviewService, type PreviewOptions } from '@/lib/content-preview'
import { cn } from '@/lib/utils'

export interface ContentPreviewProps {
  readonly content: string
  readonly title?: string
  readonly author?: string
  readonly chapter?: string
  readonly className?: string
}

export default function ContentPreview({
  content,
  title,
  author = '用户',
  chapter,
  className
}: ContentPreviewProps) {
  const [previewOptions, setPreviewOptions] = useState<PreviewOptions>({
    theme: 'default',
    fontSize: 'medium',
    lineHeight: 'normal',
    showMetadata: true,
    showAudio: true,
    showImages: true
  })
  const [showSettings, setShowSettings] = useState(false)
  const [showExportMenu, setShowExportMenu] = useState(false)

  // 生成预览数据
  const previewData = useMemo(() => {
    if (!content.trim()) {
      return {
        content: '',
        wordCount: 0,
        readingTime: 0,
        suggestions: [],
        metadata: {
          lastModified: new Date().toISOString(),
          author,
          chapter,
          tags: []
        }
      }
    }

    const data = ContentPreviewService.generatePreview(content)
    return {
      ...data,
      metadata: {
        ...data.metadata,
        author,
        chapter
      }
    }
  }, [content, author, chapter])

  // 获取主题样式
  const themeStyles = ContentPreviewService.getThemeStyles(previewOptions.theme)
  const fontSizeStyles = ContentPreviewService.getFontSizeStyles(previewOptions.fontSize)

  // 导出功能
  const handleExport = (format: 'html' | 'text' | 'markdown') => {
    const exportedContent = ContentPreviewService.exportPreview(previewData, format)
    const blob = new Blob([exportedContent], { 
      type: format === 'html' ? 'text/html' : 'text/plain' 
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${title || '回忆录内容'}.${format === 'html' ? 'html' : format === 'markdown' ? 'md' : 'txt'}`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    setShowExportMenu(false)
  }

  // 分享功能
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: title || '回忆录内容',
          text: content.substring(0, 100) + '...',
          url: window.location.href
        })
      } catch (error) {
        console.log('分享取消或失败:', error)
      }
    } else {
      // 复制到剪贴板
      await navigator.clipboard.writeText(content)
      alert('内容已复制到剪贴板')
    }
  }

  if (!content.trim()) {
    return (
      <Card className={className}>
        <CardContent className="p-12 text-center">
          <Eye className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无内容</h3>
          <p className="text-gray-600">开始创建内容后，预览将在这里显示</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* 预览控制栏 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Eye className="w-5 h-5 text-blue-600" />
              <span>内容预览</span>
              {title && <span className="text-sm font-normal text-gray-600">- {title}</span>}
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSettings(true)}
                icon={<Settings className="w-4 h-4" />}
              >
                设置
              </Button>
              
              <div className="relative">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowExportMenu(!showExportMenu)}
                  icon={<Download className="w-4 h-4" />}
                >
                  导出
                </Button>
                
                {showExportMenu && (
                  <div className="absolute right-0 top-full mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                    <div className="py-2">
                      <button
                        onClick={() => handleExport('text')}
                        className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50"
                      >
                        导出为文本文件
                      </button>
                      <button
                        onClick={() => handleExport('html')}
                        className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50"
                      >
                        导出为HTML
                      </button>
                      <button
                        onClick={() => handleExport('markdown')}
                        className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50"
                      >
                        导出为Markdown
                      </button>
                    </div>
                  </div>
                )}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleShare}
                icon={<Share2 className="w-4 h-4" />}
              >
                分享
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* 内容统计 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <FileText className="w-8 h-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">{previewData.wordCount}</div>
            <div className="text-sm text-gray-600">字数</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Clock className="w-8 h-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">{previewData.readingTime}</div>
            <div className="text-sm text-gray-600">分钟阅读</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Settings className="w-8 h-8 text-orange-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">{previewData.suggestions.length}</div>
            <div className="text-sm text-gray-600">优化建议</div>
          </CardContent>
        </Card>
      </div>

      {/* 预览内容 */}
      <Card>
        <CardContent className="p-0">
          <div 
            className="p-8 min-h-[400px]"
            style={{
              ...themeStyles,
              ...fontSizeStyles,
              lineHeight: previewOptions.lineHeight === 'compact' ? '1.4' : 
                         previewOptions.lineHeight === 'relaxed' ? '1.8' : '1.6'
            }}
          >
            {/* 元数据 */}
            {previewOptions.showMetadata && (
              <div className="mb-6 pb-4 border-b border-gray-200">
                <div className="text-sm text-gray-600 space-y-1">
                  <div>作者：{previewData.metadata.author}</div>
                  {previewData.metadata.chapter && (
                    <div>章节：{previewData.metadata.chapter}</div>
                  )}
                  <div>最后修改：{new Date(previewData.metadata.lastModified).toLocaleString('zh-CN')}</div>
                  {previewData.metadata.tags && previewData.metadata.tags.length > 0 && (
                    <div className="flex items-center space-x-2 mt-2">
                      <span>标签：</span>
                      <div className="flex flex-wrap gap-1">
                        {previewData.metadata.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
            
            {/* 内容 */}
            <div className="whitespace-pre-wrap leading-relaxed">
              {previewData.content}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 设置模态框 */}
      <Modal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        title="预览设置"
        size="md"
      >
        <ModalContent>
          <div className="space-y-6 p-6">
            {/* 主题选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                <Palette className="w-4 h-4 inline mr-2" />
                主题
              </label>
              <div className="grid grid-cols-2 gap-3">
                {(['default', 'classic', 'modern', 'elegant'] as const).map(theme => (
                  <button
                    key={theme}
                    onClick={() => setPreviewOptions(prev => ({ ...prev, theme }))}
                    className={cn(
                      'p-3 border rounded-lg text-left transition-all duration-200',
                      previewOptions.theme === theme
                        ? 'border-blue-500 bg-blue-50 text-blue-900'
                        : 'border-gray-200 hover:border-gray-300'
                    )}
                  >
                    <div className="font-medium">
                      {theme === 'default' && '默认'}
                      {theme === 'classic' && '经典'}
                      {theme === 'modern' && '现代'}
                      {theme === 'elegant' && '优雅'}
                    </div>
                    <div className="text-xs text-gray-600 mt-1">
                      {theme === 'default' && '简洁清晰'}
                      {theme === 'classic' && '传统书籍'}
                      {theme === 'modern' && '现代简约'}
                      {theme === 'elegant' && '优雅精致'}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* 字体大小 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                <Type className="w-4 h-4 inline mr-2" />
                字体大小
              </label>
              <div className="grid grid-cols-4 gap-2">
                {(['small', 'medium', 'large', 'extra-large'] as const).map(size => (
                  <button
                    key={size}
                    onClick={() => setPreviewOptions(prev => ({ ...prev, fontSize: size }))}
                    className={cn(
                      'p-2 border rounded text-center transition-all duration-200',
                      previewOptions.fontSize === size
                        ? 'border-blue-500 bg-blue-50 text-blue-900'
                        : 'border-gray-200 hover:border-gray-300'
                    )}
                  >
                    {size === 'small' && '小'}
                    {size === 'medium' && '中'}
                    {size === 'large' && '大'}
                    {size === 'extra-large' && '特大'}
                  </button>
                ))}
              </div>
            </div>

            {/* 行高 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                <MoreHorizontal className="w-4 h-4 inline mr-2" />
                行高
              </label>
              <div className="grid grid-cols-3 gap-2">
                {(['compact', 'normal', 'relaxed'] as const).map(lineHeight => (
                  <button
                    key={lineHeight}
                    onClick={() => setPreviewOptions(prev => ({ ...prev, lineHeight }))}
                    className={cn(
                      'p-2 border rounded text-center transition-all duration-200',
                      previewOptions.lineHeight === lineHeight
                        ? 'border-blue-500 bg-blue-50 text-blue-900'
                        : 'border-gray-200 hover:border-gray-300'
                    )}
                  >
                    {lineHeight === 'compact' && '紧凑'}
                    {lineHeight === 'normal' && '正常'}
                    {lineHeight === 'relaxed' && '宽松'}
                  </button>
                ))}
              </div>
            </div>

            {/* 显示选项 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">显示选项</label>
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={previewOptions.showMetadata}
                    onChange={(e) => setPreviewOptions(prev => ({ 
                      ...prev, 
                      showMetadata: e.target.checked 
                    }))}
                    className="mr-3 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  显示元数据信息
                </label>
                
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={previewOptions.showAudio}
                    onChange={(e) => setPreviewOptions(prev => ({ 
                      ...prev, 
                      showAudio: e.target.checked 
                    }))}
                    className="mr-3 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  显示音频控件
                </label>
                
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={previewOptions.showImages}
                    onChange={(e) => setPreviewOptions(prev => ({ 
                      ...prev, 
                      showImages: e.target.checked 
                    }))}
                    className="mr-3 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  显示图片
                </label>
              </div>
            </div>
          </div>
        </ModalContent>
      </Modal>
    </div>
  )
}
