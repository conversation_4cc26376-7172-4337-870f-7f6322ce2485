'use client'

import { useState, useEffect, useCallback } from 'react'
import { 
  TestTube, 
  Monitor, 
  Smartphone, 
  Accessibility, 
  Zap, 
  AlertTriangle,
  CheckCircle,
  Info,
  RefreshCw,
  Download
} from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { PerformanceMonitor, type PerformanceMetrics, type AccessibilityIssue } from '@/lib/performance-monitor'
import { cn } from '@/lib/utils'

export interface TestingPanelProps {
  readonly className?: string
}

export default function TestingPanel({ className }: TestingPanelProps) {
  const [isMonitoring, setIsMonitoring] = useState(false)
  const [performanceScore, setPerformanceScore] = useState(0)
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [accessibilityIssues, setAccessibilityIssues] = useState<AccessibilityIssue[]>([])
  const [deviceTests, setDeviceTests] = useState({
    mobile: false,
    tablet: false,
    desktop: false
  })

  const monitor = PerformanceMonitor.getInstance()

  // 开始监控
  const startMonitoring = useCallback(() => {
    setIsMonitoring(true)
    monitor.startMonitoring()
    
    // 定期更新数据
    const interval = setInterval(() => {
      const report = monitor.getPerformanceReport()
      setMetrics(report.metrics)
      setAccessibilityIssues(report.accessibilityIssues)
      setPerformanceScore(monitor.getPerformanceScore())
    }, 2000)

    setTimeout(() => {
      clearInterval(interval)
      setIsMonitoring(false)
    }, 10000)
  }, [monitor])

  // 检测设备类型
  const detectDeviceTypes = useCallback(() => {
    const width = window.innerWidth
    setDeviceTests({
      mobile: width <= 768,
      tablet: width > 768 && width <= 1024,
      desktop: width > 1024
    })
  }, [])

  useEffect(() => {
    // 自动开始监控
    startMonitoring()

    // 检测设备类型
    detectDeviceTypes()

    return () => {
      monitor.stopMonitoring()
    }
  }, [monitor, startMonitoring, detectDeviceTypes])

  // 运行响应式测试
  const runResponsiveTest = () => {
    const testSizes = [
      { name: 'mobile', width: 375, height: 667 },
      { name: 'tablet', width: 768, height: 1024 },
      { name: 'desktop', width: 1920, height: 1080 }
    ]

    testSizes.forEach(size => {
      // 在实际应用中，这里会打开新窗口进行测试
      console.log(`Testing ${size.name}: ${size.width}x${size.height}`)
    })
  }

  // 运行无障碍测试
  const runAccessibilityTest = () => {
    // 重新检查无障碍性
    setTimeout(() => {
      const report = monitor.getPerformanceReport()
      setAccessibilityIssues(report.accessibilityIssues)
    }, 1000)
  }

  // 导出测试报告
  const exportReport = () => {
    const report = {
      timestamp: new Date().toISOString(),
      performanceScore,
      metrics,
      accessibilityIssues,
      deviceTests,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    }

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `test-report-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 获取性能等级
  const getPerformanceGrade = (score: number) => {
    if (score >= 90) return { grade: 'A', color: 'text-green-600', bg: 'bg-green-100' }
    if (score >= 80) return { grade: 'B', color: 'text-blue-600', bg: 'bg-blue-100' }
    if (score >= 70) return { grade: 'C', color: 'text-yellow-600', bg: 'bg-yellow-100' }
    if (score >= 60) return { grade: 'D', color: 'text-orange-600', bg: 'bg-orange-100' }
    return { grade: 'F', color: 'text-red-600', bg: 'bg-red-100' }
  }

  const performanceGrade = getPerformanceGrade(performanceScore)

  return (
    <div className={cn('space-y-6', className)}>
      {/* 测试概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TestTube className="w-5 h-5 text-blue-600" />
            <span>测试与优化面板</span>
            {isMonitoring && (
              <div className="flex items-center space-x-2 text-sm text-blue-600">
                <RefreshCw className="w-4 h-4 animate-spin" />
                <span>监控中...</span>
              </div>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* 性能评分 */}
            <div className="text-center">
              <div className={cn(
                'w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-2 text-2xl font-bold',
                performanceGrade.bg,
                performanceGrade.color
              )}>
                {performanceGrade.grade}
              </div>
              <div className="text-sm text-gray-600">性能评分</div>
              <div className="text-lg font-semibold">{performanceScore}/100</div>
            </div>

            {/* 无障碍问题 */}
            <div className="text-center">
              <div className="w-16 h-16 rounded-full bg-purple-100 flex items-center justify-center mx-auto mb-2">
                <Accessibility className="w-8 h-8 text-purple-600" />
              </div>
              <div className="text-sm text-gray-600">无障碍问题</div>
              <div className="text-lg font-semibold">{accessibilityIssues.length}</div>
            </div>

            {/* 设备兼容性 */}
            <div className="text-center">
              <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-2">
                <Monitor className="w-8 h-8 text-green-600" />
              </div>
              <div className="text-sm text-gray-600">设备兼容</div>
              <div className="text-lg font-semibold">
                {Object.values(deviceTests).filter(Boolean).length}/3
              </div>
            </div>

            {/* 加载时间 */}
            <div className="text-center">
              <div className="w-16 h-16 rounded-full bg-orange-100 flex items-center justify-center mx-auto mb-2">
                <Zap className="w-8 h-8 text-orange-600" />
              </div>
              <div className="text-sm text-gray-600">加载时间</div>
              <div className="text-lg font-semibold">
                {metrics?.pageLoadTime ? `${(metrics.pageLoadTime / 1000).toFixed(1)}s` : '--'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 快速测试 */}
      <Card>
        <CardHeader>
          <CardTitle>快速测试</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              onClick={startMonitoring}
              disabled={isMonitoring}
              className="h-20 flex-col space-y-2"
              variant="outline"
            >
              <Zap className="w-6 h-6" />
              <span>性能测试</span>
            </Button>

            <Button
              onClick={runResponsiveTest}
              className="h-20 flex-col space-y-2"
              variant="outline"
            >
              <Smartphone className="w-6 h-6" />
              <span>响应式测试</span>
            </Button>

            <Button
              onClick={runAccessibilityTest}
              className="h-20 flex-col space-y-2"
              variant="outline"
            >
              <Accessibility className="w-6 h-6" />
              <span>无障碍测试</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 性能指标 */}
      {metrics && (
        <Card>
          <CardHeader>
            <CardTitle>性能指标</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-600">页面加载时间</div>
                <div className="text-xl font-semibold">
                  {(metrics.pageLoadTime / 1000).toFixed(2)}s
                </div>
              </div>

              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-600">首次内容绘制</div>
                <div className="text-xl font-semibold">
                  {(metrics.firstContentfulPaint / 1000).toFixed(2)}s
                </div>
              </div>

              {metrics.memoryUsage && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="text-sm text-gray-600">内存使用</div>
                  <div className="text-xl font-semibold">
                    {metrics.memoryUsage.toFixed(1)}MB
                  </div>
                </div>
              )}

              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-600">网络速度</div>
                <div className="text-xl font-semibold">
                  {metrics.networkSpeed}
                </div>
              </div>

              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-600">累积布局偏移</div>
                <div className="text-xl font-semibold">
                  {metrics.cumulativeLayoutShift.toFixed(3)}
                </div>
              </div>

              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-600">可交互时间</div>
                <div className="text-xl font-semibold">
                  {(metrics.timeToInteractive / 1000).toFixed(2)}s
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 无障碍问题 */}
      {accessibilityIssues.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-orange-600" />
              <span>无障碍问题</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {accessibilityIssues.map((issue, index) => (
                <div
                  key={`${issue.type}-${issue.element}-${index}`}
                  className={cn(
                    'p-4 rounded-lg border-l-4',
                    issue.severity === 'critical' && 'border-red-500 bg-red-50',
                    issue.severity === 'high' && 'border-orange-500 bg-orange-50',
                    issue.severity === 'medium' && 'border-yellow-500 bg-yellow-50',
                    issue.severity === 'low' && 'border-blue-500 bg-blue-50'
                  )}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className={cn(
                          'px-2 py-1 text-xs rounded-full',
                          issue.severity === 'critical' && 'bg-red-100 text-red-800',
                          issue.severity === 'high' && 'bg-orange-100 text-orange-800',
                          issue.severity === 'medium' && 'bg-yellow-100 text-yellow-800',
                          issue.severity === 'low' && 'bg-blue-100 text-blue-800'
                        )}>
                          {issue.severity}
                        </span>
                        <span className="text-sm font-medium">{issue.type}</span>
                      </div>
                      <div className="text-sm text-gray-700 mb-2">{issue.description}</div>
                      <div className="text-sm text-gray-600">
                        <strong>建议：</strong> {issue.suggestion}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        元素：{issue.element}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 设备兼容性 */}
      <Card>
        <CardHeader>
          <CardTitle>设备兼容性</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Smartphone className="w-5 h-5 text-blue-600" />
                <span>移动设备 (≤768px)</span>
              </div>
              {deviceTests.mobile ? (
                <CheckCircle className="w-5 h-5 text-green-600" />
              ) : (
                <Info className="w-5 h-5 text-gray-400" />
              )}
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Monitor className="w-5 h-5 text-blue-600" />
                <span>平板设备 (768px-1024px)</span>
              </div>
              {deviceTests.tablet ? (
                <CheckCircle className="w-5 h-5 text-green-600" />
              ) : (
                <Info className="w-5 h-5 text-gray-400" />
              )}
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Monitor className="w-5 h-5 text-blue-600" />
                <span>桌面设备 (&gt;1024px)</span>
              </div>
              {deviceTests.desktop ? (
                <CheckCircle className="w-5 h-5 text-green-600" />
              ) : (
                <Info className="w-5 h-5 text-gray-400" />
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 操作按钮 */}
      <div className="flex space-x-4">
        <Button
          onClick={exportReport}
          variant="outline"
          icon={<Download className="w-4 h-4" />}
        >
          导出测试报告
        </Button>

        <Button
          onClick={() => window.location.reload()}
          variant="outline"
          icon={<RefreshCw className="w-4 h-4" />}
        >
          重新测试
        </Button>
      </div>
    </div>
  )
}
