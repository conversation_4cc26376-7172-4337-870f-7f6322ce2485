'use client'

import { useState } from 'react'
import { X, Phone, MessageSquare, UserPlus, Loader2, CheckCircle } from 'lucide-react'
import { 
  validateChinesePhoneNumber, 
  formatPhoneNumber, 
  getCleanPhoneNumber,
  VerificationCodeManager,
  type VerificationCodeState 
} from '@/lib/phone-validation'
import { createWeChatAuthService, isWeChatBrowser } from '@/lib/wechat-auth'
import { GuestDataMigrationService } from '@/lib/guest-auth'

interface GuestUpgradeModalProps {
  isOpen: boolean
  onClose: () => void
  guestUserId: string
  onSuccess?: () => void
}

export default function GuestUpgradeModal({ 
  isOpen, 
  onClose, 
  guestUserId, 
  onSuccess 
}: GuestUpgradeModalProps) {
  const [upgradeMethod, setUpgradeMethod] = useState<'phone' | 'wechat'>('phone')
  const [phone, setPhone] = useState('')
  const [verificationCode, setVerificationCode] = useState('')
  const [codeState, setCodeState] = useState<VerificationCodeState>({
    sent: false,
    countdown: 0,
    canResend: true
  })
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [migrationStatus, setMigrationStatus] = useState<'idle' | 'migrating' | 'success' | 'error'>('idle')
  
  const [codeManager] = useState(() => new VerificationCodeManager(setCodeState))

  if (!isOpen) return null

  // 发送短信验证码
  const handleSendSMS = async () => {
    if (!validateChinesePhoneNumber(phone)) {
      setMessage('请输入有效的中国大陆手机号')
      return
    }

    setLoading(true)
    setMessage('')

    try {
      const response = await fetch('/api/auth/send-sms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone: getCleanPhoneNumber(phone) }),
      })

      const data = await response.json()

      if (data.success) {
        setMessage(data.message)
        codeManager.startCountdown(60)
      } else {
        setMessage(data.error || '发送验证码失败')
      }
    } catch {
      setMessage('发送验证码失败，请检查网络连接')
    } finally {
      setLoading(false)
    }
  }

  // 验证短信并绑定手机号
  const handlePhoneUpgrade = async () => {
    if (!validateChinesePhoneNumber(phone)) {
      setMessage('请输入有效的中国大陆手机号')
      return
    }

    if (!verificationCode || verificationCode.length !== 6) {
      setMessage('请输入6位验证码')
      return
    }

    setLoading(true)
    setMessage('')
    setMigrationStatus('migrating')

    try {
      // 1. 验证短信验证码并创建新用户
      const verifyResponse = await fetch('/api/auth/verify-sms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          phone: getCleanPhoneNumber(phone), 
          code: verificationCode 
        }),
      })

      const verifyData = await verifyResponse.json()

      if (!verifyData.success) {
        setMessage(verifyData.error || '验证失败')
        setMigrationStatus('error')
        return
      }

      // 2. 迁移访客数据
      const migrationResult = await GuestDataMigrationService.migrateGuestData(
        guestUserId,
        verifyData.userId
      )

      if (migrationResult.success) {
        setMigrationStatus('success')
        setMessage('账户绑定成功，数据已迁移！')
        
        // 跳转到新用户会话
        if (verifyData.sessionUrl) {
          setTimeout(() => {
            window.location.href = verifyData.sessionUrl
          }, 2000)
        }
        
        onSuccess?.()
      } else {
        setMigrationStatus('error')
        setMessage(migrationResult.message || '数据迁移失败')
      }
    } catch {
      setMigrationStatus('error')
      setMessage('绑定失败，请检查网络连接')
    } finally {
      setLoading(false)
    }
  }

  // 微信绑定
  const handleWeChatUpgrade = async () => {
    setLoading(true)
    setMessage('')
    setMigrationStatus('migrating')

    try {
      const wechatService = createWeChatAuthService()
      
      if ('simulateWeChatLogin' in wechatService) {
        // 模拟微信登录和数据迁移
        const result = await wechatService.simulateWeChatLogin()
        
        if (result.success && result.userInfo) {
          // 这里应该创建微信用户并迁移数据
          // 为了演示，我们直接显示成功
          setMigrationStatus('success')
          setMessage('微信绑定成功！')
          onSuccess?.()
        } else {
          setMigrationStatus('error')
          setMessage(result.error || '微信绑定失败')
        }
      } else {
        // 真实微信登录
        wechatService.redirectToWeChatAuth()
      }
    } catch {
      setMigrationStatus('error')
      setMessage('微信绑定失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-2xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <UserPlus className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-bold text-gray-900">绑定账户</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {migrationStatus === 'success' ? (
            // 成功状态
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">绑定成功！</h3>
              <p className="text-gray-600 mb-4">
                您的访客数据已成功迁移到正式账户，现在可以永久保存您的作品了。
              </p>
              <div className="flex items-center justify-center">
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                <span className="text-sm text-gray-500">正在跳转...</span>
              </div>
            </div>
          ) : (
            <>
              {/* 升级说明 */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">保存您的作品</h3>
                <p className="text-gray-600 text-sm">
                  绑定手机号或微信账户，将访客数据迁移到正式账户，永久保存您的回忆录作品。
                </p>
              </div>

              {/* 升级方式选择 */}
              <div className="mb-6">
                <div className="flex bg-gray-100 rounded-lg p-1">
                  <button
                    type="button"
                    onClick={() => setUpgradeMethod('phone')}
                    className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors flex items-center justify-center space-x-2 ${
                      upgradeMethod === 'phone'
                        ? 'bg-white text-blue-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <Phone className="w-4 h-4" />
                    <span>手机号</span>
                  </button>
                  <button
                    type="button"
                    onClick={() => setUpgradeMethod('wechat')}
                    className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors flex items-center justify-center space-x-2 ${
                      upgradeMethod === 'wechat'
                        ? 'bg-white text-green-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <MessageSquare className="w-4 h-4" />
                    <span>微信</span>
                  </button>
                </div>
              </div>

              {/* 手机号绑定 */}
              {upgradeMethod === 'phone' && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      手机号
                    </label>
                    <input
                      type="tel"
                      value={phone}
                      onChange={(e) => setPhone(formatPhoneNumber(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="请输入手机号"
                      maxLength={13}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      验证码
                    </label>
                    <div className="flex space-x-3">
                      <input
                        type="text"
                        value={verificationCode}
                        onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="6位验证码"
                        maxLength={6}
                      />
                      <button
                        type="button"
                        onClick={handleSendSMS}
                        disabled={loading || !codeState.canResend || !validateChinesePhoneNumber(phone)}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors whitespace-nowrap text-sm"
                      >
                        {codeState.sent && !codeState.canResend 
                          ? `${codeState.countdown}s` 
                          : codeState.sent 
                            ? '重新发送' 
                            : '获取验证码'
                        }
                      </button>
                    </div>
                  </div>

                  <button
                    onClick={handlePhoneUpgrade}
                    disabled={loading || !validateChinesePhoneNumber(phone) || verificationCode.length !== 6}
                    className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {loading ? (
                      <div className="flex items-center justify-center">
                        <Loader2 className="animate-spin h-4 w-4 mr-2" />
                        {migrationStatus === 'migrating' ? '正在迁移数据...' : '验证中...'}
                      </div>
                    ) : (
                      '绑定手机号'
                    )}
                  </button>
                </div>
              )}

              {/* 微信绑定 */}
              {upgradeMethod === 'wechat' && (
                <div className="space-y-4">
                  <div className="text-center py-4">
                    <MessageSquare className="w-12 h-12 text-green-500 mx-auto mb-3" />
                    <p className="text-gray-600 text-sm mb-4">
                      {isWeChatBrowser() 
                        ? '点击下方按钮绑定微信账户' 
                        : '请在微信中打开此页面进行绑定'
                      }
                    </p>
                  </div>

                  <button
                    onClick={handleWeChatUpgrade}
                    disabled={loading || (!isWeChatBrowser() && process.env.NODE_ENV === 'production')}
                    className="w-full bg-green-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {loading ? (
                      <div className="flex items-center justify-center">
                        <Loader2 className="animate-spin h-4 w-4 mr-2" />
                        {migrationStatus === 'migrating' ? '正在迁移数据...' : '绑定中...'}
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <MessageSquare className="w-4 h-4 mr-2" />
                        {process.env.NODE_ENV === 'development' ? '绑定微信（演示）' : '绑定微信'}
                      </div>
                    )}
                  </button>
                </div>
              )}

              {/* 消息显示 */}
              {message && (
                <div className={`mt-4 p-3 rounded-lg text-sm ${
                  message.includes('成功') 
                    ? 'bg-green-50 text-green-700 border border-green-200' 
                    : 'bg-red-50 text-red-700 border border-red-200'
                }`}>
                  {message}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  )
}
