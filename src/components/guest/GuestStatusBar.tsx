'use client'

import { useState, useEffect } from 'react'
import { Clock, UserPlus, X } from 'lucide-react'
import { GuestAuthService, type GuestUser } from '@/lib/guest-auth'

interface GuestStatusBarProps {
  onUpgrade?: () => void
  onDismiss?: () => void
}

export default function GuestStatusBar({ onUpgrade, onDismiss }: GuestStatusBarProps) {
  const [guestUser, setGuestUser] = useState<GuestUser | null>(null)
  const [dismissed, setDismissed] = useState(false)
  const [remainingDays, setRemainingDays] = useState(0)

  useEffect(() => {
    const checkGuestStatus = () => {
      const currentGuest = GuestAuthService.getCurrentGuestUser()
      if (currentGuest && !GuestAuthService.isGuestUserExpired(currentGuest)) {
        setGuestUser(currentGuest)
        setRemainingDays(GuestAuthService.getRemainingDays(currentGuest))
      }
    }

    checkGuestStatus()
    
    // 每分钟检查一次状态
    const interval = setInterval(checkGuestStatus, 60000)
    
    return () => clearInterval(interval)
  }, [])

  const handleDismiss = () => {
    setDismissed(true)
    onDismiss?.()
  }

  const handleUpgrade = () => {
    onUpgrade?.()
  }

  // 如果不是访客用户或已被关闭，不显示
  if (!guestUser || dismissed) {
    return null
  }

  // 根据剩余天数选择不同的样式
  const getStatusStyle = () => {
    if (remainingDays <= 1) {
      return 'bg-red-50 border-red-200 text-red-800'
    } else if (remainingDays <= 3) {
      return 'bg-orange-50 border-orange-200 text-orange-800'
    } else {
      return 'bg-blue-50 border-blue-200 text-blue-800'
    }
  }

  const getIconColor = () => {
    if (remainingDays <= 1) {
      return 'text-red-600'
    } else if (remainingDays <= 3) {
      return 'text-orange-600'
    } else {
      return 'text-blue-600'
    }
  }

  const getUrgencyMessage = () => {
    if (remainingDays <= 1) {
      return '访客数据即将过期'
    } else if (remainingDays <= 3) {
      return '访客数据即将过期'
    } else {
      return '访客模式体验中'
    }
  }

  return (
    <div className={`border-b ${getStatusStyle()}`}>
      <div className="max-w-4xl mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Clock className={`w-5 h-5 ${getIconColor()}`} />
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <span className="font-medium text-sm">
                  {getUrgencyMessage()}
                </span>
                <span className="text-xs opacity-75">
                  剩余 {remainingDays} 天
                </span>
              </div>
              <p className="text-xs opacity-75 mt-1">
                绑定手机或微信，永久保存您的作品
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleUpgrade}
              className={`flex items-center space-x-1 px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
                remainingDays <= 1
                  ? 'bg-red-600 text-white hover:bg-red-700'
                  : remainingDays <= 3
                    ? 'bg-orange-600 text-white hover:bg-orange-700'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              <UserPlus className="w-3 h-3" />
              <span>立即绑定</span>
            </button>
            
            <button
              onClick={handleDismiss}
              className="p-1 hover:bg-black/10 rounded transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

// 访客限制提示组件
interface GuestLimitNoticeProps {
  type: 'memoir' | 'chapter' | 'content'
  currentCount: number
  maxCount: number
  onUpgrade?: () => void
}

export function GuestLimitNotice({ type, currentCount, maxCount, onUpgrade }: GuestLimitNoticeProps) {
  const getTypeText = () => {
    switch (type) {
      case 'memoir':
        return '回忆录'
      case 'chapter':
        return '章节'
      case 'content':
        return '内容块'
      default:
        return '项目'
    }
  }

  const isAtLimit = currentCount >= maxCount

  if (!isAtLimit) {
    return (
      <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4 text-orange-600" />
            <span className="text-sm text-orange-800">
              访客模式：已创建 {currentCount}/{maxCount} 个{getTypeText()}
            </span>
          </div>
          {onUpgrade && (
            <button
              onClick={onUpgrade}
              className="text-xs text-orange-600 hover:text-orange-700 font-medium"
            >
              升级账户
            </button>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
      <div className="flex items-start space-x-3">
        <Clock className="w-5 h-5 text-red-600 mt-0.5" />
        <div className="flex-1">
          <h4 className="text-sm font-medium text-red-800 mb-1">
            访客模式限制
          </h4>
          <p className="text-sm text-red-700 mb-3">
            您已达到访客模式的{getTypeText()}创建上限（{maxCount}个）。
            绑定手机或微信账户即可解除限制。
          </p>
          {onUpgrade && (
            <button
              onClick={onUpgrade}
              className="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 transition-colors"
            >
              立即绑定账户
            </button>
          )}
        </div>
      </div>
    </div>
  )
}
