'use client'

import { useState, useRef, useEffect } from 'react'
import { Mi<PERSON>, Mic<PERSON>ff, Edit3, Check, X } from 'lucide-react'

interface PhotoCaptionEditorProps {
  photoId: string
  caption: string
  onCaptionUpdate: (photoId: string, caption: string) => void
  className?: string
}

export default function PhotoCaptionEditor({
  photoId,
  caption,
  onCaptionUpdate,
  className = ''
}: PhotoCaptionEditorProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editingCaption, setEditingCaption] = useState(caption)
  const [isRecording, setIsRecording] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    setEditingCaption(caption)
  }, [caption])

  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus()
      textareaRef.current.select()
    }
  }, [isEditing])

  const handleSave = () => {
    onCaptionUpdate(photoId, editingCaption.trim())
    setIsEditing(false)
  }

  const handleCancel = () => {
    setEditingCaption(caption)
    setIsEditing(false)
  }

  const startVoiceRecording = async () => {
    try {
      // 模拟录音开始
      setIsRecording(true)

      // 延迟2秒后模拟录音结束并获取转写结果
      setTimeout(async () => {
        const mockTranscription = await simulateVoiceToText()

        if (mockTranscription) {
          const newCaption = editingCaption ?
            `${editingCaption} ${mockTranscription}` :
            mockTranscription
          setEditingCaption(newCaption)
        }

        setIsRecording(false)
      }, 2000)
    } catch (error) {
      console.error('Failed to start recording:', error)
      alert('无法启动录音，请检查麦克风权限')
    }
  }

  const stopVoiceRecording = () => {
    // 立即停止录音
    setIsRecording(false)
  }

  // 模拟语音转文字功能
  const simulateVoiceToText = async (): Promise<string> => {
    // 这里应该调用实际的语音转文字API
    // 暂时返回模拟结果
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve('这是一张美丽的照片')
      }, 1000)
    })
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSave()
    } else if (e.key === 'Escape') {
      handleCancel()
    }
  }

  if (!isEditing) {
    return (
      <div className={`group ${className}`}>
        <div 
          onClick={() => setIsEditing(true)}
          className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
        >
          <p className={`text-sm flex-1 ${caption ? 'text-gray-700' : 'text-gray-400 italic'}`}>
            {caption || '点击添加照片说明...'}
          </p>
          <Edit3 className="w-4 h-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity ml-2" />
        </div>
      </div>
    )
  }

  return (
    <div className={`${className}`}>
      <div className="space-y-2">
        <textarea
          ref={textareaRef}
          value={editingCaption}
          onChange={(e) => setEditingCaption(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="为这张照片添加说明..."
          className="w-full p-2 text-sm border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          rows={2}
        />
        
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <button
              onClick={isRecording ? stopVoiceRecording : startVoiceRecording}
              className={`p-2 rounded-lg transition-colors ${
                isRecording 
                  ? 'bg-red-100 text-red-600 hover:bg-red-200' 
                  : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
              }`}
              title={isRecording ? '停止录音' : '语音输入'}
            >
              {isRecording ? (
                <MicOff className="w-4 h-4" />
              ) : (
                <Mic className="w-4 h-4" />
              )}
            </button>
            {isRecording && (
              <span className="text-xs text-red-600 animate-pulse">
                正在录音...
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleCancel}
              className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              title="取消"
            >
              <X className="w-4 h-4" />
            </button>
            <button
              onClick={handleSave}
              className="p-2 text-green-600 hover:bg-green-100 rounded-lg transition-colors"
              title="保存"
            >
              <Check className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
