'use client'

import { useState, useRef, useCallback } from 'react'
import Image from 'next/image'
import { 
  Upload, 
  Image as ImageIcon, 
  X, 
  Plus,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardContent } from '@/components/ui/Card'
import { PhotoUploadService, type ProcessedPhoto, type UploadProgress } from '@/lib/photo-upload'
import { cn } from '@/lib/utils'

export interface PhotoUploaderProps {
  readonly onPhotosUploaded: (photos: ProcessedPhoto[]) => void
  readonly maxFiles?: number
  readonly multiple?: boolean
  readonly disabled?: boolean
  readonly className?: string
}

export default function PhotoUploader({
  onPhotosUploaded,
  maxFiles = 10,
  multiple = true,
  disabled = false,
  className
}: PhotoUploaderProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [processedPhotos, setProcessedPhotos] = useState<ProcessedPhoto[]>([])
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const dragCounter = useRef(0)

  // 处理文件选择
  const handleFileSelect = useCallback(async (files: FileList | File[]) => {
    if (disabled || isProcessing) return

    setError(null)
    setIsProcessing(true)
    setUploadProgress({ loaded: 0, total: Array.from(files).length, percentage: 0 })

    try {
      const newPhotos = await PhotoUploadService.processPhotos(
        files,
        {
          maxFileSize: 10 * 1024 * 1024, // 10MB
          quality: 0.8,
          maxWidth: 1920,
          maxHeight: 1080
        },
        (progress) => {
          setUploadProgress(progress)
        }
      )

      // 检查是否超出最大文件数
      const totalPhotos = processedPhotos.length + newPhotos.length
      if (totalPhotos > maxFiles) {
        setError(`最多只能上传 ${maxFiles} 张照片`)
        setIsProcessing(false)
        setUploadProgress(null)
        return
      }

      const updatedPhotos = [...processedPhotos, ...newPhotos]
      setProcessedPhotos(updatedPhotos)
      onPhotosUploaded(updatedPhotos)
    } catch (err) {
      setError(err instanceof Error ? err.message : '照片处理失败')
    } finally {
      setIsProcessing(false)
      setUploadProgress(null)
    }
  }, [disabled, isProcessing, processedPhotos, maxFiles, onPhotosUploaded])

  // 处理文件输入变化
  const handleInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files && files.length > 0) {
      handleFileSelect(files)
    }
    // 清空input值，允许重复选择同一文件
    event.target.value = ''
  }, [handleFileSelect])

  // 处理拖拽进入
  const handleDragEnter = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    event.stopPropagation()
    dragCounter.current++
    if (event.dataTransfer.items && event.dataTransfer.items.length > 0) {
      setIsDragging(true)
    }
  }, [])

  // 处理拖拽离开
  const handleDragLeave = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    event.stopPropagation()
    dragCounter.current--
    if (dragCounter.current === 0) {
      setIsDragging(false)
    }
  }, [])

  // 处理拖拽悬停
  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    event.stopPropagation()
  }, [])

  // 处理文件放置
  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    event.stopPropagation()
    setIsDragging(false)
    dragCounter.current = 0

    if (disabled || isProcessing) return

    const files = event.dataTransfer.files
    if (files && files.length > 0) {
      handleFileSelect(files)
    }
  }, [disabled, isProcessing, handleFileSelect])

  // 打开文件选择器
  const openFileSelector = useCallback(() => {
    if (disabled || isProcessing) return
    fileInputRef.current?.click()
  }, [disabled, isProcessing])

  // 移除照片
  const removePhoto = useCallback((index: number) => {
    const photo = processedPhotos[index]
    if (photo) {
      PhotoUploadService.revokeObjectURL(photo.url)
      if (photo.thumbnail) {
        PhotoUploadService.revokeObjectURL(photo.thumbnail)
      }
    }

    const updatedPhotos = processedPhotos.filter((_, i) => i !== index)
    setProcessedPhotos(updatedPhotos)
    onPhotosUploaded(updatedPhotos)
  }, [processedPhotos, onPhotosUploaded])

  // 清空所有照片
  const clearAllPhotos = useCallback(() => {
    processedPhotos.forEach(photo => {
      PhotoUploadService.revokeObjectURL(photo.url)
      if (photo.thumbnail) {
        PhotoUploadService.revokeObjectURL(photo.thumbnail)
      }
    })
    setProcessedPhotos([])
    onPhotosUploaded([])
  }, [processedPhotos, onPhotosUploaded])

  if (!PhotoUploadService.isSupported()) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">不支持照片上传</h3>
          <p className="text-gray-600">您的浏览器不支持照片上传功能</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* 上传区域 */}
      <Card
        className={cn(
          'border-2 border-dashed transition-all duration-200 cursor-pointer',
          isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400',
          disabled && 'opacity-50 cursor-not-allowed'
        )}
        onClick={openFileSelector}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        <CardContent className="p-12 text-center">
          {isProcessing ? (
            <div className="space-y-4">
              <Loader2 className="w-12 h-12 text-blue-600 mx-auto animate-spin" />
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">处理照片中...</h3>
                {uploadProgress && (
                  <div className="space-y-2">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${uploadProgress.percentage}%` }}
                      />
                    </div>
                    <p className="text-sm text-gray-600">
                      {uploadProgress.loaded} / {uploadProgress.total} ({uploadProgress.percentage}%)
                    </p>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex justify-center">
                {isDragging ? (
                  <Upload className="w-16 h-16 text-blue-600" />
                ) : (
                  <ImageIcon className="w-16 h-16 text-gray-400" />
                )}
              </div>
              
              <div>
                <h3 className="text-xl font-medium text-gray-900 mb-2">
                  {isDragging ? '放置照片到这里' : '上传照片'}
                </h3>
                <p className="text-gray-600 mb-4">
                  拖拽照片到这里，或点击选择文件
                </p>
                <p className="text-sm text-gray-500">
                  支持 JPG、PNG、WebP 格式，最大 10MB
                  {multiple && ` • 最多 ${maxFiles} 张`}
                </p>
              </div>
              
              <Button
                variant="outline"
                size="lg"
                disabled={disabled}
                icon={<Plus className="w-5 h-5" />}
              >
                选择照片
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        multiple={multiple}
        onChange={handleInputChange}
        className="hidden"
        disabled={disabled}
      />

      {/* 错误提示 */}
      {error && (
        <Card variant="outlined">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2 text-red-600">
              <AlertCircle className="w-5 h-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 照片预览网格 */}
      {processedPhotos.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">
              已选择照片 ({processedPhotos.length}/{maxFiles})
            </h3>
            <Button
              variant="outline"
              size="sm"
              onClick={clearAllPhotos}
              disabled={disabled}
            >
              清空全部
            </Button>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {processedPhotos.map((photo, index) => (
              <Card key={index} className="relative group">
                <CardContent className="p-2">
                  <div className="aspect-square relative overflow-hidden rounded-lg">
                    <Image
                      src={photo.thumbnail || photo.url}
                      alt={photo.metadata.originalName}
                      width={200}
                      height={200}
                      className="w-full h-full object-cover"
                    />
                    
                    {/* 移除按钮 */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        removePhoto(index)
                      }}
                      className="absolute top-2 right-2 w-6 h-6 bg-red-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                      disabled={disabled}
                    >
                      <X className="w-4 h-4" />
                    </button>
                    
                    {/* 成功标识 */}
                    <div className="absolute bottom-2 right-2">
                      <CheckCircle className="w-5 h-5 text-green-600 bg-white rounded-full" />
                    </div>
                  </div>
                  
                  {/* 文件信息 */}
                  <div className="mt-2 text-xs text-gray-600 truncate">
                    {photo.metadata.originalName}
                  </div>
                  <div className="text-xs text-gray-500">
                    {Math.round(photo.metadata.size / 1024)}KB • 
                    {photo.metadata.dimensions.width}×{photo.metadata.dimensions.height}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
