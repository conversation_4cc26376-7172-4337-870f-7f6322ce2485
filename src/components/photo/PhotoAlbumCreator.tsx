'use client'

import { useState } from 'react'
import Image from 'next/image'
import {
  ArrowLeft,
  ArrowRight,
  Save,
  Eye,
  BookOpen
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import PhotoUploader from './PhotoUploader'
import PhotoNarrator from './PhotoNarrator'
import AlbumTemplateSelector, { type AlbumTemplate } from './AlbumTemplateSelector'
import { type ProcessedPhoto } from '@/lib/photo-upload'
import { type PhotoNarration } from './PhotoNarrator'
import { type PhotoAlbum } from '@/types/memoir'

export interface PhotoAlbumCreatorProps {
  readonly onSaveAlbum: (album: Partial<PhotoAlbum>) => void
  readonly onCancel?: () => void
  readonly initialAlbum?: Partial<PhotoAlbum>
  readonly memoirId?: string
  readonly chapterId?: string
}

type Step = 'template' | 'upload' | 'narrate' | 'preview'

export default function PhotoAlbumCreator({
  onSaveAlbum,
  onCancel,
  initialAlbum,
  memoirId,
  chapterId
}: PhotoAlbumCreatorProps) {
  const [currentStep, setCurrentStep] = useState<Step>('template')
  const [albumTitle, setAlbumTitle] = useState(initialAlbum?.title || '')
  const [albumDescription, setAlbumDescription] = useState(initialAlbum?.description || '')
  const [selectedTemplate, setSelectedTemplate] = useState<AlbumTemplate | undefined>()
  const [photos, setPhotos] = useState<ProcessedPhoto[]>([])
  const [narrations, setNarrations] = useState<PhotoNarration[]>([])

  const steps: { key: Step; title: string; description: string }[] = [
    { key: 'template', title: '选择模板', description: '选择相册的展示风格' },
    { key: 'upload', title: '上传照片', description: '添加您的珍贵照片' },
    { key: 'narrate', title: '添加叙述', description: '为照片添加语音和文字说明' },
    { key: 'preview', title: '预览保存', description: '预览相册并保存' }
  ]

  const currentStepIndex = steps.findIndex(step => step.key === currentStep)
  const canGoNext = () => {
    switch (currentStep) {
      case 'template':
        return !!selectedTemplate
      case 'upload':
        return photos.length > 0
      case 'narrate':
        return true // 叙述是可选的
      case 'preview':
        return albumTitle.trim().length > 0
      default:
        return false
    }
  }

  const canGoPrevious = () => currentStepIndex > 0

  const handleNext = () => {
    if (canGoNext() && currentStepIndex < steps.length - 1) {
      setCurrentStep(steps[currentStepIndex + 1].key)
    }
  }

  const handlePrevious = () => {
    if (canGoPrevious()) {
      setCurrentStep(steps[currentStepIndex - 1].key)
    }
  }

  const handleSave = () => {
    if (!albumTitle.trim() || !selectedTemplate) return

    const album: Partial<PhotoAlbum> = {
      title: albumTitle.trim(),
      description: albumDescription.trim(),
      memoirId,
      chapterId,
      photos: photos.map((photo, index) => {
        const narration = narrations.find(n => n.photoIndex === index)
        return {
          id: `photo_${Date.now()}_${index}`,
          url: photo.url,
          thumbnailUrl: photo.thumbnail,
          caption: narration?.caption || '',
          description: narration?.description || '',
          order: index,
          createdAt: new Date().toISOString(),
          metadata: {
            originalName: photo.metadata.originalName,
            size: photo.metadata.size,
            dimensions: photo.metadata.dimensions,
            audioNarration: narration?.audioUrl
          }
        }
      }),
      status: 'completed',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    onSaveAlbum(album)
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 'template':
        return (
          <AlbumTemplateSelector
            onSelectTemplate={setSelectedTemplate}
            selectedTemplate={selectedTemplate}
          />
        )
      
      case 'upload':
        return (
          <PhotoUploader
            onPhotosUploaded={setPhotos}
            maxFiles={50}
            multiple={true}
          />
        )
      
      case 'narrate':
        return (
          <PhotoNarrator
            photos={photos}
            onNarrationSave={setNarrations}
            initialNarrations={narrations}
          />
        )
      
      case 'preview':
        return (
          <div className="space-y-6">
            {/* 相册信息编辑 */}
            <Card>
              <CardHeader>
                <CardTitle>相册信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  label="相册标题"
                  value={albumTitle}
                  onChange={(e) => setAlbumTitle(e.target.value)}
                  placeholder="为您的相册起一个名字..."
                  required
                />
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    相册描述
                  </label>
                  <textarea
                    value={albumDescription}
                    onChange={(e) => setAlbumDescription(e.target.value)}
                    placeholder="描述这个相册的主题或背景..."
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
                  />
                </div>
              </CardContent>
            </Card>

            {/* 相册预览 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Eye className="w-5 h-5" />
                  <span>相册预览</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* 模板信息 */}
                  {selectedTemplate && (
                    <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                      {selectedTemplate.icon}
                      <div>
                        <div className="font-medium text-blue-900">{selectedTemplate.name}</div>
                        <div className="text-sm text-blue-700">{selectedTemplate.description}</div>
                      </div>
                    </div>
                  )}

                  {/* 照片网格预览 */}
                  <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                    {photos.map((photo, index) => {
                      const narration = narrations.find(n => n.photoIndex === index)
                      return (
                        <div key={index} className="relative group">
                          <div className="aspect-square overflow-hidden rounded-lg">
                            <Image
                              src={photo.thumbnail || photo.url}
                              alt={narration?.caption || `照片 ${index + 1}`}
                              width={200}
                              height={200}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          
                          {/* 叙述指示器 */}
                          {narration && (
                            <div className="absolute top-2 right-2 flex space-x-1">
                              {narration.caption && (
                                <div className="w-2 h-2 bg-blue-500 rounded-full" />
                              )}
                              {narration.audioUrl && (
                                <div className="w-2 h-2 bg-green-500 rounded-full" />
                              )}
                            </div>
                          )}
                          
                          {/* 悬停显示标题 */}
                          {narration?.caption && (
                            <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                              {narration.caption}
                            </div>
                          )}
                        </div>
                      )
                    })}
                  </div>

                  {/* 统计信息 */}
                  <div className="flex items-center justify-between text-sm text-gray-600 pt-4 border-t border-gray-200">
                    <div className="flex items-center space-x-4">
                      <span>📸 {photos.length} 张照片</span>
                      <span>📝 {narrations.filter(n => n.caption).length} 个标题</span>
                      <span>🎤 {narrations.filter(n => n.audioUrl).length} 段录音</span>
                    </div>
                    <div className="text-gray-500">
                      创建时间：{new Date().toLocaleDateString('zh-CN')}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )
      
      default:
        return null
    }
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* 头部 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-3">
            <BookOpen className="w-6 h-6 text-blue-600" />
            <span>创建照片相册</span>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* 步骤指示器 */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.key} className="flex items-center">
                <div className="flex flex-col items-center">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-200 ${
                    index <= currentStepIndex
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {index + 1}
                  </div>
                  <div className="mt-2 text-center">
                    <div className={`text-sm font-medium ${
                      index <= currentStepIndex ? 'text-blue-600' : 'text-gray-600'
                    }`}>
                      {step.title}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {step.description}
                    </div>
                  </div>
                </div>
                
                {index < steps.length - 1 && (
                  <div className={`flex-1 h-0.5 mx-4 ${
                    index < currentStepIndex ? 'bg-blue-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 步骤内容 */}
      <div className="min-h-[600px]">
        {renderStepContent()}
      </div>

      {/* 导航按钮 */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex space-x-3">
              {onCancel && (
                <Button
                  variant="outline"
                  onClick={onCancel}
                >
                  取消
                </Button>
              )}
              
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={!canGoPrevious()}
                icon={<ArrowLeft className="w-5 h-5" />}
              >
                上一步
              </Button>
            </div>
            
            <div className="flex space-x-3">
              {currentStep === 'preview' ? (
                <Button
                  onClick={handleSave}
                  disabled={!canGoNext()}
                  icon={<Save className="w-5 h-5" />}
                >
                  保存相册
                </Button>
              ) : (
                <Button
                  onClick={handleNext}
                  disabled={!canGoNext()}
                  icon={<ArrowRight className="w-5 h-5" />}
                  iconPosition="right"
                >
                  下一步
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
