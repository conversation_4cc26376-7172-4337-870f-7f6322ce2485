'use client'

import { useState, useRef } from 'react'
import Image from 'next/image'
import {
  Save,
  Trash2,
  Volume2,
  Edit3,
  CheckCircle
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { VoiceButton } from '@/components/ui/VoiceButton'
import { VoiceRecordingService, type VoiceRecordingResult } from '@/lib/voice-recording'
import { type ProcessedPhoto } from '@/lib/photo-upload'
import { cn } from '@/lib/utils'

export interface PhotoNarration {
  readonly photoIndex: number
  readonly caption: string
  readonly description: string
  readonly audioUrl?: string
  readonly timestamp: string
}

export interface PhotoNarratorProps {
  readonly photos: ProcessedPhoto[]
  readonly onNarrationSave: (narrations: PhotoNarration[]) => void
  readonly initialNarrations?: PhotoNarration[]
  readonly disabled?: boolean
}

export default function PhotoNarrator({
  photos,
  onNarrationSave,
  initialNarrations = [],
  disabled = false
}: PhotoNarratorProps) {
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0)
  const [narrations, setNarrations] = useState<PhotoNarration[]>(initialNarrations)
  const [isRecording, setIsRecording] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [recordingResult, setRecordingResult] = useState<VoiceRecordingResult | null>(null)
  const [editingCaption, setEditingCaption] = useState(false)
  const [editingDescription, setEditingDescription] = useState(false)

  const voiceService = useRef<VoiceRecordingService | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)

  // 初始化语音服务
  if (!voiceService.current) {
    voiceService.current = new VoiceRecordingService()
  }

  const currentPhoto = photos[currentPhotoIndex]
  const currentNarration = narrations.find(n => n.photoIndex === currentPhotoIndex)

  // 开始录制
  const handleStartRecording = async () => {
    try {
      await voiceService.current?.startRecording()
      setIsRecording(true)
      setRecordingResult(null)
    } catch (error) {
      console.error('录制启动失败:', error)
    }
  }

  // 停止录制
  const handleStopRecording = async () => {
    try {
      const result = await voiceService.current?.stopRecording()
      if (result) {
        setRecordingResult(result)
      }
      setIsRecording(false)
    } catch (error) {
      console.error('录制停止失败:', error)
    }
  }

  // 播放录音
  const handlePlayRecording = () => {
    const audioUrl = recordingResult?.audioUrl || currentNarration?.audioUrl
    if (!audioUrl) return

    if (audioRef.current) {
      audioRef.current.pause()
      audioRef.current = null
    }

    audioRef.current = new Audio(audioUrl)
    audioRef.current.play()
    setIsPlaying(true)

    audioRef.current.onended = () => {
      setIsPlaying(false)
    }
  }

  // 暂停播放
  const handlePausePlayback = () => {
    if (audioRef.current) {
      audioRef.current.pause()
      setIsPlaying(false)
    }
  }

  // 保存叙述
  const handleSaveNarration = (caption: string, description: string) => {
    const newNarration: PhotoNarration = {
      photoIndex: currentPhotoIndex,
      caption: caption.trim(),
      description: description.trim(),
      audioUrl: recordingResult?.audioUrl || currentNarration?.audioUrl,
      timestamp: new Date().toISOString()
    }

    const updatedNarrations = narrations.filter(n => n.photoIndex !== currentPhotoIndex)
    updatedNarrations.push(newNarration)
    
    setNarrations(updatedNarrations)
    onNarrationSave(updatedNarrations)
    setRecordingResult(null)
    setEditingCaption(false)
    setEditingDescription(false)
  }

  // 删除叙述
  const handleDeleteNarration = () => {
    const updatedNarrations = narrations.filter(n => n.photoIndex !== currentPhotoIndex)
    setNarrations(updatedNarrations)
    onNarrationSave(updatedNarrations)
    setRecordingResult(null)
  }

  // 切换到下一张照片
  const goToNextPhoto = () => {
    if (currentPhotoIndex < photos.length - 1) {
      setCurrentPhotoIndex(currentPhotoIndex + 1)
      setRecordingResult(null)
      setEditingCaption(false)
      setEditingDescription(false)
    }
  }

  // 切换到上一张照片
  const goToPreviousPhoto = () => {
    if (currentPhotoIndex > 0) {
      setCurrentPhotoIndex(currentPhotoIndex - 1)
      setRecordingResult(null)
      setEditingCaption(false)
      setEditingDescription(false)
    }
  }

  if (photos.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <Volume2 className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无照片</h3>
          <p className="text-gray-600">请先上传照片，然后为它们添加语音叙述</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* 照片导航 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>照片叙述</span>
            <span className="text-sm font-normal text-gray-600">
              {currentPhotoIndex + 1} / {photos.length}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* 照片缩略图导航 */}
          <div className="flex space-x-2 overflow-x-auto pb-2">
            {photos.map((photo, index) => (
              <button
                key={index}
                onClick={() => setCurrentPhotoIndex(index)}
                className={cn(
                  'flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200',
                  index === currentPhotoIndex 
                    ? 'border-blue-500 ring-2 ring-blue-200' 
                    : 'border-gray-200 hover:border-gray-300'
                )}
                disabled={disabled}
              >
                <Image
                  src={photo.thumbnail || photo.url}
                  alt={`照片 ${index + 1}`}
                  width={64}
                  height={64}
                  className="w-full h-full object-cover"
                />
                {/* 叙述完成标识 */}
                {narrations.some(n => n.photoIndex === index) && (
                  <div className="absolute -top-1 -right-1">
                    <CheckCircle className="w-4 h-4 text-green-600 bg-white rounded-full" />
                  </div>
                )}
              </button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 当前照片展示 */}
      <Card>
        <CardContent className="p-6">
          <div className="aspect-video relative overflow-hidden rounded-lg bg-gray-100 mb-6">
            <Image
              src={currentPhoto.url}
              alt={currentPhoto.metadata.originalName}
              width={800}
              height={450}
              className="w-full h-full object-contain"
            />
          </div>

          {/* 照片信息 */}
          <div className="text-sm text-gray-600 mb-4">
            <p>文件名：{currentPhoto.metadata.originalName}</p>
            <p>尺寸：{currentPhoto.metadata.dimensions.width} × {currentPhoto.metadata.dimensions.height}</p>
            <p>大小：{Math.round(currentPhoto.metadata.size / 1024)} KB</p>
          </div>

          {/* 导航按钮 */}
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={goToPreviousPhoto}
              disabled={currentPhotoIndex === 0 || disabled}
            >
              上一张
            </Button>
            
            <Button
              variant="outline"
              onClick={goToNextPhoto}
              disabled={currentPhotoIndex === photos.length - 1 || disabled}
            >
              下一张
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 叙述编辑区域 */}
      <Card>
        <CardHeader>
          <CardTitle>为照片添加叙述</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 标题编辑 */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium text-gray-700">照片标题</label>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setEditingCaption(!editingCaption)}
                icon={<Edit3 className="w-4 h-4" />}
              >
                {editingCaption ? '完成' : '编辑'}
              </Button>
            </div>
            
            {editingCaption ? (
              <Input
                defaultValue={currentNarration?.caption || ''}
                placeholder="为这张照片添加一个标题..."
                onBlur={(e) => {
                  if (e.target.value.trim()) {
                    handleSaveNarration(
                      e.target.value,
                      currentNarration?.description || ''
                    )
                  }
                }}
                disabled={disabled}
              />
            ) : (
              <div className="min-h-[56px] p-4 border border-gray-300 rounded-lg bg-gray-50 flex items-center">
                <span className={cn(
                  'text-lg',
                  currentNarration?.caption ? 'text-gray-900' : 'text-gray-400'
                )}>
                  {currentNarration?.caption || '点击编辑添加标题...'}
                </span>
              </div>
            )}
          </div>

          {/* 描述编辑 */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium text-gray-700">照片描述</label>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setEditingDescription(!editingDescription)}
                icon={<Edit3 className="w-4 h-4" />}
              >
                {editingDescription ? '完成' : '编辑'}
              </Button>
            </div>
            
            {editingDescription ? (
              <textarea
                defaultValue={currentNarration?.description || ''}
                placeholder="描述这张照片的故事、背景或回忆..."
                rows={4}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
                onBlur={(e) => {
                  if (e.target.value.trim()) {
                    handleSaveNarration(
                      currentNarration?.caption || '',
                      e.target.value
                    )
                  }
                }}
                disabled={disabled}
              />
            ) : (
              <div className="min-h-[120px] p-4 border border-gray-300 rounded-lg bg-gray-50">
                <span className={cn(
                  'text-lg leading-relaxed whitespace-pre-wrap',
                  currentNarration?.description ? 'text-gray-900' : 'text-gray-400'
                )}>
                  {currentNarration?.description || '点击编辑添加描述...'}
                </span>
              </div>
            )}
          </div>

          {/* 语音录制区域 */}
          <div className="text-center">
            <VoiceButton
              onStartRecording={handleStartRecording}
              onStopRecording={handleStopRecording}
              onPlayback={handlePlayRecording}
              onPausePlayback={handlePausePlayback}
              isRecording={isRecording}
              isPlaying={isPlaying}
              hasRecording={!!(recordingResult || currentNarration?.audioUrl)}
              disabled={disabled}
              size="lg"
            />
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={handleDeleteNarration}
              disabled={!currentNarration || disabled}
              icon={<Trash2 className="w-5 h-5" />}
            >
              删除叙述
            </Button>
            
            <Button
              onClick={() => handleSaveNarration(
                currentNarration?.caption || '',
                currentNarration?.description || ''
              )}
              disabled={!recordingResult || disabled}
              icon={<Save className="w-5 h-5" />}
            >
              保存录音
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
