'use client'

import { useState } from 'react'
import {
  Layout,
  Calendar,
  Heart,
  MapPin,
  Users,
  Camera,
  Star,
  CheckCircle
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { cn } from '@/lib/utils'

export interface AlbumTemplate {
  readonly id: string
  readonly name: string
  readonly description: string
  readonly icon: React.ReactNode
  readonly layout: 'grid' | 'timeline' | 'story' | 'collage'
  readonly features: string[]
  readonly preview: string
  readonly category: 'family' | 'travel' | 'life' | 'special'
}

export interface AlbumTemplateProps {
  readonly onSelectTemplate: (template: AlbumTemplate) => void
  readonly selectedTemplate?: AlbumTemplate
  readonly disabled?: boolean
}

const ALBUM_TEMPLATES: AlbumTemplate[] = [
  {
    id: 'family-memories',
    name: '家庭回忆',
    description: '温馨的家庭照片集，按时间线展示珍贵时刻',
    icon: <Heart className="w-6 h-6" />,
    layout: 'timeline',
    features: ['时间线布局', '家庭标签', '温馨配色', '语音叙述'],
    preview: '📸 家庭聚会 → 📸 生日庆祝 → 📸 节日时光',
    category: 'family'
  },
  {
    id: 'travel-journey',
    name: '旅行足迹',
    description: '记录旅行中的美好瞬间和难忘经历',
    icon: <MapPin className="w-6 h-6" />,
    layout: 'story',
    features: ['地图标记', '行程记录', '风景展示', '游记配文'],
    preview: '🗺️ 出发 → 📍 景点 → 🏨 住宿 → 🍽️ 美食',
    category: 'travel'
  },
  {
    id: 'life-moments',
    name: '生活点滴',
    description: '日常生活中的美好瞬间和重要里程碑',
    icon: <Calendar className="w-6 h-6" />,
    layout: 'grid',
    features: ['网格布局', '日期标记', '生活分类', '心情记录'],
    preview: '📅 今日 📅 本周 📅 本月 📅 今年',
    category: 'life'
  },
  {
    id: 'special-events',
    name: '特殊时刻',
    description: '婚礼、毕业、庆典等人生重要时刻的纪念',
    icon: <Star className="w-6 h-6" />,
    layout: 'collage',
    features: ['拼贴布局', '主题装饰', '仪式感', '纪念意义'],
    preview: '💒 仪式 🎓 庆祝 🎂 派对 🎊 纪念',
    category: 'special'
  },
  {
    id: 'photo-collection',
    name: '照片集锦',
    description: '简洁的照片展示，突出图片本身的美感',
    icon: <Camera className="w-6 h-6" />,
    layout: 'grid',
    features: ['简洁布局', '高清展示', '快速浏览', '批量管理'],
    preview: '🖼️ 📷 🖼️ 📷 🖼️ 📷 🖼️ 📷',
    category: 'life'
  },
  {
    id: 'friendship-album',
    name: '友谊相册',
    description: '与朋友们的美好回忆和珍贵友谊时光',
    icon: <Users className="w-6 h-6" />,
    layout: 'story',
    features: ['朋友标签', '聚会记录', '友谊故事', '共同回忆'],
    preview: '👥 聚会 🎉 庆祝 🍻 畅谈 📸 合影',
    category: 'family'
  }
]

const CATEGORY_LABELS = {
  family: '家庭',
  travel: '旅行',
  life: '生活',
  special: '特殊'
}

export default function AlbumTemplateSelector({
  onSelectTemplate,
  selectedTemplate,
  disabled = false
}: AlbumTemplateProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  const filteredTemplates = selectedCategory === 'all' 
    ? ALBUM_TEMPLATES 
    : ALBUM_TEMPLATES.filter(template => template.category === selectedCategory)

  const categories = ['all', ...Object.keys(CATEGORY_LABELS)]

  return (
    <div className="space-y-6">
      {/* 分类筛选 */}
      <div className="flex flex-wrap gap-2">
        {categories.map(category => (
          <Button
            key={category}
            variant={selectedCategory === category ? 'primary' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory(category)}
            disabled={disabled}
          >
            {category === 'all' ? '全部' : CATEGORY_LABELS[category as keyof typeof CATEGORY_LABELS]}
          </Button>
        ))}
      </div>

      {/* 模板网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map(template => (
          <TemplateCard
            key={template.id}
            template={template}
            isSelected={selectedTemplate?.id === template.id}
            onSelect={() => onSelectTemplate(template)}
            disabled={disabled}
          />
        ))}
      </div>

      {/* 选中模板的详细信息 */}
      {selectedTemplate && (
        <Card variant="elevated">
          <CardHeader>
            <CardTitle className="flex items-center space-x-3">
              {selectedTemplate.icon}
              <span>已选择：{selectedTemplate.name}</span>
              <CheckCircle className="w-5 h-5 text-green-600" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">{selectedTemplate.description}</p>
            
            <div className="space-y-3">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">模板特色</h4>
                <div className="flex flex-wrap gap-2">
                  {selectedTemplate.features.map((feature, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">布局预览</h4>
                <div className="p-3 bg-gray-50 rounded-lg font-mono text-sm text-gray-600">
                  {selectedTemplate.preview}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// 模板卡片组件
interface TemplateCardProps {
  readonly template: AlbumTemplate
  readonly isSelected: boolean
  readonly onSelect: () => void
  readonly disabled: boolean
}

function TemplateCard({ template, isSelected, onSelect, disabled }: TemplateCardProps) {
  return (
    <Card
      className={cn(
        'cursor-pointer transition-all duration-200 hover:shadow-lg',
        isSelected && 'ring-2 ring-blue-500 border-blue-500',
        disabled && 'opacity-50 cursor-not-allowed'
      )}
      onClick={disabled ? undefined : onSelect}
      interactive={!disabled}
    >
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={cn(
              'p-2 rounded-lg',
              isSelected ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
            )}>
              {template.icon}
            </div>
            <span className="text-lg">{template.name}</span>
          </div>
          
          {isSelected && (
            <CheckCircle className="w-5 h-5 text-green-600" />
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <p className="text-gray-600 mb-4 leading-relaxed">
          {template.description}
        </p>
        
        <div className="space-y-3">
          <div>
            <div className="text-sm font-medium text-gray-700 mb-2">特色功能</div>
            <div className="flex flex-wrap gap-1">
              {template.features.slice(0, 3).map((feature, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
                >
                  {feature}
                </span>
              ))}
              {template.features.length > 3 && (
                <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                  +{template.features.length - 3}
                </span>
              )}
            </div>
          </div>
          
          <div>
            <div className="text-sm font-medium text-gray-700 mb-2">布局类型</div>
            <div className="flex items-center space-x-2">
              <Layout className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600 capitalize">
                {template.layout === 'grid' && '网格布局'}
                {template.layout === 'timeline' && '时间线'}
                {template.layout === 'story' && '故事模式'}
                {template.layout === 'collage' && '拼贴风格'}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
