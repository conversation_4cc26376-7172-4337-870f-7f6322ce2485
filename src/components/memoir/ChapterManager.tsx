'use client'

import { useState } from 'react'
import {
  Plus,
  Edit3,
  Trash2,
  ChevronRight,
  Clock,
  CheckCircle,
  Circle,
  Mic,
  FileText
} from 'lucide-react'
import { ChapterTemplate } from '@/lib/memoir-templates'

interface Chapter {
  id: string
  title: string
  description: string
  order: number
  status: 'not_started' | 'in_progress' | 'completed'
  contentBlocks: ContentBlock[]
  createdAt: string
  updatedAt: string
}

interface ContentBlock {
  id: string
  type: 'text' | 'voice' | 'image'
  content: string
  order: number
  createdAt: string
}

interface ChapterManagerProps {
  readonly chapters: Chapter[]
  readonly template?: ChapterTemplate[]
  readonly onCreateChapter: (chapter: Partial<Chapter>) => void
  readonly onUpdateChapter: (id: string, updates: Partial<Chapter>) => void
  readonly onDeleteChapter: (id: string) => void
  readonly onSelectChapter: (chapter: Chapter) => void
  readonly onStartRecording: (chapterId: string) => void
}

export default function ChapterManager({
  chapters,
  template,
  onCreateChapter,
  onUpdate<PERSON>hapter,
  onDeleteChapter,
  onSelectChapter,
  onStartRecording
}: ChapterManagerProps) {
  const [showCreateForm, setShowCreateForm] = useState(false)

  // 计算总体进度
  const calculateProgress = () => {
    if (chapters.length === 0) return 0
    const completedChapters = chapters.filter(chapter => chapter.status === 'completed').length
    return Math.round((completedChapters / chapters.length) * 100)
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">章节管理</h1>
          <p className="text-gray-600">
            管理您的回忆录章节，记录人生的每个重要阶段
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-5 h-5" />
          <span>新建章节</span>
        </button>
      </div>

      {/* Progress Overview */}
      {chapters.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">整体进度</h2>
            <span className="text-2xl font-bold text-blue-600">{calculateProgress()}%</span>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
            <div 
              className="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-300"
              style={{ width: `${calculateProgress()}%` }}
            />
          </div>
          
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {chapters.filter(c => c.status === 'completed').length}
              </div>
              <div className="text-sm text-gray-600">已完成</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {chapters.filter(c => c.status === 'in_progress').length}
              </div>
              <div className="text-sm text-gray-600">进行中</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-400">
                {chapters.filter(c => c.status === 'not_started').length}
              </div>
              <div className="text-sm text-gray-600">未开始</div>
            </div>
          </div>
        </div>
      )}

      {/* Chapters List */}
      <div className="space-y-4">
        {chapters.length > 0 ? (
          [...chapters]
            .sort((a, b) => a.order - b.order)
            .map((chapter) => (
              <ChapterCard
                key={chapter.id}
                chapter={chapter}
                onSelect={onSelectChapter}
                onEdit={() => console.log('Edit chapter:', chapter.id)}
                onDelete={() => onDeleteChapter(chapter.id)}
                onStartRecording={() => onStartRecording(chapter.id)}
                onUpdateStatus={(status) => onUpdateChapter(chapter.id, { status })}
              />
            ))
        ) : (
          <EmptyState onCreateChapter={() => setShowCreateForm(true)} />
        )}
      </div>

      {/* Template Suggestions */}
      {template && template.length > 0 && (
        <div className="mt-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">模板建议章节</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {template.map((templateChapter) => (
              <TemplateSuggestionCard
                key={templateChapter.id}
                template={templateChapter}
                onUse={() => onCreateChapter({
                  title: templateChapter.title,
                  description: templateChapter.description,
                  order: templateChapter.order,
                  status: 'not_started'
                })}
                isUsed={chapters.some(c => c.title === templateChapter.title)}
              />
            ))}
          </div>
        </div>
      )}

      {/* Create Chapter Form Modal */}
      {showCreateForm && (
        <CreateChapterModal
          onClose={() => setShowCreateForm(false)}
          onCreate={(chapter) => {
            onCreateChapter(chapter)
            setShowCreateForm(false)
          }}
          nextOrder={chapters.length + 1}
        />
      )}
    </div>
  )
}

// 章节卡片组件
interface ChapterCardProps {
  readonly chapter: Chapter
  readonly onSelect: (chapter: Chapter) => void
  readonly onEdit: () => void
  readonly onDelete: () => void
  readonly onStartRecording: () => void
  readonly onUpdateStatus: (status: Chapter['status']) => void
}

function ChapterCard({ 
  chapter, 
  onSelect, 
  onEdit, 
  onDelete, 
  onStartRecording,
  onUpdateStatus 
}: ChapterCardProps) {
  const getStatusIcon = (status: Chapter['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'in_progress':
        return <Circle className="w-5 h-5 text-blue-600 fill-current" />
      case 'not_started':
        return <Circle className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: Chapter['status']) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-50'
      case 'in_progress': return 'text-blue-600 bg-blue-50'
      case 'not_started': return 'text-gray-600 bg-gray-50'
    }
  }

  const getStatusText = (status: Chapter['status']) => {
    switch (status) {
      case 'completed': return '已完成'
      case 'in_progress': return '进行中'
      case 'not_started': return '未开始'
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-3">
              {getStatusIcon(chapter.status)}
              <h3 className="text-lg font-semibold text-gray-900">{chapter.title}</h3>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(chapter.status)}`}>
                {getStatusText(chapter.status)}
              </span>
            </div>
            
            <p className="text-gray-600 mb-4">{chapter.description}</p>
            
            <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
              <div className="flex items-center space-x-1">
                <FileText className="w-4 h-4" />
                <span>{chapter.contentBlocks.length} 个内容块</span>
              </div>
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>更新于 {new Date(chapter.updatedAt).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2 ml-4">
            <button
              onClick={onEdit}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <Edit3 className="w-4 h-4" />
            </button>
            <button
              onClick={onDelete}
              className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={onStartRecording}
            className="flex items-center space-x-2 bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 transition-colors"
          >
            <Mic className="w-4 h-4" />
            <span>开始录制</span>
          </button>
          
          <button
            onClick={() => onSelect(chapter)}
            className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"
          >
            <span>查看详情</span>
            <ChevronRight className="w-4 h-4" />
          </button>
          
          {chapter.status !== 'completed' && (
            <button
              onClick={() => onUpdateStatus('completed')}
              className="flex items-center space-x-2 border border-green-600 text-green-600 px-4 py-2 rounded-lg font-medium hover:bg-green-50 transition-colors"
            >
              <CheckCircle className="w-4 h-4" />
              <span>标记完成</span>
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

// 空状态组件
function EmptyState({ onCreateChapter }: { readonly onCreateChapter: () => void }) {
  return (
    <div className="text-center py-12">
      <div className="text-gray-400 text-6xl mb-4">📚</div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">还没有章节</h3>
      <p className="text-gray-600 mb-6">
        创建您的第一个章节，开始记录人生故事
      </p>
      <button
        onClick={onCreateChapter}
        className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
      >
        创建第一个章节
      </button>
    </div>
  )
}

// 模板建议卡片
interface TemplateSuggestionCardProps {
  readonly template: ChapterTemplate
  readonly onUse: () => void
  readonly isUsed: boolean
}

function TemplateSuggestionCard({ template, onUse, isUsed }: TemplateSuggestionCardProps) {
  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div className="flex items-start justify-between mb-3">
        <h4 className="font-medium text-gray-900">{template.title}</h4>
        {isUsed && (
          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
            已使用
          </span>
        )}
      </div>
      <p className="text-sm text-gray-600 mb-3">{template.description}</p>
      <div className="flex items-center justify-between">
        <span className="text-xs text-gray-500">
          建议时长: {template.suggestedDuration} 分钟
        </span>
        {!isUsed && (
          <button
            onClick={onUse}
            className="text-sm bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700 transition-colors"
          >
            使用模板
          </button>
        )}
      </div>
    </div>
  )
}

// 创建章节模态框
interface CreateChapterModalProps {
  readonly onClose: () => void
  readonly onCreate: (chapter: Partial<Chapter>) => void
  readonly nextOrder: number
}

function CreateChapterModal({ onClose, onCreate, nextOrder }: CreateChapterModalProps) {
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (title.trim()) {
      onCreate({
        title: title.trim(),
        description: description.trim(),
        order: nextOrder,
        status: 'not_started'
      })
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-2xl shadow-xl max-w-md w-full">
        <div className="p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">创建新章节</h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="chapter-title" className="block text-sm font-medium text-gray-700 mb-2">
                章节标题
              </label>
              <input
                id="chapter-title"
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="输入章节标题"
                required
              />
            </div>
            
            <div>
              <label htmlFor="chapter-description" className="block text-sm font-medium text-gray-700 mb-2">
                章节描述
              </label>
              <textarea
                id="chapter-description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="描述这个章节的内容"
              />
            </div>
            
            <div className="flex items-center space-x-3 pt-4">
              <button
                type="submit"
                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                创建章节
              </button>
              <button
                type="button"
                onClick={onClose}
                className="flex-1 border border-gray-300 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors"
              >
                取消
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
