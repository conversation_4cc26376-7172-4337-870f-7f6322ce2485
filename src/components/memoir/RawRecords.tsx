'use client'

import { useState, useEffect } from 'react'
import { Clock, FileText, CheckCircle, Calendar } from 'lucide-react'

import { Button } from '@/components/ui/Button'
import { Card, CardContent } from '@/components/ui/Card'
import { ConversationSessionManager, ConversationSummary } from '@/lib/conversation-sessions'

export default function RawRecords() {
  const [viewMode, setViewMode] = useState<'timeline' | 'files'>('timeline')
  const [conversationSummaries, setConversationSummaries] = useState<ConversationSummary[]>([])

  // 加载对话会话摘要
  useEffect(() => {
    const summaries = ConversationSessionManager.getSessionSummaries()
    setConversationSummaries(summaries)
  }, [])

  return (
    <div className="space-y-6">
      {/* View Mode Toggle */}
      <div className="flex items-center justify-between">
        <div className="flex gap-2">
          <Button
            onClick={() => setViewMode('timeline')}
            variant={viewMode === 'timeline' ? 'primary' : 'outline'}
            icon={<Clock className="w-4 h-4" />}
          >
            对话时间线
          </Button>
          <Button
            onClick={() => setViewMode('files')}
            variant={viewMode === 'files' ? 'primary' : 'outline'}
            icon={<FileText className="w-4 h-4" />}
          >
            文件记录
          </Button>
        </div>
      </div>

      {/* Timeline View */}
      {viewMode === 'timeline' && (
        <div className="space-y-4">
          {conversationSummaries.length > 0 ? (
            <div className="space-y-4">
              {conversationSummaries.map((summary) => (
                <Card key={summary.sessionId} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-3">
                        <div className="flex items-center space-x-3">
                          <CheckCircle className="w-5 h-5 text-green-600" />
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-gray-900">{summary.title}</h3>
                            <p className="text-sm text-gray-600">{summary.chapterTitle}</p>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center space-x-2 text-sm text-gray-500">
                              <Calendar className="w-4 h-4" />
                              <span>{summary.date.toLocaleDateString()}</span>
                            </div>
                          </div>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-3">
                          <p className="text-sm text-gray-700 leading-relaxed">
                            {summary.summary}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Clock className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">还没有对话记录</h3>
                <p className="text-gray-600 mb-6">开始创作对话后，您的对话记录将显示在这里</p>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Files View */}
      {viewMode === 'files' && (
        <Card>
          <CardContent className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <FileText className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">文件记录功能</h3>
            <p className="text-gray-600 mb-6">文件上传和管理功能正在开发中</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
