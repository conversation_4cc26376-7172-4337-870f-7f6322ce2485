'use client'

import { useState, useEffect } from 'react'
import { Clock, FileText, CheckCircle, Calendar } from 'lucide-react'

import { Button } from '@/components/ui/Button'
import { Card, CardContent } from '@/components/ui/Card'
import { ConversationSessionManager, ConversationSummary } from '@/lib/conversation-sessions'

export default function RawRecords() {
  const [viewMode, setViewMode] = useState<'timeline' | 'files'>('timeline')
  const [conversationSummaries, setConversationSummaries] = useState<ConversationSummary[]>([])

  // 加载对话会话摘要
  useEffect(() => {
    const summaries = ConversationSessionManager.getSessionSummaries()
    setConversationSummaries(summaries)
  }, [])

  return (
    <div className="space-y-6">
      {/* View Mode Toggle */}
      <div className="flex items-center justify-between">
        <div className="flex gap-2">
          <Button
            onClick={() => setViewMode('timeline')}
            variant={viewMode === 'timeline' ? 'primary' : 'outline'}
            icon={<Clock className="w-4 h-4" />}
          >
            对话时间线
          </Button>
          <Button
            onClick={() => setViewMode('files')}
            variant={viewMode === 'files' ? 'primary' : 'outline'}
            icon={<FileText className="w-4 h-4" />}
          >
            文件记录
          </Button>
        </div>
      </div>

      {/* Timeline View */}
      {viewMode === 'timeline' && (
        <div className="space-y-4">
          {conversationSummaries.length > 0 ? (
            <>
              {/* Mobile: Horizontal scroll timeline */}
              <div className="md:hidden">
                <div className="flex space-x-4 overflow-x-auto pb-4 scrollbar-hide snap-x snap-mandatory">
                  {conversationSummaries.map((summary) => (
                    <Card key={`mobile-${summary.sessionId}`} className="flex-shrink-0 w-80 hover:shadow-lg transition-shadow snap-center">
                      <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-3">
                        <div className="flex items-center space-x-3">
                          <CheckCircle className="w-5 h-5 text-green-600" />
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-gray-900">{summary.title}</h3>
                            <p className="text-sm text-gray-600">{summary.chapterTitle}</p>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center space-x-2 text-sm text-gray-500">
                              <Calendar className="w-4 h-4" />
                              <span>{summary.date.toLocaleDateString()}</span>
                            </div>
                          </div>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-3">
                          <p className="text-sm text-gray-700 leading-relaxed">
                            {summary.summary}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            </div>

            {/* Desktop: Vertical timeline */}
            <div className="hidden md:block space-y-4">
              {conversationSummaries.map((summary) => (
                <Card key={`desktop-${summary.sessionId}`} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-3">
                        {/* Header */}
                        <div className="flex items-center space-x-3">
                          <CheckCircle className="w-5 h-5 text-green-600" />
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-gray-900">{summary.title}</h3>
                            <p className="text-sm text-gray-600">{summary.chapterTitle}</p>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center space-x-2 text-sm text-gray-500">
                              <Calendar className="w-4 h-4" />
                              <span>{summary.date.toLocaleDateString()}</span>
                            </div>
                            <div className="flex items-center space-x-2 text-sm text-gray-500 mt-1">
                              <Clock className="w-4 h-4" />
                              <span>{summary.duration}秒</span>
                            </div>
                          </div>
                        </div>

                        {/* Status and Progress */}
                        <div className="flex items-center space-x-3">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border bg-green-50 border-green-200 text-green-800">
                            {summary.status === 'completed' && '已完成'}
                            {summary.status === 'ongoing' && '进行中'}
                            {summary.status === 'paused' && '已暂停'}
                          </span>
                          <span className="text-sm text-gray-500">{summary.messageCount} 条消息</span>
                          <span className="text-sm text-gray-500">{summary.completionPercentage}% 完成</span>
                        </div>

                        {/* Progress Bar */}
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${summary.completionPercentage}%` }}
                          ></div>
                        </div>

                        {/* Summary */}
                        <div className="bg-gray-50 rounded-lg p-3">
                          <p className="text-sm text-gray-700 leading-relaxed">
                            {summary.summary}
                          </p>
                        </div>

                        {/* Key Points */}
                        {summary.keyPoints.length > 0 && (
                          <div className="space-y-2">
                            <h4 className="text-sm font-medium text-gray-900">关键要点:</h4>
                            <ul className="space-y-1">
                              {summary.keyPoints.slice(0, 3).map((point, pointIndex) => (
                                <li key={`${summary.sessionId}-point-${pointIndex}`} className="text-sm text-gray-600 flex items-start">
                                  <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                                  {point}
                                </li>
                              ))}
                              {summary.keyPoints.length > 3 && (
                                <li className="text-sm text-gray-500 italic">
                                  还有 {summary.keyPoints.length - 3} 个要点...
                                </li>
                              )}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            </>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Clock className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">还没有对话记录</h3>
                <p className="text-gray-600 mb-6">开始创作对话后，您的对话记录将显示在这里</p>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Files View */}
      {viewMode === 'files' && (
        <Card>
          <CardContent className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <FileText className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">文件记录功能</h3>
            <p className="text-gray-600 mb-6">文件上传和管理功能正在开发中</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
