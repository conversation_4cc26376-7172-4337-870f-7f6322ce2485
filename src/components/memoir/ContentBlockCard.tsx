'use client'

import { useState } from 'react'
import Image from 'next/image'
import {
  FileText,
  Mic,
  Image as ImageIcon,
  Edit3,
  Trash2,
  Play,
  Pause,
  Volume2,
  Calendar,
  Clock,
  MoreVertical,
  Copy,
  Share2
} from 'lucide-react'

export interface ContentBlock {
  id: string
  type: 'text' | 'voice' | 'image'
  content: string
  metadata?: {
    duration?: number // 语音时长（秒）
    transcription?: string // 语音转文字
    imageCaption?: string // 图片说明
    recordedAt?: string // 录制时间
    processedContent?: string // AI处理后的内容
  }
  order: number
  createdAt: string
  updatedAt: string
}

interface ContentBlockCardProps {
  readonly block: ContentBlock
  readonly onEdit: (block: ContentBlock) => void
  readonly onDelete: (blockId: string) => void
  readonly isEditing?: boolean
  readonly onSave?: (blockId: string, content: string) => void
  readonly onCancel?: () => void
}

export default function ContentBlockCard({
  block,
  onEdit,
  onDelete,
  isEditing = false,
  onSave,
  onCancel
}: ContentBlockCardProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [showMenu, setShowMenu] = useState(false)
  const [editContent, setEditContent] = useState(block.content)

  const handleSave = () => {
    onSave?.(block.id, editContent)
  }

  const handleCancel = () => {
    setEditContent(block.content)
    onCancel?.()
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getTypeIcon = () => {
    switch (block.type) {
      case 'text':
        return <FileText className="w-5 h-5 text-blue-600" />
      case 'voice':
        return <Mic className="w-5 h-5 text-red-600" />
      case 'image':
        return <ImageIcon className="w-5 h-5 text-green-600" />
    }
  }

  const getTypeLabel = () => {
    switch (block.type) {
      case 'text': return '文本'
      case 'voice': return '语音'
      case 'image': return '图片'
    }
  }

  const getTypeBgColor = () => {
    switch (block.type) {
      case 'text': return 'bg-blue-50 border-blue-200'
      case 'voice': return 'bg-red-50 border-red-200'
      case 'image': return 'bg-green-50 border-green-200'
    }
  }

  return (
    <div className={`bg-white rounded-xl shadow-sm border-2 transition-all duration-200 ${getTypeBgColor()}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <div className="flex items-center space-x-3">
          {getTypeIcon()}
          <div>
            <span className="font-medium text-gray-900">{getTypeLabel()}</span>
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <Calendar className="w-3 h-3" />
              <span>{formatDate(block.createdAt)}</span>
              {block.metadata?.duration && (
                <>
                  <Clock className="w-3 h-3 ml-2" />
                  <span>{formatDuration(block.metadata.duration)}</span>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <div className="relative">
            <button
              onClick={() => setShowMenu(!showMenu)}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <MoreVertical className="w-4 h-4" />
            </button>
            
            {showMenu && (
              <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[120px]">
                <button
                  onClick={() => {
                    onEdit(block)
                    setShowMenu(false)
                  }}
                  className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  <Edit3 className="w-4 h-4" />
                  <span>编辑</span>
                </button>
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(block.content)
                    setShowMenu(false)
                  }}
                  className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  <Copy className="w-4 h-4" />
                  <span>复制</span>
                </button>
                <button
                  onClick={() => {
                    // 实现分享功能 - 复制内容到剪贴板
                    navigator.clipboard.writeText(block.content)
                    setShowMenu(false)
                  }}
                  className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  <Share2 className="w-4 h-4" />
                  <span>分享</span>
                </button>
                <hr className="my-1" />
                <button
                  onClick={() => {
                    onDelete(block.id)
                    setShowMenu(false)
                  }}
                  className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-red-600 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4" />
                  <span>删除</span>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {block.type === 'text' && (
          <div>
            {isEditing ? (
              <div className="space-y-3">
                <textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  rows={4}
                  placeholder="输入文本内容..."
                />
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleSave}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                  >
                    保存
                  </button>
                  <button
                    onClick={handleCancel}
                    className="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors"
                  >
                    取消
                  </button>
                </div>
              </div>
            ) : (
              <p className="text-gray-800 leading-relaxed whitespace-pre-wrap">
                {block.content}
              </p>
            )}
          </div>
        )}

        {block.type === 'voice' && (
          <div className="space-y-3">
            {/* Audio Player */}
            <div className="flex items-center space-x-3 bg-gray-50 rounded-lg p-3">
              <button
                onClick={() => setIsPlaying(!isPlaying)}
                className="flex items-center justify-center w-10 h-10 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors"
              >
                {isPlaying ? (
                  <Pause className="w-5 h-5" />
                ) : (
                  <Play className="w-5 h-5 ml-0.5" />
                )}
              </button>
              
              <div className="flex-1">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium text-gray-700">语音录制</span>
                  {block.metadata?.duration && (
                    <span className="text-xs text-gray-500">
                      {formatDuration(block.metadata.duration)}
                    </span>
                  )}
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-red-600 h-2 rounded-full" style={{ width: '30%' }} />
                </div>
              </div>
              
              <Volume2 className="w-5 h-5 text-gray-400" />
            </div>

            {/* Transcription */}
            {block.metadata?.transcription && (
              <div className="bg-gray-50 rounded-lg p-3">
                <h4 className="text-sm font-medium text-gray-700 mb-2">语音转文字</h4>
                <p className="text-gray-800 text-sm leading-relaxed">
                  {block.metadata.transcription}
                </p>
              </div>
            )}

            {/* AI Processed Content */}
            {block.metadata?.processedContent && (
              <div className="bg-blue-50 rounded-lg p-3">
                <h4 className="text-sm font-medium text-blue-700 mb-2">AI整理内容</h4>
                <p className="text-gray-800 text-sm leading-relaxed">
                  {block.metadata.processedContent}
                </p>
              </div>
            )}
          </div>
        )}

        {block.type === 'image' && (
          <div className="space-y-3">
            {/* Image Display */}
            <div className="relative">
              <Image
                src={block.content}
                alt={block.metadata?.imageCaption ?? '回忆录图片'}
                width={400}
                height={192}
                className="w-full h-48 object-cover rounded-lg"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-all duration-200 rounded-lg" />
            </div>

            {/* Image Caption */}
            {block.metadata?.imageCaption && (
              <div className="bg-gray-50 rounded-lg p-3">
                <h4 className="text-sm font-medium text-gray-700 mb-1">图片说明</h4>
                <p className="text-gray-800 text-sm">
                  {block.metadata.imageCaption}
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-4 pb-4">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>第 {block.order} 个内容块</span>
          <span>更新于 {formatDate(block.updatedAt)}</span>
        </div>
      </div>
    </div>
  )
}
