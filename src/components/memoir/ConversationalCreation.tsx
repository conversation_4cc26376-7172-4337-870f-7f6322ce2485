'use client'

import { useState, useEffect, useRef } from 'react'
import { <PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, Send, Bot, User, Volume2, VolumeX, RefreshCw, Lightbulb, Edit3, Check } from 'lucide-react'
import { MemoirTemplate } from '@/lib/memoir-templates'
import { <PERSON><PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { VoiceRecognition, VoiceRecognitionResult } from '@/lib/voice-recognition'

interface ConversationalCreationProps {
  template: MemoirTemplate
  onContentChange: () => void
}

interface Message {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
  audioUrl?: string
}

interface VoiceRecording {
  isRecording: boolean
  audioUrl?: string
  duration: number
  transcription?: string
  isTranscribing?: boolean
  transcriptionError?: string
}

export default function ConversationalCreation({ template, onContentChange }: Readonly<ConversationalCreationProps>) {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputText, setInputText] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [currentChapterIndex, setCurrentChapterIndex] = useState(0)
  const [currentPromptIndex, setCurrentPromptIndex] = useState(0)
  const [voiceRecording, setVoiceRecording] = useState<VoiceRecording>({
    isRecording: false,
    duration: 0
  })
  const [isSpeechEnabled, setIsSpeechEnabled] = useState(true)
  const [voiceRecognition, setVoiceRecognition] = useState<VoiceRecognition | null>(null)
  const [isTranscribing, setIsTranscribing] = useState(false)
  const [transcriptionText, setTranscriptionText] = useState('')
  const [isEditingTranscription, setIsEditingTranscription] = useState(false)
  const [speechSupported, setSpeechSupported] = useState(false)

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioChunksRef = useRef<Blob[]>([])

  const currentChapter = template.chapters[currentChapterIndex]
  const currentPrompt = currentChapter?.prompts[currentPromptIndex]

  useEffect(() => {
    // 初始化对话
    if (messages.length === 0) {
      const welcomeMessage: Message = {
        id: Date.now().toString(),
        type: 'ai',
        content: `您好！我是您的AI助手，很高兴帮助您创作《${template.name}》。让我们从第一章"${currentChapter.title}"开始吧。\n\n${currentPrompt}`,
        timestamp: new Date()
      }
      setMessages([welcomeMessage])
    }
  }, [template, currentChapter, currentPrompt, messages.length])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // 初始化语音识别服务
  useEffect(() => {
    const recognition = new VoiceRecognition({
      language: 'zh-CN',
      continuous: true,
      interimResults: true
    })

    setSpeechSupported(recognition.isRecognitionSupported())
    setVoiceRecognition(recognition)
  }, [])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async () => {
    const transcription = voiceRecording.transcription || ''
    const messageContent = inputText.trim() || transcription || '语音消息'

    if (!messageContent && !voiceRecording.audioUrl) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: messageContent,
      timestamp: new Date(),
      audioUrl: voiceRecording.audioUrl
    }

    setMessages(prev => [...prev, userMessage])
    setInputText('')
    setTranscriptionText('')
    setVoiceRecording({ isRecording: false, duration: 0 })
    setIsLoading(true)
    onContentChange()

    try {
      // 模拟AI响应
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: generateAIResponse(),
        timestamp: new Date()
      }

      setMessages(prev => [...prev, aiResponse])
      
      // 语音播报AI回复
      if (isSpeechEnabled) {
        speakText(aiResponse.content)
      }
    } catch (error) {
      console.error('Failed to get AI response:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const generateAIResponse = (): string => {
    const responses = [
      `谢谢您的分享！这个故事很有意思。能再详细说说当时的感受吗？`,
      `听起来是一段很珍贵的回忆。还有什么相关的细节想要补充的吗？`,
      `这个经历对您来说一定很重要。您觉得它对您的人生有什么影响？`,
      `很棒的内容！让我们继续下一个话题。${getNextPrompt()}`
    ]
    return responses[Math.floor(Math.random() * responses.length)]
  }

  const getNextPrompt = (): string => {
    if (currentPromptIndex < currentChapter.prompts.length - 1) {
      return currentChapter.prompts[currentPromptIndex + 1]
    } else if (currentChapterIndex < template.chapters.length - 1) {
      return `让我们开始下一章"${template.chapters[currentChapterIndex + 1].title}"。${template.chapters[currentChapterIndex + 1].prompts[0]}`
    }
    return '我们已经完成了所有章节的基础内容！您还想补充什么吗？'
  }

  const handleNextPrompt = () => {
    if (currentPromptIndex < currentChapter.prompts.length - 1) {
      setCurrentPromptIndex(prev => prev + 1)
    } else if (currentChapterIndex < template.chapters.length - 1) {
      setCurrentChapterIndex(prev => prev + 1)
      setCurrentPromptIndex(0)
    }
  }

  const startVoiceRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const mediaRecorder = new MediaRecorder(stream)
      mediaRecorderRef.current = mediaRecorder
      audioChunksRef.current = []

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data)
      }

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' })
        const audioUrl = URL.createObjectURL(audioBlob)
        setVoiceRecording(prev => ({
          ...prev,
          audioUrl,
          transcription: transcriptionText.split('|')[0] || '',
          isTranscribing: false
        }))
        stream.getTracks().forEach(track => track.stop())
      }

      mediaRecorder.start()
      setVoiceRecording(prev => ({ ...prev, isRecording: true }))

      // 同时开始语音转文字
      if (voiceRecognition && speechSupported) {
        setIsTranscribing(true)
        setTranscriptionText('')

        voiceRecognition.startListening(
          (result: VoiceRecognitionResult) => {
            if (result.isFinal) {
              setTranscriptionText(prev => prev + result.transcript + ' ')
            } else {
              // 显示临时结果
              setTranscriptionText(prev => {
                const finalText = prev.split('|')[0] || ''
                return finalText + '|' + result.transcript
              })
            }
          },
          (error: string) => {
            console.error('Speech recognition error:', error)
            setVoiceRecording(prev => ({ ...prev, transcriptionError: error }))
            setIsTranscribing(false)
          },
          () => {
            setIsTranscribing(false)
            // 清理临时结果标记
            setTranscriptionText(prev => prev.split('|')[0] || '')
          }
        )
      }
    } catch (error) {
      console.error('Failed to start recording:', error)
      alert('无法访问麦克风，请检查权限设置')
    }
  }

  const stopVoiceRecording = () => {
    if (mediaRecorderRef.current && voiceRecording.isRecording) {
      mediaRecorderRef.current.stop()
      setVoiceRecording(prev => ({ ...prev, isRecording: false }))
    }

    // 停止语音转文字
    if (voiceRecognition && isTranscribing) {
      voiceRecognition.stopListening()
    }
  }

  const speakText = (text: string) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text)
      utterance.lang = 'zh-CN'
      utterance.rate = 0.8
      speechSynthesis.speak(utterance)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // 键盘快捷键处理
  useEffect(() => {
    const handleGlobalKeyPress = (e: KeyboardEvent) => {
      // Ctrl/Cmd + M 开始/停止录音
      if ((e.ctrlKey || e.metaKey) && e.key === 'm') {
        e.preventDefault()
        if (voiceRecording.isRecording) {
          stopVoiceRecording()
        } else {
          startVoiceRecording()
        }
      }

      // Ctrl/Cmd + Enter 发送消息
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault()
        handleSendMessage()
      }

      // Escape 停止录音
      if (e.key === 'Escape' && voiceRecording.isRecording) {
        e.preventDefault()
        stopVoiceRecording()
      }
    }

    document.addEventListener('keydown', handleGlobalKeyPress)
    return () => document.removeEventListener('keydown', handleGlobalKeyPress)
  }, [voiceRecording.isRecording, handleSendMessage, startVoiceRecording, stopVoiceRecording])

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-200px)]">
      {/* Chat Area */}
      <div className="lg:col-span-3 flex flex-col">
        <Card className="flex-1 flex flex-col">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center justify-between">
              <span>AI对话创作</span>
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsSpeechEnabled(!isSpeechEnabled)}
                  icon={isSpeechEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
                  aria-label={isSpeechEnabled ? '关闭语音播放' : '开启语音播放'}
                >
                  {isSpeechEnabled ? '语音开启' : '语音关闭'}
                </Button>
              </div>
            </CardTitle>

            {/* Accessibility Help */}
            <div className="mt-3 p-2 bg-blue-50 rounded-lg">
              <p className="text-xs text-blue-800">
                <span className="font-medium">快捷键提示：</span>
                <span className="hidden sm:inline"> Ctrl+M 录音 | Ctrl+Enter 发送 | Esc 停止录音</span>
                <span className="sm:hidden"> 长按录音按钮开始录音</span>
              </p>
            </div>
          </CardHeader>
          
          <CardContent className="flex-1 flex flex-col">
            {/* Messages */}
            <div className="flex-1 overflow-y-auto space-y-4 mb-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] rounded-lg p-4 ${
                      message.type === 'user'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-900'
                    }`}
                  >
                    <div className="flex items-start space-x-2">
                      {message.type === 'ai' && <Bot className="w-5 h-5 mt-0.5 text-blue-600" />}
                      {message.type === 'user' && <User className="w-5 h-5 mt-0.5" />}
                      <div className="flex-1">
                        <p className="whitespace-pre-wrap">{message.content}</p>
                        {message.audioUrl && (
                          <audio controls className="mt-2 w-full">
                            <source src={message.audioUrl} type="audio/wav" />
                            <track kind="captions" srcLang="zh" label="中文" />
                          </audio>
                        )}
                        <p className={`text-xs mt-2 ${
                          message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
                        }`}>
                          {message.timestamp.toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              {isLoading && (
                <div className="flex justify-start">
                  <div className="bg-gray-100 rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                      <Bot className="w-5 h-5 text-blue-600" />
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* Input Area */}
            <div className="border-t pt-4">
              {/* Speech Recognition Status */}
              {isTranscribing && (
                <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    <p className="text-sm text-blue-700">正在识别语音...</p>
                  </div>
                  {transcriptionText && (
                    <div className="mt-2 p-2 bg-white rounded border">
                      <p className="text-sm text-gray-700">
                        {transcriptionText.split('|')[0]}
                        {transcriptionText.includes('|') && (
                          <span className="text-gray-400 italic">
                            {transcriptionText.split('|')[1]}
                          </span>
                        )}
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Voice Recording Completed */}
              {voiceRecording.audioUrl && (
                <div className="mb-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-sm text-green-700">录音完成</p>
                    {!speechSupported && (
                      <p className="text-xs text-orange-600">浏览器不支持语音识别</p>
                    )}
                  </div>

                  <audio controls className="w-full mb-3">
                    <source src={voiceRecording.audioUrl} type="audio/wav" />
                    <track kind="captions" srcLang="zh" label="中文" />
                  </audio>

                  {/* Transcription Display and Editing */}
                  {voiceRecording.transcription && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-700">语音转文字:</p>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setIsEditingTranscription(!isEditingTranscription)}
                          icon={isEditingTranscription ? <Check className="w-4 h-4" /> : <Edit3 className="w-4 h-4" />}
                        >
                          {isEditingTranscription ? '完成' : '编辑'}
                        </Button>
                      </div>

                      {isEditingTranscription ? (
                        <textarea
                          value={voiceRecording.transcription}
                          onChange={(e) => setVoiceRecording(prev => ({
                            ...prev,
                            transcription: e.target.value
                          }))}
                          className="w-full p-2 text-sm border border-gray-300 rounded resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          rows={3}
                          placeholder="编辑转录文本..."
                        />
                      ) : (
                        <div className="p-2 bg-white border border-gray-200 rounded">
                          <p className="text-sm text-gray-700 whitespace-pre-wrap">
                            {voiceRecording.transcription}
                          </p>
                        </div>
                      )}
                    </div>
                  )}

                  {voiceRecording.transcriptionError && (
                    <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                      <p className="text-sm text-red-600">{voiceRecording.transcriptionError}</p>
                    </div>
                  )}
                </div>
              )}

              {/* Speech Recognition Not Supported Warning */}
              {!speechSupported && (
                <div className="mb-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-700">您的浏览器不支持语音识别功能。建议使用最新版本的Chrome、Edge或Safari浏览器。</p>
                </div>
              )}
              
              <div className="flex space-x-2">
                <div className="flex-1 relative">
                  <label htmlFor="conversation-input" className="sr-only">
                    输入您的回答或使用语音录制
                  </label>
                  <textarea
                    id="conversation-input"
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    onKeyDown={handleKeyPress}
                    placeholder="输入您的回答，或使用语音录制..."
                    aria-describedby="conversation-input-help"
                    aria-label="对话输入框"
                    className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg focus-enhanced"
                    rows={2}
                  />
                  <div id="conversation-input-help" className="sr-only">
                    在此输入您的回答或回忆。按Enter发送，Shift+Enter换行。您也可以使用语音录制功能。
                  </div>
                </div>
                
                <div className="flex flex-col space-y-2">
                  {/* Voice Recording Button with Enhanced Accessibility */}
                  <Button
                    onClick={voiceRecording.isRecording ? stopVoiceRecording : startVoiceRecording}
                    variant={voiceRecording.isRecording ? 'destructive' : 'outline'}
                    size="lg"
                    icon={voiceRecording.isRecording ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
                    aria-label={voiceRecording.isRecording ? '停止录音 (Ctrl+M)' : '开始录音 (Ctrl+M)'}
                    aria-describedby="voice-recording-help"
                    className="touch-target focus-enhanced"
                  >
                    {voiceRecording.isRecording ? '停止录音' : '开始录音'}
                  </Button>

                  {/* Voice Recording Status */}
                  <div
                    id="voice-recording-help"
                    className="sr-only"
                    aria-live="polite"
                    aria-atomic="true"
                  >
                    {(() => {
                      if (voiceRecording.isRecording) {
                        return '正在录音，请说话。按Escape或点击停止按钮结束录音。'
                      } else if (voiceRecording.transcription) {
                        return `录音完成。转换文字: ${voiceRecording.transcription}`
                      } else {
                        return '准备录音。点击按钮或按Ctrl+M开始录音。'
                      }
                    })()}
                  </div>

                  <Button
                    onClick={handleSendMessage}
                    disabled={!inputText.trim() && !voiceRecording.audioUrl && !voiceRecording.transcription}
                    icon={<Send className="w-5 h-5" />}
                    aria-label="发送消息 (Ctrl+Enter)"
                    className="touch-target focus-enhanced"
                  >
                    发送消息
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sidebar */}
      <div className="space-y-6">
        {/* Current Chapter Progress */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">创作进度</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <p className="text-sm font-medium text-gray-700">当前章节</p>
                <p className="text-lg font-semibold text-blue-600">
                  第{currentChapterIndex + 1}章：{currentChapter.title}
                </p>
              </div>
              
              <div>
                <p className="text-sm font-medium text-gray-700">进度</p>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${((currentChapterIndex * currentChapter.prompts.length + currentPromptIndex + 1) / 
                        (template.chapters.reduce((acc, ch) => acc + ch.prompts.length, 0))) * 100}%`
                    }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {currentPromptIndex + 1} / {currentChapter.prompts.length} 个问题
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Current Prompt */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <Lightbulb className="w-5 h-5 mr-2 text-yellow-500" />
              当前引导
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 mb-4">{currentPrompt}</p>
            <Button
              onClick={handleNextPrompt}
              variant="outline"
              size="sm"
              icon={<RefreshCw className="w-4 h-4" />}
              disabled={currentChapterIndex >= template.chapters.length - 1 && 
                       currentPromptIndex >= currentChapter.prompts.length - 1}
            >
              下一个问题
            </Button>
          </CardContent>
        </Card>

        {/* Chapter List */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">章节列表</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {template.chapters.map((chapter, index) => (
                <button
                  key={`chapter-${index}-${chapter.title}`}
                  onClick={() => {
                    setCurrentChapterIndex(index)
                    setCurrentPromptIndex(0)
                  }}
                  className={`w-full text-left p-3 rounded-lg transition-colors ${
                    index === currentChapterIndex
                      ? 'bg-blue-100 text-blue-700 border border-blue-200'
                      : 'hover:bg-gray-50'
                  }`}
                >
                  <p className="font-medium">第{index + 1}章</p>
                  <p className="text-sm text-gray-600">{chapter.title}</p>
                </button>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
