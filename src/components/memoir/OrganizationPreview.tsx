'use client'

import { useState, useEffect } from 'react'
import { Edit3, Eye, Download, Share2, BookOpen, ChevronDown, ChevronRight, Save, Zap, ArrowRight, ArrowLeft, Check, X, Loader2, BarChart3, TrendingUp } from 'lucide-react'
import { MemoirTemplate } from '@/lib/memoir-templates'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { AIContentProcessor, ProcessingResult, BeforeAfterComparison } from '@/lib/ai-content-processor'

interface OrganizationPreviewProps {
  template: MemoirTemplate
  onContentChange: () => void
}

interface ChapterContent {
  id: string
  title: string
  content: string
  isExpanded: boolean
  lastModified: Date
}

interface MemoirData {
  title: string
  subtitle: string
  author: string
  chapters: ChapterContent[]
  coverImage?: string
  createdAt: Date
  lastModified: Date
}

export default function OrganizationPreview({ template, onContentChange }: Readonly<OrganizationPreviewProps>) {
  const [viewMode, setViewMode] = useState<'edit' | 'preview' | 'ai-processing' | 'comparison'>('edit')
  const [memoirData, setMemoirData] = useState<MemoirData>({
    title: `我的${template.name}`,
    subtitle: '珍贵回忆的记录',
    author: '作者姓名',
    chapters: template.chapters.map((chapter, index) => ({
      id: `chapter-${index}`,
      title: chapter.title,
      content: `这里是"${chapter.title}"的内容。您可以在这里编辑和整理从对话创作和原始记录中收集的内容。\n\n${chapter.description}\n\n请在此处添加您的具体内容...`,
      isExpanded: index === 0,
      lastModified: new Date()
    })),
    createdAt: new Date(),
    lastModified: new Date()
  })
  const [editingChapter, setEditingChapter] = useState<string | null>(null)
  const [tempContent, setTempContent] = useState('')

  // AI处理相关状态
  const [isProcessing, setIsProcessing] = useState(false)
  const [processingProgress, setProcessingProgress] = useState(0)
  const [processingStatus, setProcessingStatus] = useState('')
  const [processingResult, setProcessingResult] = useState<ProcessingResult | null>(null)
  const [comparisons, setComparisons] = useState<BeforeAfterComparison[]>([])
  const [currentComparisonIndex, setCurrentComparisonIndex] = useState(0)

  const handleTitleChange = (field: 'title' | 'subtitle' | 'author', value: string) => {
    setMemoirData(prev => ({
      ...prev,
      [field]: value,
      lastModified: new Date()
    }))
    onContentChange()
  }

  const handleChapterEdit = (chapterId: string) => {
    const chapter = memoirData.chapters.find(ch => ch.id === chapterId)
    if (chapter) {
      setEditingChapter(chapterId)
      setTempContent(chapter.content)
    }
  }

  const handleChapterSave = () => {
    if (editingChapter) {
      setMemoirData(prev => ({
        ...prev,
        chapters: prev.chapters.map(ch =>
          ch.id === editingChapter
            ? { ...ch, content: tempContent, lastModified: new Date() }
            : ch
        ),
        lastModified: new Date()
      }))
      setEditingChapter(null)
      setTempContent('')
      onContentChange()
    }
  }

  const handleChapterCancel = () => {
    setEditingChapter(null)
    setTempContent('')
  }

  const toggleChapterExpansion = (chapterId: string) => {
    setMemoirData(prev => ({
      ...prev,
      chapters: prev.chapters.map(ch =>
        ch.id === chapterId ? { ...ch, isExpanded: !ch.isExpanded } : ch
      )
    }))
  }

  const handleExport = () => {
    // TODO: 实现导出功能
    console.log('Exporting memoir...', memoirData)
    alert('导出功能正在开发中...')
  }

  const handleShare = () => {
    // TODO: 实现分享功能
    console.log('Sharing memoir...', memoirData)
    alert('分享功能正在开发中...')
  }

  // AI处理功能
  const handleAIProcessing = async () => {
    setIsProcessing(true)
    setProcessingProgress(0)
    setProcessingStatus('开始处理...')
    setViewMode('ai-processing')

    try {
      const result = await AIContentProcessor.processAllContent(
        template,
        (progress, status) => {
          setProcessingProgress(progress)
          setProcessingStatus(status)
        }
      )

      setProcessingResult(result)

      if (result.success) {
        // 生成对比数据
        const comparisonData = AIContentProcessor.generateComparisons(result.contentBlocks)
        setComparisons(comparisonData)
        AIContentProcessor.saveComparisons(comparisonData)

        setViewMode('comparison')
        setCurrentComparisonIndex(0)
      } else {
        alert(`AI处理失败: ${result.error}`)
        setViewMode('edit')
      }
    } catch (error) {
      console.error('AI processing error:', error)
      alert('AI处理过程中出现错误，请重试')
      setViewMode('edit')
    } finally {
      setIsProcessing(false)
    }
  }

  // 处理用户对AI建议的决定
  const handleUserDecision = (decision: 'accept' | 'reject' | 'modify', modifications?: string) => {
    const currentComparison = comparisons[currentComparisonIndex]
    if (!currentComparison) return

    AIContentProcessor.saveUserDecision(
      currentComparison.blockId,
      decision === 'accept' ? 'accepted' : decision === 'reject' ? 'rejected' : 'modified',
      modifications
    )

    // 如果接受建议，更新章节内容
    if (decision === 'accept') {
      const chapterIndex = currentComparison.blockId.split('_')[1]
      const chapterIndexNum = parseInt(chapterIndex)

      setMemoirData(prev => ({
        ...prev,
        chapters: prev.chapters.map((ch, index) =>
          index === chapterIndexNum
            ? { ...ch, content: currentComparison.enhanced, lastModified: new Date() }
            : ch
        ),
        lastModified: new Date()
      }))
    } else if (decision === 'modify' && modifications) {
      const chapterIndex = currentComparison.blockId.split('_')[1]
      const chapterIndexNum = parseInt(chapterIndex)

      setMemoirData(prev => ({
        ...prev,
        chapters: prev.chapters.map((ch, index) =>
          index === chapterIndexNum
            ? { ...ch, content: modifications, lastModified: new Date() }
            : ch
        ),
        lastModified: new Date()
      }))
    }

    // 移动到下一个对比
    if (currentComparisonIndex < comparisons.length - 1) {
      setCurrentComparisonIndex(currentComparisonIndex + 1)
    } else {
      // 所有对比完成，返回编辑模式
      setViewMode('edit')
      onContentChange()
    }
  }

  // 加载已有的处理结果
  useEffect(() => {
    const savedResult = AIContentProcessor.getProcessedContent()
    if (savedResult) {
      setProcessingResult(savedResult)
    }

    const savedComparisons = AIContentProcessor.getComparisons()
    if (savedComparisons.length > 0) {
      setComparisons(savedComparisons)
    }
  }, [])

  const renderEditMode = () => (
    <div className="space-y-6">
      {/* Memoir Header */}
      <Card>
        <CardHeader>
          <CardTitle>回忆录信息</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">标题</label>
            <input
              type="text"
              value={memoirData.title}
              onChange={(e) => handleTitleChange('title', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="回忆录标题"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">副标题</label>
            <input
              type="text"
              value={memoirData.subtitle}
              onChange={(e) => handleTitleChange('subtitle', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="副标题（可选）"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">作者</label>
            <input
              type="text"
              value={memoirData.author}
              onChange={(e) => handleTitleChange('author', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="作者姓名"
            />
          </div>
        </CardContent>
      </Card>

      {/* Chapters */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">章节内容</h2>
          <div className="text-sm text-gray-500">
            最后修改: {memoirData.lastModified.toLocaleString()}
          </div>
        </div>

        {memoirData.chapters.map((chapter, index) => (
          <Card key={chapter.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <button
                  onClick={() => toggleChapterExpansion(chapter.id)}
                  className="flex items-center space-x-2 text-left flex-1"
                >
                  {chapter.isExpanded ? (
                    <ChevronDown className="w-5 h-5 text-gray-400" />
                  ) : (
                    <ChevronRight className="w-5 h-5 text-gray-400" />
                  )}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      第{index + 1}章：{chapter.title}
                    </h3>
                    <p className="text-sm text-gray-500">
                      最后修改: {chapter.lastModified.toLocaleString()}
                    </p>
                  </div>
                </button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleChapterEdit(chapter.id)}
                  icon={<Edit3 className="w-4 h-4" />}
                >
                  编辑
                </Button>
              </div>
            </CardHeader>
            
            {chapter.isExpanded && (
              <CardContent>
                {editingChapter === chapter.id ? (
                  <div className="space-y-4">
                    <textarea
                      value={tempContent}
                      onChange={(e) => setTempContent(e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      rows={12}
                      placeholder="在此编辑章节内容..."
                    />
                    <div className="flex gap-2">
                      <Button onClick={handleChapterSave} icon={<Save className="w-4 h-4" />}>
                        保存
                      </Button>
                      <Button variant="outline" onClick={handleChapterCancel}>
                        取消
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="prose max-w-none">
                    <div className="whitespace-pre-wrap text-gray-700 bg-gray-50 p-4 rounded-lg">
                      {chapter.content}
                    </div>
                  </div>
                )}
              </CardContent>
            )}
          </Card>
        ))}
      </div>
    </div>
  )

  const renderPreviewMode = () => (
    <div className="max-w-4xl mx-auto">
      {/* Cover Page */}
      <div className="bg-white p-12 rounded-lg shadow-lg mb-8 text-center">
        <div className="space-y-6">
          <div className="w-24 h-24 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto">
            <BookOpen className="w-12 h-12 text-white" />
          </div>
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">{memoirData.title}</h1>
            {memoirData.subtitle && (
              <p className="text-xl text-gray-600 mb-4">{memoirData.subtitle}</p>
            )}
            <p className="text-lg text-gray-700">作者：{memoirData.author}</p>
          </div>
          <div className="text-sm text-gray-500">
            创建于 {memoirData.createdAt.toLocaleDateString()}
          </div>
        </div>
      </div>

      {/* Table of Contents */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>目录</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {memoirData.chapters.map((chapter, index) => (
              <div key={chapter.id} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                <span className="font-medium">第{index + 1}章：{chapter.title}</span>
                <span className="text-gray-500">{index + 1}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Chapters */}
      <div className="space-y-8">
        {memoirData.chapters.map((chapter, index) => (
          <Card key={chapter.id}>
            <CardHeader>
              <CardTitle className="text-2xl">
                第{index + 1}章：{chapter.title}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                  {chapter.content}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )

  // AI处理进度视图
  const renderAIProcessingMode = () => (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="w-6 h-6 text-blue-600" />
            <span>AI正在处理您的回忆录内容</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
            </div>
            <p className="text-lg font-medium text-gray-900 mb-2">{processingStatus}</p>
            <p className="text-sm text-gray-600">请稍候，AI正在分析和优化您的内容...</p>
          </div>

          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-600">
              <span>处理进度</span>
              <span>{Math.round(processingProgress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-blue-600 h-3 rounded-full transition-all duration-500 ease-out"
                style={{ width: `${processingProgress}%` }}
              ></div>
            </div>
          </div>

          {/* Processing Steps */}
          <div className="space-y-3">
            <div className={`flex items-center space-x-3 ${processingProgress >= 10 ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-6 h-6 rounded-full flex items-center justify-center ${processingProgress >= 10 ? 'bg-green-100' : 'bg-gray-100'}`}>
                {processingProgress >= 10 ? <Check className="w-4 h-4" /> : <div className="w-2 h-2 bg-gray-400 rounded-full"></div>}
              </div>
              <span>收集对话内容</span>
            </div>
            <div className={`flex items-center space-x-3 ${processingProgress >= 50 ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-6 h-6 rounded-full flex items-center justify-center ${processingProgress >= 50 ? 'bg-green-100' : 'bg-gray-100'}`}>
                {processingProgress >= 50 ? <Check className="w-4 h-4" /> : <div className="w-2 h-2 bg-gray-400 rounded-full"></div>}
              </div>
              <span>AI内容优化</span>
            </div>
            <div className={`flex items-center space-x-3 ${processingProgress >= 95 ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-6 h-6 rounded-full flex items-center justify-center ${processingProgress >= 95 ? 'bg-green-100' : 'bg-gray-100'}`}>
                {processingProgress >= 95 ? <Check className="w-4 h-4" /> : <div className="w-2 h-2 bg-gray-400 rounded-full"></div>}
              </div>
              <span>生成整体摘要</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  // 对比视图
  const renderComparisonMode = () => {
    const currentComparison = comparisons[currentComparisonIndex]
    if (!currentComparison) return null

    return (
      <div className="max-w-6xl mx-auto">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="w-6 h-6 text-green-600" />
                <span>AI内容优化对比</span>
              </CardTitle>
              <div className="text-sm text-gray-600">
                {currentComparisonIndex + 1} / {comparisons.length}
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Chapter Info */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-900">{currentComparison.chapterTitle}</h3>
              <p className="text-sm text-blue-700 mt-1">AI已对此章节内容进行优化，请查看对比结果</p>
            </div>

            {/* Before/After Comparison */}
            <div className="grid md:grid-cols-2 gap-6">
              {/* Original Content */}
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900 flex items-center space-x-2">
                  <ArrowLeft className="w-4 h-4" />
                  <span>原始内容</span>
                </h4>
                <div className="bg-gray-50 p-4 rounded-lg border">
                  <div className="whitespace-pre-wrap text-gray-700 text-sm leading-relaxed max-h-96 overflow-y-auto">
                    {currentComparison.original || '此章节暂无原始内容'}
                  </div>
                </div>
              </div>

              {/* Enhanced Content */}
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900 flex items-center space-x-2">
                  <ArrowRight className="w-4 h-4" />
                  <span>AI优化后</span>
                </h4>
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <div className="whitespace-pre-wrap text-gray-700 text-sm leading-relaxed max-h-96 overflow-y-auto">
                    {currentComparison.enhanced}
                  </div>
                </div>
              </div>
            </div>

            {/* Changes Summary */}
            {currentComparison.changes.length > 0 && (
              <div className="bg-yellow-50 p-4 rounded-lg">
                <h4 className="font-medium text-yellow-900 mb-2">主要改进:</h4>
                <ul className="space-y-1">
                  {currentComparison.changes.map((change, index) => (
                    <li key={index} className="text-sm text-yellow-800 flex items-start space-x-2">
                      <span className="w-1.5 h-1.5 bg-yellow-600 rounded-full mt-2 flex-shrink-0"></span>
                      <span>{change.description}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
              <Button
                onClick={() => handleUserDecision('accept')}
                variant="primary"
                icon={<Check className="w-4 h-4" />}
                className="flex-1"
              >
                接受AI优化
              </Button>
              <Button
                onClick={() => handleUserDecision('reject')}
                variant="outline"
                icon={<X className="w-4 h-4" />}
                className="flex-1"
              >
                保持原内容
              </Button>
              <Button
                onClick={() => {
                  const modifications = prompt('请输入您的修改内容:', currentComparison.enhanced)
                  if (modifications !== null) {
                    handleUserDecision('modify', modifications)
                  }
                }}
                variant="outline"
                icon={<Edit3 className="w-4 h-4" />}
                className="flex-1"
              >
                自定义修改
              </Button>
            </div>

            {/* Progress */}
            <div className="text-center text-sm text-gray-600">
              还有 {comparisons.length - currentComparisonIndex - 1} 个章节待处理
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Mode Toggle and Actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex gap-2">
          <Button
            onClick={() => setViewMode('edit')}
            variant={viewMode === 'edit' ? 'primary' : 'outline'}
            icon={<Edit3 className="w-4 h-4" />}
            disabled={isProcessing}
          >
            编辑模式
          </Button>
          <Button
            onClick={() => setViewMode('preview')}
            variant={viewMode === 'preview' ? 'primary' : 'outline'}
            icon={<Eye className="w-4 h-4" />}
            disabled={isProcessing}
          >
            预览模式
          </Button>
          <Button
            onClick={handleAIProcessing}
            variant={viewMode === 'ai-processing' || viewMode === 'comparison' ? 'primary' : 'outline'}
            icon={isProcessing ? <Loader2 className="w-4 h-4 animate-spin" /> : <Zap className="w-4 h-4" />}
            disabled={isProcessing}
          >
            {isProcessing ? 'AI处理中...' : 'AI优化内容'}
          </Button>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={handleExport}
            variant="outline"
            icon={<Download className="w-4 h-4" />}
          >
            导出
          </Button>
          <Button
            onClick={handleShare}
            variant="outline"
            icon={<Share2 className="w-4 h-4" />}
          >
            分享
          </Button>
        </div>
      </div>

      {/* Content */}
      {viewMode === 'edit' && renderEditMode()}
      {viewMode === 'preview' && renderPreviewMode()}
      {viewMode === 'ai-processing' && renderAIProcessingMode()}
      {viewMode === 'comparison' && renderComparisonMode()}

      {/* Processing Result Summary */}
      {processingResult && viewMode === 'edit' && (
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                <BarChart3 className="w-4 h-4 text-green-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-green-900 mb-2">AI处理完成</h3>
                <p className="text-sm text-green-800 mb-3">{processingResult.overallSummary}</p>
                <div className="flex items-center space-x-4 text-sm text-green-700">
                  <span>完成度: {processingResult.completionPercentage}%</span>
                  <span>处理章节: {processingResult.contentBlocks.length}</span>
                </div>
                {processingResult.suggestions.length > 0 && (
                  <div className="mt-3">
                    <h4 className="font-medium text-green-900 mb-1">建议:</h4>
                    <ul className="space-y-1">
                      {processingResult.suggestions.slice(0, 3).map((suggestion, index) => (
                        <li key={index} className="text-sm text-green-800 flex items-start space-x-2">
                          <span className="w-1 h-1 bg-green-600 rounded-full mt-2 flex-shrink-0"></span>
                          <span>{suggestion}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                <div className="mt-3 flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      const savedComparisons = AIContentProcessor.getComparisons()
                      if (savedComparisons.length > 0) {
                        setComparisons(savedComparisons)
                        setCurrentComparisonIndex(0)
                        setViewMode('comparison')
                      }
                    }}
                  >
                    查看对比
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      AIContentProcessor.clearProcessedData()
                      setProcessingResult(null)
                      setComparisons([])
                    }}
                  >
                    清除结果
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
