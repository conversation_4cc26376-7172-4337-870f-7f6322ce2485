'use client'

import { useState } from 'react'
import { Edit3, Eye, Download, Share2, BookOpen, ChevronDown, ChevronRight, Save } from 'lucide-react'
import { MemoirTemplate } from '@/lib/memoir-templates'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'

interface OrganizationPreviewProps {
  template: MemoirTemplate
  onContentChange: () => void
}

interface ChapterContent {
  id: string
  title: string
  content: string
  isExpanded: boolean
  lastModified: Date
}

interface MemoirData {
  title: string
  subtitle: string
  author: string
  chapters: ChapterContent[]
  coverImage?: string
  createdAt: Date
  lastModified: Date
}

export default function OrganizationPreview({ template, onContentChange }: Readonly<OrganizationPreviewProps>) {
  const [viewMode, setViewMode] = useState<'edit' | 'preview'>('edit')
  const [memoirData, setMemoirData] = useState<MemoirData>({
    title: `我的${template.name}`,
    subtitle: '珍贵回忆的记录',
    author: '作者姓名',
    chapters: template.chapters.map((chapter, index) => ({
      id: `chapter-${index}`,
      title: chapter.title,
      content: `这里是"${chapter.title}"的内容。您可以在这里编辑和整理从对话创作和原始记录中收集的内容。\n\n${chapter.description}\n\n请在此处添加您的具体内容...`,
      isExpanded: index === 0,
      lastModified: new Date()
    })),
    createdAt: new Date(),
    lastModified: new Date()
  })
  const [editingChapter, setEditingChapter] = useState<string | null>(null)
  const [tempContent, setTempContent] = useState('')

  const handleTitleChange = (field: 'title' | 'subtitle' | 'author', value: string) => {
    setMemoirData(prev => ({
      ...prev,
      [field]: value,
      lastModified: new Date()
    }))
    onContentChange()
  }

  const handleChapterEdit = (chapterId: string) => {
    const chapter = memoirData.chapters.find(ch => ch.id === chapterId)
    if (chapter) {
      setEditingChapter(chapterId)
      setTempContent(chapter.content)
    }
  }

  const handleChapterSave = () => {
    if (editingChapter) {
      setMemoirData(prev => ({
        ...prev,
        chapters: prev.chapters.map(ch =>
          ch.id === editingChapter
            ? { ...ch, content: tempContent, lastModified: new Date() }
            : ch
        ),
        lastModified: new Date()
      }))
      setEditingChapter(null)
      setTempContent('')
      onContentChange()
    }
  }

  const handleChapterCancel = () => {
    setEditingChapter(null)
    setTempContent('')
  }

  const toggleChapterExpansion = (chapterId: string) => {
    setMemoirData(prev => ({
      ...prev,
      chapters: prev.chapters.map(ch =>
        ch.id === chapterId ? { ...ch, isExpanded: !ch.isExpanded } : ch
      )
    }))
  }

  const handleExport = () => {
    // TODO: 实现导出功能
    console.log('Exporting memoir...', memoirData)
    alert('导出功能正在开发中...')
  }

  const handleShare = () => {
    // TODO: 实现分享功能
    console.log('Sharing memoir...', memoirData)
    alert('分享功能正在开发中...')
  }

  const renderEditMode = () => (
    <div className="space-y-6">
      {/* Memoir Header */}
      <Card>
        <CardHeader>
          <CardTitle>回忆录信息</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">标题</label>
            <input
              type="text"
              value={memoirData.title}
              onChange={(e) => handleTitleChange('title', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="回忆录标题"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">副标题</label>
            <input
              type="text"
              value={memoirData.subtitle}
              onChange={(e) => handleTitleChange('subtitle', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="副标题（可选）"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">作者</label>
            <input
              type="text"
              value={memoirData.author}
              onChange={(e) => handleTitleChange('author', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="作者姓名"
            />
          </div>
        </CardContent>
      </Card>

      {/* Chapters */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">章节内容</h2>
          <div className="text-sm text-gray-500">
            最后修改: {memoirData.lastModified.toLocaleString()}
          </div>
        </div>

        {memoirData.chapters.map((chapter, index) => (
          <Card key={chapter.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <button
                  onClick={() => toggleChapterExpansion(chapter.id)}
                  className="flex items-center space-x-2 text-left flex-1"
                >
                  {chapter.isExpanded ? (
                    <ChevronDown className="w-5 h-5 text-gray-400" />
                  ) : (
                    <ChevronRight className="w-5 h-5 text-gray-400" />
                  )}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      第{index + 1}章：{chapter.title}
                    </h3>
                    <p className="text-sm text-gray-500">
                      最后修改: {chapter.lastModified.toLocaleString()}
                    </p>
                  </div>
                </button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleChapterEdit(chapter.id)}
                  icon={<Edit3 className="w-4 h-4" />}
                >
                  编辑
                </Button>
              </div>
            </CardHeader>
            
            {chapter.isExpanded && (
              <CardContent>
                {editingChapter === chapter.id ? (
                  <div className="space-y-4">
                    <textarea
                      value={tempContent}
                      onChange={(e) => setTempContent(e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      rows={12}
                      placeholder="在此编辑章节内容..."
                    />
                    <div className="flex gap-2">
                      <Button onClick={handleChapterSave} icon={<Save className="w-4 h-4" />}>
                        保存
                      </Button>
                      <Button variant="outline" onClick={handleChapterCancel}>
                        取消
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="prose max-w-none">
                    <div className="whitespace-pre-wrap text-gray-700 bg-gray-50 p-4 rounded-lg">
                      {chapter.content}
                    </div>
                  </div>
                )}
              </CardContent>
            )}
          </Card>
        ))}
      </div>
    </div>
  )

  const renderPreviewMode = () => (
    <div className="max-w-4xl mx-auto">
      {/* Cover Page */}
      <div className="bg-white p-12 rounded-lg shadow-lg mb-8 text-center">
        <div className="space-y-6">
          <div className="w-24 h-24 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto">
            <BookOpen className="w-12 h-12 text-white" />
          </div>
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">{memoirData.title}</h1>
            {memoirData.subtitle && (
              <p className="text-xl text-gray-600 mb-4">{memoirData.subtitle}</p>
            )}
            <p className="text-lg text-gray-700">作者：{memoirData.author}</p>
          </div>
          <div className="text-sm text-gray-500">
            创建于 {memoirData.createdAt.toLocaleDateString()}
          </div>
        </div>
      </div>

      {/* Table of Contents */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>目录</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {memoirData.chapters.map((chapter, index) => (
              <div key={chapter.id} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                <span className="font-medium">第{index + 1}章：{chapter.title}</span>
                <span className="text-gray-500">{index + 1}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Chapters */}
      <div className="space-y-8">
        {memoirData.chapters.map((chapter, index) => (
          <Card key={chapter.id}>
            <CardHeader>
              <CardTitle className="text-2xl">
                第{index + 1}章：{chapter.title}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                  {chapter.content}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Mode Toggle and Actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex gap-2">
          <Button
            onClick={() => setViewMode('edit')}
            variant={viewMode === 'edit' ? 'primary' : 'outline'}
            icon={<Edit3 className="w-4 h-4" />}
          >
            编辑模式
          </Button>
          <Button
            onClick={() => setViewMode('preview')}
            variant={viewMode === 'preview' ? 'primary' : 'outline'}
            icon={<Eye className="w-4 h-4" />}
          >
            预览模式
          </Button>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={handleExport}
            variant="outline"
            icon={<Download className="w-4 h-4" />}
          >
            导出
          </Button>
          <Button
            onClick={handleShare}
            variant="outline"
            icon={<Share2 className="w-4 h-4" />}
          >
            分享
          </Button>
        </div>
      </div>

      {/* Content */}
      {viewMode === 'edit' ? renderEditMode() : renderPreviewMode()}
    </div>
  )
}
