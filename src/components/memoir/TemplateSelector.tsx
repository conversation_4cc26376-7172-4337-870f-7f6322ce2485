'use client'

import { useState } from 'react'
import { Search, Clock, Star, ChevronRight, Filter } from 'lucide-react'
import { MemoirTemplate, MemoirTemplateService } from '@/lib/memoir-templates'

interface TemplateSelectorProps {
  readonly onSelectTemplate: (template: MemoirTemplate) => void
}

export default function TemplateSelector({ onSelectTemplate }: TemplateSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all')
  const [showFilters, setShowFilters] = useState(false)

  // 获取过滤后的模板
  const getFilteredTemplates = () => {
    let templates = MemoirTemplateService.getAllTemplates()

    // 搜索过滤
    if (searchQuery) {
      templates = MemoirTemplateService.searchTemplates(searchQuery)
    }

    // 分类过滤
    if (selectedCategory !== 'all') {
      templates = templates.filter(template => template.category === selectedCategory)
    }

    // 难度过滤
    if (selectedDifficulty !== 'all') {
      templates = templates.filter(template => template.difficulty === selectedDifficulty)
    }

    return templates
  }

  const filteredTemplates = getFilteredTemplates()
  const recommendedTemplates = MemoirTemplateService.getRecommendedTemplates()

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">选择回忆录模板</h1>
        <p className="text-gray-600 text-lg">
          选择一个适合您的模板，开始记录人生故事
        </p>
      </div>

      {/* Search and Filters */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row gap-4 mb-4">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="搜索模板..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
            />
          </div>
          
          {/* Filter Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-2 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Filter className="w-5 h-5" />
            <span>筛选</span>
          </button>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="bg-gray-50 rounded-lg p-4 space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {/* Category Filter */}
              <div>
                <label htmlFor="category-filter" className="block text-sm font-medium text-gray-700 mb-2">分类</label>
                <select
                  id="category-filter"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">全部分类</option>
                  <option value="life">人生历程</option>
                  <option value="family">家族传承</option>
                  <option value="career">职业生涯</option>
                  <option value="travel">旅行见闻</option>
                </select>
              </div>

              {/* Difficulty Filter */}
              <div>
                <label htmlFor="difficulty-filter" className="block text-sm font-medium text-gray-700 mb-2">难度</label>
                <select
                  id="difficulty-filter"
                  value={selectedDifficulty}
                  onChange={(e) => setSelectedDifficulty(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">全部难度</option>
                  <option value="easy">简单</option>
                  <option value="medium">中等</option>
                  <option value="hard">困难</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Recommended Templates */}
      {!searchQuery && selectedCategory === 'all' && selectedDifficulty === 'all' && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <Star className="w-5 h-5 text-yellow-500 mr-2" />
            推荐模板
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {recommendedTemplates.map((template) => (
              <TemplateCard
                key={template.id}
                template={template}
                onSelect={onSelectTemplate}
                isRecommended
              />
            ))}
          </div>
        </div>
      )}

      {/* All Templates */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          {searchQuery || selectedCategory !== 'all' || selectedDifficulty !== 'all' 
            ? '搜索结果' 
            : '所有模板'
          }
          <span className="text-sm font-normal text-gray-500 ml-2">
            ({filteredTemplates.length} 个模板)
          </span>
        </h2>
        
        {filteredTemplates.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTemplates.map((template) => (
              <TemplateCard
                key={template.id}
                template={template}
                onSelect={onSelectTemplate}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📝</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到匹配的模板</h3>
            <p className="text-gray-600">
              尝试调整搜索条件或筛选选项
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

// 模板卡片组件
interface TemplateCardProps {
  readonly template: MemoirTemplate
  readonly onSelect: (template: MemoirTemplate) => void
  readonly isRecommended?: boolean
}

function TemplateCard({ template, onSelect, isRecommended }: TemplateCardProps) {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600 bg-green-50'
      case 'medium': return 'text-orange-600 bg-orange-50'
      case 'hard': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '简单'
      case 'medium': return '中等'
      case 'hard': return '困难'
      default: return difficulty
    }
  }

  const getCategoryText = (category: string) => {
    switch (category) {
      case 'life': return '人生历程'
      case 'family': return '家族传承'
      case 'career': return '职业生涯'
      case 'travel': return '旅行见闻'
      case 'custom': return '自定义'
      default: return category
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200 overflow-hidden">
      {isRecommended && (
        <div className="bg-gradient-to-r from-yellow-400 to-orange-400 text-white text-xs font-medium px-3 py-1 text-center">
          ⭐ 推荐
        </div>
      )}
      
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="text-3xl">{template.icon}</div>
            <div>
              <h3 className="font-semibold text-gray-900 text-lg">{template.name}</h3>
              <p className="text-sm text-gray-500">{getCategoryText(template.category)}</p>
            </div>
          </div>
        </div>

        {/* Description */}
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
          {template.description}
        </p>

        {/* Meta Info */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <div className="flex items-center space-x-1">
              <Clock className="w-4 h-4" />
              <span>{template.estimatedTime}</span>
            </div>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(template.difficulty)}`}>
              {getDifficultyText(template.difficulty)}
            </span>
          </div>
        </div>

        {/* Chapters Count */}
        <div className="text-sm text-gray-500 mb-4">
          包含 {template.chapters.length} 个章节
        </div>

        {/* Action Button */}
        <button
          onClick={() => onSelect(template)}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
        >
          <span>选择此模板</span>
          <ChevronRight className="w-4 h-4" />
        </button>
      </div>
    </div>
  )
}
