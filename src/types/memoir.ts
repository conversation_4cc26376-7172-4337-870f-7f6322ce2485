// 用户类型
export interface User {
  readonly id: string
  readonly email?: string
  readonly phone?: string
  readonly name?: string
  readonly avatar?: string
  readonly createdAt: string
  readonly lastLoginAt?: string
  readonly preferences?: UserPreferences
}

export interface UserPreferences {
  readonly language?: string
  readonly theme?: 'light' | 'dark'
  readonly voiceSettings?: VoiceSettings
  readonly notifications?: NotificationSettings
}

export interface VoiceSettings {
  readonly preferredLanguage: string
  readonly speechRate: number
  readonly speechPitch: number
  readonly autoSave: boolean
}

export interface NotificationSettings {
  readonly email: boolean
  readonly push: boolean
  readonly reminders: boolean
}

// 访客用户类型
export interface GuestUser {
  readonly id: string
  readonly deviceFingerprint: string
  readonly createdAt: string
  readonly expiresAt: string
  readonly limits: GuestLimits
}

export interface GuestLimits {
  readonly maxMemoirs: number
  readonly maxChaptersPerMemoir: number
  readonly maxContentBlocksPerChapter: number
  readonly validityDays: number
}

// 回忆录类型
export interface Memoir {
  readonly id: string
  readonly title: string
  readonly description?: string
  readonly authorId: string
  readonly templateId?: string
  readonly coverImage?: string
  readonly status: 'draft' | 'in_progress' | 'completed' | 'published'
  readonly privacy: 'private' | 'family' | 'public'
  readonly chapters: Chapter[]
  readonly tags?: string[]
  readonly metadata?: MemoirMetadata
  readonly createdAt: string
  readonly updatedAt: string
  readonly publishedAt?: string
}

export interface MemoirMetadata {
  readonly wordCount: number
  readonly estimatedReadingTime: number
  readonly completionPercentage: number
  readonly lastEditedChapter?: string
  readonly collaborators?: string[]
  readonly exportFormats?: string[]
}

// 章节类型
export interface Chapter {
  readonly id: string
  readonly title: string
  readonly description?: string
  readonly memoirId: string
  readonly order: number
  readonly status: 'not_started' | 'in_progress' | 'completed'
  readonly contentBlocks: ContentBlock[]
  readonly wordCount?: number
  readonly estimatedDuration?: number
  readonly metadata?: ChapterMetadata
  readonly createdAt: string
  readonly lastModified: string
}

export interface ChapterMetadata {
  readonly timeframe?: string
  readonly location?: string
  readonly people?: string[]
  readonly themes?: string[]
  readonly emotions?: string[]
  readonly keywords?: string[]
}

// 内容块类型
export interface ContentBlock {
  readonly id: string
  readonly type: 'text' | 'audio' | 'image' | 'video'
  readonly content: string
  readonly order: number
  readonly metadata?: ContentBlockMetadata
}

export interface ContentBlockMetadata {
  readonly audioUrl?: string
  readonly imageUrl?: string
  readonly videoUrl?: string
  readonly duration?: number
  readonly transcription?: string
  readonly aiProcessed?: boolean
  readonly confidence?: number
  readonly time?: string
  readonly location?: string
  readonly people?: string
  readonly date?: string
  readonly imageCaption?: string
  readonly createdAt?: string
  readonly lastModified?: string
}

// 模板类型
export interface MemoirTemplate {
  readonly id: string
  readonly name: string
  readonly description: string
  readonly icon: string
  readonly category: string
  readonly estimatedTime: string
  readonly difficulty: 'easy' | 'medium' | 'hard'
  readonly tags: string[]
  readonly chapters: ChapterTemplate[]
}

export interface ChapterTemplate {
  readonly id: string
  readonly title: string
  readonly description: string
  readonly order: number
  readonly suggestedDuration: number
  readonly prompts: string[]
}

// 相册类型
export interface PhotoAlbum {
  readonly id: string
  readonly title: string
  readonly description?: string
  readonly memoirId?: string
  readonly chapterId?: string
  readonly photos: Photo[]
  readonly coverPhoto?: string
  readonly status: 'draft' | 'completed'
  readonly createdAt: string
  readonly updatedAt: string
}

export interface Photo {
  readonly id: string
  readonly url: string
  readonly thumbnailUrl?: string
  readonly caption?: string
  readonly description?: string
  readonly metadata?: PhotoMetadata
  readonly order: number
  readonly createdAt: string
}

export interface PhotoMetadata {
  readonly originalName?: string
  readonly size?: number
  readonly dimensions?: {
    readonly width: number
    readonly height: number
  }
  readonly location?: string
  readonly dateTaken?: string
  readonly camera?: string
  readonly tags?: string[]
  readonly people?: string[]
  readonly audioNarration?: string
}

// API响应类型
export interface ApiResponse<T = unknown> {
  readonly success: boolean
  readonly data?: T
  readonly error?: string
  readonly message?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  readonly pagination: {
    readonly page: number
    readonly limit: number
    readonly total: number
    readonly totalPages: number
  }
}

// 导出类型
export interface ExportOptions {
  readonly format: 'pdf' | 'epub' | 'docx' | 'html'
  readonly includeImages: boolean
  readonly includeAudio: boolean
  readonly includeMetadata: boolean
  readonly template?: string
  readonly customization?: ExportCustomization
}

export interface ExportCustomization {
  readonly fontSize?: number
  readonly fontFamily?: string
  readonly pageSize?: string
  readonly margins?: string
  readonly coverPage?: boolean
  readonly tableOfContents?: boolean
  readonly watermark?: string
}

// 分享类型
export interface ShareSettings {
  readonly type: 'link' | 'email' | 'social'
  readonly privacy: 'private' | 'family' | 'public'
  readonly expiresAt?: string
  readonly password?: string
  readonly allowComments?: boolean
  readonly allowDownload?: boolean
}

// 搜索类型
export interface SearchFilters {
  readonly query?: string
  readonly category?: string
  readonly dateRange?: {
    readonly start: string
    readonly end: string
  }
  readonly tags?: string[]
  readonly status?: string[]
  readonly author?: string
}

export interface SearchResult {
  readonly type: 'memoir' | 'chapter' | 'content'
  readonly id: string
  readonly title: string
  readonly excerpt: string
  readonly relevance: number
  readonly highlights: string[]
  readonly metadata: Record<string, unknown>
}
