'use client'

import { useEffect, useState, Suspense } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { Loader2, AlertCircle, CheckCircle } from 'lucide-react'
import { createWeChatAuthService } from '@/lib/wechat-auth'
import { supabase } from '@/lib/supabase'

function WeChatCallbackContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState('')

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get('code')
        // const state = searchParams.get('state')

        if (!code) {
          throw new Error('未获取到授权码')
        }

        setMessage('正在处理微信授权...')

        const wechatService = createWeChatAuthService()
        
        // 检查是否为模拟服务
        if ('handleAuthCallback' in wechatService) {
          const result = await wechatService.handleAuthCallback(code)
          
          if (!result.success || !result.userInfo) {
            throw new Error(result.error || '微信授权失败')
          }

          setMessage('正在创建用户账户...')

          // 检查用户是否已存在
          const { data: existingUser } = await supabase
            .from('profiles')
            .select('*')
            .eq('wechat_openid', result.userInfo.openid)
            .single()

          // let userId: string

          if (existingUser) {
            // userId = existingUser.id
            setMessage('登录成功，正在跳转...')
          } else {
            // 创建新用户
            const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
              email: `${result.userInfo.openid}@wechat.local`,
              email_confirm: true,
              user_metadata: {
                auth_method: 'wechat',
                wechat_openid: result.userInfo.openid,
                wechat_unionid: result.userInfo.unionid,
                wechat_nickname: result.userInfo.nickname,
                full_name: result.userInfo.nickname
              }
            })

            if (createError || !newUser.user) {
              throw new Error('创建用户失败')
            }

            // userId = newUser.user.id
            setMessage('注册成功，正在跳转...')
          }

          // 生成登录会话
          const { data: sessionData, error: sessionError } = await supabase.auth.admin.generateLink({
            type: 'magiclink',
            email: `${result.userInfo.openid}@wechat.local`,
            options: {
              redirectTo: process.env.NEXT_PUBLIC_APP_URL
            }
          })

          if (sessionError || !sessionData) {
            throw new Error('生成登录会话失败')
          }

          setStatus('success')
          setMessage('微信登录成功！')

          // 跳转到应用主页
          setTimeout(() => {
            if (sessionData.properties?.action_link) {
              window.location.href = sessionData.properties.action_link
            } else {
              router.push('/')
            }
          }, 2000)

        } else {
          throw new Error('微信服务配置错误')
        }

      } catch (error) {
        console.error('微信授权回调处理失败:', error)
        setStatus('error')
        setMessage(error instanceof Error ? error.message : '微信登录失败')
      }
    }

    handleCallback()
  }, [searchParams, router])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
          {status === 'loading' && (
            <>
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Loader2 className="w-8 h-8 text-green-600 animate-spin" />
              </div>
              <h2 className="text-xl font-bold text-gray-900 mb-2">处理中</h2>
              <p className="text-gray-600">{message}</p>
            </>
          )}

          {status === 'success' && (
            <>
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <h2 className="text-xl font-bold text-gray-900 mb-2">登录成功</h2>
              <p className="text-gray-600 mb-4">{message}</p>
              <div className="flex items-center justify-center">
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                <span className="text-sm text-gray-500">正在跳转...</span>
              </div>
            </>
          )}

          {status === 'error' && (
            <>
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <AlertCircle className="w-8 h-8 text-red-600" />
              </div>
              <h2 className="text-xl font-bold text-gray-900 mb-2">登录失败</h2>
              <p className="text-gray-600 mb-6">{message}</p>
              <button
                onClick={() => router.push('/')}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                返回登录页
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default function WeChatCallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
            </div>
            <h2 className="text-xl font-bold text-gray-900 mb-2">加载中</h2>
            <p className="text-gray-600">正在处理微信授权...</p>
          </div>
        </div>
      </div>
    }>
      <WeChatCallbackContent />
    </Suspense>
  )
}
