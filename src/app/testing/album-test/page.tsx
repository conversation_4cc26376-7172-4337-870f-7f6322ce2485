'use client'

import { useState, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { Play, Pause, Upload } from 'lucide-react'
import { AlbumManager, type AlbumPhoto } from '@/lib/album-manager'
import { BackgroundMusicManager, AudioPlayerManager } from '@/lib/background-music'

export default function AlbumTestPage() {
  const router = useRouter()
  const [testPhotos, setTestPhotos] = useState<AlbumPhoto[]>([])
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentMusic, setCurrentMusic] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 测试图片上传和显示
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return

    Array.from(files).forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader()
        reader.onload = (e) => {
          const newPhoto: AlbumPhoto = {
            id: `test-photo-${Date.now()}-${Math.random()}`,
            url: e.target?.result as string,
            caption: `测试照片 - ${file.name}`,
            voiceNote: null,
            order: testPhotos.length,
            metadata: {
              filename: file.name,
              size: file.size,
              type: file.type,
              uploadDate: new Date()
            }
          }
          setTestPhotos(prev => [...prev, newPhoto])
        }
        reader.readAsDataURL(file)
      }
    })
  }

  // 测试音乐播放
  const testMusicPlayback = async (musicId: string) => {
    try {
      const music = BackgroundMusicManager.getMusicById(musicId)
      if (!music) {
        alert('音乐未找到')
        return
      }

      if (isPlaying && currentMusic === musicId) {
        AudioPlayerManager.pauseMusic()
        setIsPlaying(false)
      } else {
        await AudioPlayerManager.playMusic(music, 0.5)
        setIsPlaying(true)
        setCurrentMusic(musicId)
      }
    } catch (error) {
      console.error('Music test error:', error)
      alert(error instanceof Error ? error.message : '音乐播放测试失败')
    }
  }

  // 测试相册创建和保存
  const testAlbumCreation = () => {
    if (testPhotos.length === 0) {
      alert('请先上传一些测试照片')
      return
    }

    try {
      const album = AlbumManager.createAlbum('测试相册', '这是一个测试相册')
      
      // 更新相册信息
      const updatedAlbum = {
        ...album,
        photos: testPhotos,
        totalPhotos: testPhotos.length,
        templateId: 'classic',
        backgroundMusicId: 'warm-memories'
      }

      AlbumManager.saveAlbum(updatedAlbum)
      alert(`相册创建成功！ID: ${album.id}`)
      
      // 跳转到编辑页面
      router.push(`/album/edit/${album.id}`)
    } catch (error) {
      console.error('Album creation test error:', error)
      alert('相册创建测试失败')
    }
  }

  const musicList = BackgroundMusicManager.getAllMusic()

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">相册功能测试</h1>
          
          {/* 图片上传测试 */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">图片上传和显示测试</h2>
            <button
              onClick={() => fileInputRef.current?.click()}
              className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors mb-4"
            >
              <Upload className="w-4 h-4" />
              <span>上传测试图片</span>
            </button>
            
            {testPhotos.length > 0 && (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {testPhotos.map((photo) => (
                  <div key={photo.id} className="relative">
                    <img
                      src={photo.url}
                      alt={photo.caption}
                      className="w-full h-32 object-cover rounded-lg"
                    />
                    <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                      {photo.metadata.filename}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 音乐播放测试 */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">背景音乐播放测试</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {musicList.slice(0, 4).map((music) => (
                <div key={music.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-900">{music.name}</h3>
                      <p className="text-sm text-gray-600">{music.description}</p>
                      <span className={`inline-block px-2 py-1 rounded text-xs mt-2 ${BackgroundMusicManager.getGenreColor(music.genre)}`}>
                        {BackgroundMusicManager.getGenreLabel(music.genre)}
                      </span>
                    </div>
                    <button
                      onClick={() => testMusicPlayback(music.id)}
                      className={`p-2 rounded-full transition-colors ${
                        isPlaying && currentMusic === music.id
                          ? 'bg-red-600 text-white hover:bg-red-700'
                          : 'bg-blue-600 text-white hover:bg-blue-700'
                      }`}
                    >
                      {isPlaying && currentMusic === music.id ? (
                        <Pause className="w-4 h-4" />
                      ) : (
                        <Play className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 相册创建测试 */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">相册创建和保存测试</h2>
            <button
              onClick={testAlbumCreation}
              disabled={testPhotos.length === 0}
              className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              创建测试相册并跳转到编辑页面
            </button>
          </div>

          {/* 测试结果显示 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-2">测试状态</h3>
            <ul className="space-y-1 text-sm">
              <li className={testPhotos.length > 0 ? 'text-green-600' : 'text-gray-500'}>
                ✓ 图片上传: {testPhotos.length} 张照片已上传
              </li>
              <li className={isPlaying ? 'text-green-600' : 'text-gray-500'}>
                ♪ 音乐播放: {isPlaying ? `正在播放 ${currentMusic}` : '未播放'}
              </li>
              <li className="text-gray-500">
                📱 相册功能: 点击&quot;创建测试相册&quot;按钮进行测试
              </li>
            </ul>
          </div>
        </div>

        {/* 返回按钮 */}
        <div className="text-center">
          <button
            onClick={() => router.back()}
            className="text-blue-600 hover:text-blue-800 transition-colors"
          >
            ← 返回上一页
          </button>
        </div>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*"
        onChange={handleImageUpload}
        className="hidden"
      />
    </div>
  )
}
