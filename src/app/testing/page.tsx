'use client'

import { useState } from 'react'
import { ArrowLeft, TestTube } from 'lucide-react'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import TestingPanel from '@/components/testing/TestingPanel'

export default function TestingPage() {
  const [showAdvanced, setShowAdvanced] = useState(false)

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/">
                <Button variant="ghost" size="sm" icon={<ArrowLeft className="w-4 h-4" />}>
                  返回首页
                </Button>
              </Link>
              <div className="flex items-center space-x-2">
                <TestTube className="w-6 h-6 text-blue-600" />
                <h1 className="text-xl font-semibold text-gray-900">测试与优化</h1>
              </div>
            </div>

            <Button
              variant="outline"
              onClick={() => setShowAdvanced(!showAdvanced)}
            >
              {showAdvanced ? '简化视图' : '高级视图'}
            </Button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>测试与优化工具</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-6">
                这个页面提供了全面的测试和优化工具，帮助确保应用在各种设备和环境下都能提供最佳的用户体验。
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-medium text-blue-900 mb-2">性能测试</h3>
                  <p className="text-sm text-blue-700">
                    监控页面加载时间、内存使用、网络状态等关键性能指标
                  </p>
                </div>

                <div className="bg-green-50 p-4 rounded-lg">
                  <h3 className="font-medium text-green-900 mb-2">响应式测试</h3>
                  <p className="text-sm text-green-700">
                    验证在不同屏幕尺寸和设备上的显示效果和交互体验
                  </p>
                </div>

                <div className="bg-purple-50 p-4 rounded-lg">
                  <h3 className="font-medium text-purple-900 mb-2">无障碍测试</h3>
                  <p className="text-sm text-purple-700">
                    检查颜色对比度、焦点指示、键盘导航等无障碍功能
                  </p>
                </div>

                <div className="bg-orange-50 p-4 rounded-lg">
                  <h3 className="font-medium text-orange-900 mb-2">兼容性测试</h3>
                  <p className="text-sm text-orange-700">
                    确保在主流浏览器和操作系统上的兼容性和稳定性
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <TestingPanel />

          {showAdvanced && (
            <Card>
              <CardHeader>
                <CardTitle>高级测试选项</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">老年用户体验测试</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-4 border border-gray-200 rounded-lg">
                        <h4 className="font-medium text-gray-900 mb-2">字体大小测试</h4>
                        <p className="text-sm text-gray-600 mb-3">
                          验证在不同字体大小设置下的可读性
                        </p>
                        <div className="space-y-2">
                          <div className="text-sm">正常大小 (16px)</div>
                          <div className="text-base">中等大小 (18px)</div>
                          <div className="text-lg">大字体 (20px)</div>
                          <div className="text-xl">特大字体 (24px)</div>
                        </div>
                      </div>

                      <div className="p-4 border border-gray-200 rounded-lg">
                        <h4 className="font-medium text-gray-900 mb-2">按钮大小测试</h4>
                        <p className="text-sm text-gray-600 mb-3">
                          确保按钮足够大，便于点击
                        </p>
                        <div className="space-y-2">
                          <Button size="sm">小按钮</Button>
                          <Button size="md">中等按钮</Button>
                          <Button size="lg">大按钮</Button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">错误处理测试</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Button
                        variant="outline"
                        onClick={() => {
                          throw new Error('测试错误')
                        }}
                      >
                        触发错误
                      </Button>

                      <Button
                        variant="outline"
                        onClick={() => {
                          fetch('/non-existent-api').catch(console.error)
                        }}
                      >
                        网络错误
                      </Button>

                      <Button
                        variant="outline"
                        onClick={() => {
                          console.log('测试完成')
                        }}
                      >
                        测试完成
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>测试建议</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">定期测试</h4>
                  <p className="text-sm text-blue-700">
                    建议每次更新后都运行完整的测试套件。
                  </p>
                </div>

                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h4 className="font-medium text-green-900 mb-2">用户测试</h4>
                  <p className="text-sm text-green-700">
                    邀请真实的老年用户参与测试。
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}