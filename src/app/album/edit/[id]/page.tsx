'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter, useParams } from 'next/navigation'
import {
  ArrowLeft,
  Save,
  Eye,
  Settings,
  Music,
  Image as ImageIcon,
  Plus,
  Trash2,
  Volume2,
  VolumeX,
  Play,
  Pause,
  Palette,
  Grid
} from 'lucide-react'
import { AlbumManager, type Album, type AlbumPhoto } from '@/lib/album-manager'
import { albumTemplates, type AlbumTemplate, getTemplateById, generateTemplateCSS } from '@/lib/album-templates'
import { BackgroundMusicManager, AudioPlayerManager, type BackgroundMusic } from '@/lib/background-music'

export default function AlbumEditPage() {
  const router = useRouter()
  const params = useParams()
  const albumId = params.id as string
  
  const [album, setAlbum] = useState<Album | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState<'photos' | 'template' | 'music' | 'settings'>('photos')
  const [selectedTemplate, setSelectedTemplate] = useState<AlbumTemplate>(albumTemplates[0])
  const [selectedMusic, setSelectedMusic] = useState<BackgroundMusic | null>(null)
  const [musicVolume, setMusicVolume] = useState(0.5)
  const [isPlaying, setIsPlaying] = useState(false)
  const [draggedPhoto, setDraggedPhoto] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    loadAlbum()
  }, [albumId])

  const loadAlbum = async () => {
    try {
      setLoading(true)
      const albumData = AlbumManager.getAlbum(albumId)
      
      if (!albumData) {
        router.push('/404')
        return
      }

      setAlbum(albumData)
      
      // 加载保存的模板
      if (albumData.templateId) {
        const template = getTemplateById(albumData.templateId)
        if (template) {
          setSelectedTemplate(template)
        }
      }

      // 加载保存的背景音乐
      if (albumData.backgroundMusicId) {
        const music = BackgroundMusicManager.getMusicById(albumData.backgroundMusicId)
        if (music) {
          setSelectedMusic(music)
        }
      }

    } catch (error) {
      console.error('Failed to load album:', error)
      router.push('/404')
    } finally {
      setLoading(false)
    }
  }

  const saveAlbum = async () => {
    if (!album) return

    try {
      setSaving(true)
      
      const updatedAlbum: Album = {
        ...album,
        templateId: selectedTemplate.id,
        backgroundMusicId: selectedMusic?.id,
        lastModified: new Date()
      }

      AlbumManager.saveAlbum(updatedAlbum)
      setAlbum(updatedAlbum)
      
      // 显示保存成功提示
      alert('相册已保存')
    } catch (error) {
      console.error('Failed to save album:', error)
      alert('保存失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  const handleAddPhotos = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || !album) return

    Array.from(files).forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader()
        reader.onload = (e) => {
          const newPhoto: AlbumPhoto = {
            id: `photo-${Date.now()}-${Math.random()}`,
            url: e.target?.result as string,
            caption: '',
            voiceNote: null,
            order: album.photos.length,
            metadata: {
              filename: file.name,
              size: file.size,
              type: file.type,
              uploadDate: new Date()
            }
          }

          setAlbum(prev => prev ? {
            ...prev,
            photos: [...prev.photos, newPhoto],
            totalPhotos: prev.totalPhotos + 1
          } : null)
        }
        reader.readAsDataURL(file)
      }
    })

    // 清空文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleDeletePhoto = (photoId: string) => {
    if (!album) return
    
    if (window.confirm('确定要删除这张照片吗？')) {
      setAlbum(prev => prev ? {
        ...prev,
        photos: prev.photos.filter(photo => photo.id !== photoId),
        totalPhotos: prev.totalPhotos - 1
      } : null)
    }
  }

  const handlePhotoReorder = (draggedId: string, targetId: string) => {
    if (!album || draggedId === targetId) return

    const photos = [...album.photos]
    const draggedIndex = photos.findIndex(p => p.id === draggedId)
    const targetIndex = photos.findIndex(p => p.id === targetId)

    if (draggedIndex === -1 || targetIndex === -1) return

    // 重新排序
    const [draggedPhoto] = photos.splice(draggedIndex, 1)
    photos.splice(targetIndex, 0, draggedPhoto)

    // 更新order字段
    photos.forEach((photo, index) => {
      photo.order = index
    })

    setAlbum(prev => prev ? { ...prev, photos } : null)
  }

  const handleTemplateChange = (template: AlbumTemplate) => {
    setSelectedTemplate(template)
  }

  const handleMusicSelect = async (music: BackgroundMusic) => {
    setSelectedMusic(music)
    
    // 如果正在播放其他音乐，先停止
    if (isPlaying) {
      AudioPlayerManager.stopMusic()
      setIsPlaying(false)
    }
  }

  const toggleMusicPlayback = async () => {
    if (!selectedMusic) return

    try {
      if (isPlaying) {
        AudioPlayerManager.pauseMusic()
        setIsPlaying(false)
      } else {
        await AudioPlayerManager.playMusic(selectedMusic, musicVolume)
        setIsPlaying(true)
      }
    } catch (error) {
      console.error('Music playback error:', error)
      alert('音乐播放失败')
    }
  }

  const handleVolumeChange = (volume: number) => {
    setMusicVolume(volume)
    AudioPlayerManager.setVolume(volume)
  }

  const handlePreview = () => {
    // 保存当前状态并跳转到预览页面
    saveAlbum().then(() => {
      window.open(`/album/preview/${albumId}`, '_blank')
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!album) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">相册未找到</h1>
          <button
            onClick={() => router.back()}
            className="text-blue-600 hover:text-blue-800"
          >
            返回上一页
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">{album.title}</h1>
                <p className="text-sm text-gray-600">{album.photos.length} 张照片</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={handlePreview}
                className="flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Eye className="w-4 h-4" />
                <span>预览</span>
              </button>
              <button
                onClick={saveAlbum}
                disabled={saving}
                className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                <Save className="w-4 h-4" />
                <span>{saving ? '保存中...' : '保存'}</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm overflow-hidden">
              <div className="border-b border-gray-200">
                <nav className="flex flex-col">
                  {[
                    { id: 'photos', label: '照片管理', icon: ImageIcon },
                    { id: 'template', label: '模板样式', icon: Palette },
                    { id: 'music', label: '背景音乐', icon: Music },
                    { id: 'settings', label: '相册设置', icon: Settings }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id as typeof activeTab)}
                      className={`flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors ${
                        activeTab === tab.id ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : 'text-gray-700'
                      }`}
                    >
                      <tab.icon className="w-5 h-5" />
                      <span>{tab.label}</span>
                    </button>
                  ))}
                </nav>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {activeTab === 'photos' && (
              <PhotosTab
                album={album}
                onAddPhotos={() => fileInputRef.current?.click()}
                onDeletePhoto={handleDeletePhoto}
                onPhotoReorder={handlePhotoReorder}
                draggedPhoto={draggedPhoto}
                setDraggedPhoto={setDraggedPhoto}
              />
            )}

            {activeTab === 'template' && (
              <TemplateTab
                selectedTemplate={selectedTemplate}
                onTemplateChange={handleTemplateChange}
                album={album}
              />
            )}

            {activeTab === 'music' && (
              <MusicTab
                selectedMusic={selectedMusic}
                onMusicSelect={handleMusicSelect}
                isPlaying={isPlaying}
                onTogglePlayback={toggleMusicPlayback}
                volume={musicVolume}
                onVolumeChange={handleVolumeChange}
              />
            )}

            {activeTab === 'settings' && (
              <SettingsTab
                album={album}
                onAlbumUpdate={setAlbum}
              />
            )}
          </div>
        </div>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*"
        onChange={handleAddPhotos}
        className="hidden"
      />

      {/* Template CSS */}
      <style dangerouslySetInnerHTML={{
        __html: generateTemplateCSS(selectedTemplate)
      }} />
    </div>
  )
}

// 照片管理标签页组件
function PhotosTab({
  album,
  onAddPhotos,
  onDeletePhoto,
  onPhotoReorder,
  draggedPhoto,
  setDraggedPhoto
}: {
  album: Album
  onAddPhotos: () => void
  onDeletePhoto: (photoId: string) => void
  onPhotoReorder: (draggedId: string, targetId: string) => void
  draggedPhoto: string | null
  setDraggedPhoto: (id: string | null) => void
}) {
  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">照片管理</h2>
        <button
          onClick={onAddPhotos}
          className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>添加照片</span>
        </button>
      </div>

      {album.photos.length === 0 ? (
        <div className="text-center py-12">
          <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">还没有照片</h3>
          <p className="text-gray-600 mb-6">点击&quot;添加照片&quot;开始创建您的相册</p>
          <button
            onClick={onAddPhotos}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            添加第一张照片
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {album.photos
            .sort((a, b) => a.order - b.order)
            .map((photo) => (
              <div
                key={photo.id}
                draggable
                onDragStart={() => setDraggedPhoto(photo.id)}
                onDragEnd={() => setDraggedPhoto(null)}
                onDragOver={(e) => e.preventDefault()}
                onDrop={() => {
                  if (draggedPhoto) {
                    onPhotoReorder(draggedPhoto, photo.id)
                  }
                }}
                className={`relative group cursor-move rounded-lg overflow-hidden ${
                  draggedPhoto === photo.id ? 'opacity-50' : ''
                }`}
              >
                <img
                  src={photo.url}
                  alt={photo.caption || '相册照片'}
                  className="w-full h-32 object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2">
                    <button
                      onClick={() => onDeletePhoto(photo.id)}
                      className="p-2 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                  {photo.order + 1}
                </div>
              </div>
            ))}
        </div>
      )}
    </div>
  )
}

// 模板选择标签页组件
function TemplateTab({
  selectedTemplate,
  onTemplateChange,
  album
}: {
  selectedTemplate: AlbumTemplate
  onTemplateChange: (template: AlbumTemplate) => void
  album: Album
}) {
  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">选择模板样式</h2>
        <p className="text-gray-600">选择一个模板来改变相册的外观和布局</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {albumTemplates.map((template) => (
          <div
            key={template.id}
            onClick={() => onTemplateChange(template)}
            className={`cursor-pointer rounded-lg border-2 transition-all duration-200 ${
              selectedTemplate.id === template.id
                ? 'border-blue-600 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="p-4">
              <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-3 flex items-center justify-center">
                <Grid className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-1">{template.name}</h3>
              <p className="text-sm text-gray-600 mb-3">{template.description}</p>
              <div className="flex flex-wrap gap-2">
                <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                  {template.style.layout}
                </span>
                <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                  {template.style.columns} 列
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 预览区域 */}
      <div className="border-t border-gray-200 pt-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">预览效果</h3>
        <div
          className="album-container p-6 rounded-lg border border-gray-200"
          style={{ backgroundColor: selectedTemplate.colors.background }}
        >
          <h4
            className="album-title mb-4"
            style={{
              color: selectedTemplate.colors.primary,
              fontFamily: selectedTemplate.typography.titleFont,
              fontSize: selectedTemplate.typography.titleSize,
              fontWeight: selectedTemplate.typography.titleWeight
            }}
          >
            {album.title}
          </h4>
          <div className="photo-grid">
            {album.photos.slice(0, 6).map((photo) => (
              <div key={photo.id} className="photo-item overflow-hidden">
                <img
                  src={photo.url}
                  alt={photo.caption || '预览照片'}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// 背景音乐标签页组件
function MusicTab({
  selectedMusic,
  onMusicSelect,
  isPlaying,
  onTogglePlayback,
  volume,
  onVolumeChange
}: {
  selectedMusic: BackgroundMusic | null
  onMusicSelect: (music: BackgroundMusic) => void
  isPlaying: boolean
  onTogglePlayback: () => void
  volume: number
  onVolumeChange: (volume: number) => void
}) {
  const [musicList] = useState(BackgroundMusicManager.getAllMusic())
  const [selectedGenre, setSelectedGenre] = useState<string>('all')
  const fileInputRef = useRef<HTMLInputElement>(null)

  const filteredMusic = selectedGenre === 'all'
    ? musicList
    : musicList.filter(music => music.genre === selectedGenre)

  const handleCustomMusicUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    try {
      const customMusic = await BackgroundMusicManager.addCustomMusic(file)
      onMusicSelect(customMusic)
      alert('自定义音乐添加成功！')
    } catch (error) {
      alert(error instanceof Error ? error.message : '音乐上传失败')
    }

    // 清空文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">背景音乐</h2>
          <p className="text-gray-600">为您的相册选择合适的背景音乐</p>
        </div>
        <button
          onClick={() => fileInputRef.current?.click()}
          className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>上传音乐</span>
        </button>
      </div>

      {/* 当前选择的音乐 */}
      {selectedMusic && (
        <div className="bg-blue-50 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-gray-900">{selectedMusic.name}</h3>
              <p className="text-sm text-gray-600">{selectedMusic.description}</p>
              <div className="flex items-center space-x-2 mt-2">
                <span className={`px-2 py-1 rounded text-xs ${BackgroundMusicManager.getGenreColor(selectedMusic.genre)}`}>
                  {BackgroundMusicManager.getGenreLabel(selectedMusic.genre)}
                </span>
                <span className="text-xs text-gray-500">
                  {BackgroundMusicManager.formatDuration(selectedMusic.duration)}
                </span>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <VolumeX className="w-4 h-4 text-gray-500" />
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={volume}
                  onChange={(e) => onVolumeChange(parseFloat(e.target.value))}
                  className="w-20"
                />
                <Volume2 className="w-4 h-4 text-gray-500" />
              </div>
              <button
                onClick={onTogglePlayback}
                className="p-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
              >
                {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 类型筛选 */}
      <div className="flex flex-wrap gap-2 mb-6">
        <button
          onClick={() => setSelectedGenre('all')}
          className={`px-3 py-1 rounded-full text-sm transition-colors ${
            selectedGenre === 'all' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          全部
        </button>
        {['warm', 'nostalgic', 'cheerful', 'peaceful', 'romantic', 'family'].map((genre) => (
          <button
            key={genre}
            onClick={() => setSelectedGenre(genre)}
            className={`px-3 py-1 rounded-full text-sm transition-colors ${
              selectedGenre === genre ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {BackgroundMusicManager.getGenreLabel(genre as BackgroundMusic['genre'])}
          </button>
        ))}
      </div>

      {/* 音乐列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {filteredMusic.map((music) => (
          <button
            key={music.id}
            onClick={() => onMusicSelect(music)}
            className={`p-4 rounded-lg border-2 text-left transition-all duration-200 ${
              selectedMusic?.id === music.id
                ? 'border-blue-600 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">{music.name}</h4>
                <p className="text-sm text-gray-600 mt-1">{music.description}</p>
                <div className="flex items-center space-x-2 mt-2">
                  <span className={`px-2 py-1 rounded text-xs ${BackgroundMusicManager.getGenreColor(music.genre)}`}>
                    {BackgroundMusicManager.getGenreLabel(music.genre)}
                  </span>
                  <span className="text-xs text-gray-500">
                    {BackgroundMusicManager.formatDuration(music.duration)}
                  </span>
                  {music.isCustom && (
                    <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded">
                      自定义
                    </span>
                  )}
                </div>
              </div>
              <Music className="w-5 h-5 text-gray-400" />
            </div>
          </button>
        ))}
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="audio/*"
        onChange={handleCustomMusicUpload}
        className="hidden"
      />
    </div>
  )
}

// 设置标签页组件
function SettingsTab({
  album,
  onAlbumUpdate
}: {
  album: Album
  onAlbumUpdate: (album: Album) => void
}) {
  const [title, setTitle] = useState(album.title)
  const [description, setDescription] = useState(album.description || '')

  const handleSave = () => {
    const updatedAlbum = {
      ...album,
      title,
      description,
      lastModified: new Date()
    }
    onAlbumUpdate(updatedAlbum)
    alert('设置已保存')
  }

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-6">相册设置</h2>

      <div className="space-y-6">
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
            相册标题
          </label>
          <input
            id="title"
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="输入相册标题"
          />
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
            相册描述
          </label>
          <textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="输入相册描述"
          />
        </div>

        <div className="pt-4">
          <button
            onClick={handleSave}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            保存设置
          </button>
        </div>
      </div>
    </div>
  )
}
