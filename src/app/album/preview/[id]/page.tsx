'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter, useParams } from 'next/navigation'
import {
  ArrowLeft,
  Play,
  Pause,
  SkipBack,
  SkipForward,
  Volume2,
  VolumeX,
  Maximize,
  Minimize,
  Share2,
  Download,
  Settings
} from 'lucide-react'
import { AlbumManager, type Album } from '@/lib/album-manager'
import { getTemplateById, generateTemplateCSS } from '@/lib/album-templates'
import { BackgroundMusicManager, AudioPlayerManager } from '@/lib/background-music'

export default function AlbumPreviewPage() {
  const router = useRouter()
  const params = useParams()
  const albumId = params.id as string
  
  const [album, setAlbum] = useState<Album | null>(null)
  const [loading, setLoading] = useState(true)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0)
  const [isAutoPlay, setIsAutoPlay] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [volume, setVolume] = useState(0.5)
  const [isMuted, setIsMuted] = useState(false)
  const [showControls, setShowControls] = useState(true)
  const autoPlayIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    loadAlbum()
    return () => {
      // 清理定时器和音频
      if (autoPlayIntervalRef.current) {
        clearInterval(autoPlayIntervalRef.current)
      }
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }
      AudioPlayerManager.stopMusic()
    }
  }, [albumId])

  useEffect(() => {
    // 自动播放逻辑
    if (isAutoPlay && album && album.photos.length > 0) {
      autoPlayIntervalRef.current = setInterval(() => {
        setCurrentPhotoIndex(prev => (prev + 1) % album.photos.length)
      }, 3000) // 每3秒切换一张照片
    } else if (autoPlayIntervalRef.current) {
      clearInterval(autoPlayIntervalRef.current)
      autoPlayIntervalRef.current = null
    }

    return () => {
      if (autoPlayIntervalRef.current) {
        clearInterval(autoPlayIntervalRef.current)
      }
    }
  }, [isAutoPlay, album])

  useEffect(() => {
    // 控制栏自动隐藏
    if (isFullscreen) {
      const resetTimeout = () => {
        if (controlsTimeoutRef.current) {
          clearTimeout(controlsTimeoutRef.current)
        }
        setShowControls(true)
        controlsTimeoutRef.current = setTimeout(() => {
          setShowControls(false)
        }, 3000)
      }

      const handleMouseMove = () => resetTimeout()
      const handleKeyPress = () => resetTimeout()

      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('keydown', handleKeyPress)
      resetTimeout()

      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('keydown', handleKeyPress)
        if (controlsTimeoutRef.current) {
          clearTimeout(controlsTimeoutRef.current)
        }
      }
    }
  }, [isFullscreen])

  const loadAlbum = async () => {
    try {
      setLoading(true)
      const albumData = AlbumManager.getAlbum(albumId)
      
      if (!albumData) {
        router.push('/404')
        return
      }

      setAlbum(albumData)

      // 自动播放背景音乐
      if (albumData.backgroundMusicId) {
        const music = BackgroundMusicManager.getMusicById(albumData.backgroundMusicId)
        if (music) {
          try {
            await AudioPlayerManager.playMusic(music, volume)
            setIsPlaying(true)
          } catch (error) {
            console.error('Failed to play background music:', error)
          }
        }
      }

    } catch (error) {
      console.error('Failed to load album:', error)
      router.push('/404')
    } finally {
      setLoading(false)
    }
  }

  const togglePlayback = async () => {
    if (!album?.backgroundMusicId) return

    try {
      if (isPlaying) {
        AudioPlayerManager.pauseMusic()
        setIsPlaying(false)
      } else {
        const music = BackgroundMusicManager.getMusicById(album.backgroundMusicId)
        if (music) {
          await AudioPlayerManager.playMusic(music, volume)
          setIsPlaying(true)
        }
      }
    } catch (error) {
      console.error('Music playback error:', error)
    }
  }

  const toggleAutoPlay = () => {
    setIsAutoPlay(!isAutoPlay)
  }

  const nextPhoto = () => {
    if (!album) return
    setCurrentPhotoIndex((prev) => (prev + 1) % album.photos.length)
  }

  const prevPhoto = () => {
    if (!album) return
    setCurrentPhotoIndex((prev) => (prev - 1 + album.photos.length) % album.photos.length)
  }

  const toggleFullscreen = () => {
    if (!isFullscreen) {
      document.documentElement.requestFullscreen?.()
    } else {
      document.exitFullscreen?.()
    }
    setIsFullscreen(!isFullscreen)
  }

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume)
    AudioPlayerManager.setVolume(newVolume)
    setIsMuted(newVolume === 0)
  }

  const toggleMute = () => {
    if (isMuted) {
      handleVolumeChange(0.5)
    } else {
      handleVolumeChange(0)
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: album?.title,
          text: album?.description,
          url: window.location.href
        })
      } catch (error) {
        console.error('Share failed:', error)
      }
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href)
      alert('链接已复制到剪贴板')
    }
  }

  const handleDownload = () => {
    // TODO: 实现相册下载功能
    alert('下载功能正在开发中...')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
      </div>
    )
  }

  if (!album) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center text-white">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">相册未找到</h1>
          <button
            onClick={() => router.back()}
            className="text-blue-400 hover:text-blue-300"
          >
            返回上一页
          </button>
        </div>
      </div>
    )
  }

  const template = getTemplateById(album.templateId || 'classic')
  const currentPhoto = album.photos[currentPhotoIndex]

  return (
    <div className={`min-h-screen ${isFullscreen ? 'bg-black' : 'bg-gray-900'} relative`}>
      {/* Template CSS */}
      <style dangerouslySetInnerHTML={{
        __html: `
          ${template ? generateTemplateCSS(template) : ''}
          .preview-container {
            background: ${template?.colors.background || '#ffffff'};
            color: ${template?.colors.text || '#000000'};
          }
        `
      }} />

      {/* Header - 只在非全屏模式显示 */}
      {!isFullscreen && (
        <header className="bg-black bg-opacity-50 text-white sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.back()}
                  className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                >
                  <ArrowLeft className="w-5 h-5" />
                </button>
                <div>
                  <h1 className="text-xl font-semibold">{album.title}</h1>
                  <p className="text-sm text-gray-300">
                    {currentPhotoIndex + 1} / {album.photos.length}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleShare}
                  className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                >
                  <Share2 className="w-5 h-5" />
                </button>
                <button
                  onClick={handleDownload}
                  className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                >
                  <Download className="w-5 h-5" />
                </button>
                <button
                  onClick={() => router.push(`/album/edit/${albumId}`)}
                  className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                >
                  <Settings className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </header>
      )}

      {/* Main Content */}
      <div className="relative h-screen flex items-center justify-center">
        {album.photos.length > 0 ? (
          <>
            {/* Current Photo */}
            <div className="relative w-full h-full flex items-center justify-center">
              <img
                src={currentPhoto.url}
                alt={currentPhoto.caption || '相册照片'}
                className="max-w-full max-h-full object-contain"
              />
              
              {/* Photo Caption */}
              {currentPhoto.caption && (
                <div className="absolute bottom-4 left-4 right-4 bg-black bg-opacity-50 text-white p-4 rounded-lg">
                  <p className="text-center">{currentPhoto.caption}</p>
                </div>
              )}
            </div>

            {/* Navigation Arrows */}
            <button
              onClick={prevPhoto}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 p-3 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all"
            >
              <SkipBack className="w-6 h-6" />
            </button>
            <button
              onClick={nextPhoto}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 p-3 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all"
            >
              <SkipForward className="w-6 h-6" />
            </button>
          </>
        ) : (
          <div className="text-center text-white">
            <h2 className="text-2xl font-bold mb-2">相册为空</h2>
            <p className="text-gray-300">这个相册还没有添加照片</p>
          </div>
        )}
      </div>

      {/* Controls */}
      <div className={`absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-4 transition-all duration-300 ${
        isFullscreen && !showControls ? 'opacity-0 pointer-events-none' : 'opacity-100'
      }`}>
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between">
            {/* Left Controls */}
            <div className="flex items-center space-x-4">
              {album.backgroundMusicId && (
                <button
                  onClick={togglePlayback}
                  className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                >
                  {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
                </button>
              )}
              
              <button
                onClick={toggleAutoPlay}
                className={`px-3 py-1 rounded text-sm transition-colors ${
                  isAutoPlay ? 'bg-blue-600 text-white' : 'bg-white bg-opacity-20 text-white'
                }`}
              >
                自动播放
              </button>
            </div>

            {/* Center - Photo Progress */}
            <div className="flex-1 mx-8">
              <div className="flex items-center space-x-2">
                <span className="text-sm">{currentPhotoIndex + 1}</span>
                <div className="flex-1 bg-white bg-opacity-20 rounded-full h-2">
                  <div
                    className="bg-white h-2 rounded-full transition-all duration-300"
                    style={{ width: `${((currentPhotoIndex + 1) / album.photos.length) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm">{album.photos.length}</span>
              </div>
            </div>

            {/* Right Controls */}
            <div className="flex items-center space-x-4">
              {/* Volume Control */}
              {album.backgroundMusicId && (
                <div className="flex items-center space-x-2">
                  <button onClick={toggleMute} className="p-1">
                    {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                  </button>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={volume}
                    onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                    className="w-20"
                  />
                </div>
              )}

              <button
                onClick={toggleFullscreen}
                className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
              >
                {isFullscreen ? <Minimize className="w-5 h-5" /> : <Maximize className="w-5 h-5" />}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Thumbnail Strip - 只在非全屏模式显示 */}
      {!isFullscreen && album.photos.length > 1 && (
        <div className="absolute bottom-20 left-0 right-0 p-4">
          <div className="flex justify-center">
            <div className="flex space-x-2 bg-black bg-opacity-50 p-2 rounded-lg max-w-full overflow-x-auto">
              {album.photos.map((photo, index) => (
                <button
                  key={photo.id}
                  onClick={() => setCurrentPhotoIndex(index)}
                  className={`flex-shrink-0 w-16 h-16 rounded overflow-hidden border-2 transition-all ${
                    index === currentPhotoIndex ? 'border-white' : 'border-transparent opacity-70'
                  }`}
                >
                  <img
                    src={photo.url}
                    alt={`缩略图 ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
