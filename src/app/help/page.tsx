'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import {
  ArrowLeft,
  ChevronDown,
  ChevronRight,
  BookOpen,
  Camera,
  Mic,
  Settings,
  HelpCircle,
  Phone
} from 'lucide-react'

interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
}

interface HelpSection {
  id: string
  title: string
  icon: React.ReactNode
  description: string
  items: string[]
}

export default function HelpPage() {
  const router = useRouter()
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null)

  const helpSections: HelpSection[] = [
    {
      id: 'getting-started',
      title: '快速开始',
      icon: <BookOpen className="w-6 h-6" />,
      description: '了解如何开始使用我的回忆录',
      items: [
        '选择合适的回忆录模板',
        '使用语音录制功能',
        '查看和编辑录制内容',
        '保存和分享您的回忆录'
      ]
    },
    {
      id: 'voice-recording',
      title: '语音录制',
      icon: <Mic className="w-6 h-6" />,
      description: '掌握语音录制的技巧',
      items: [
        '确保环境安静',
        '保持适当的录制距离',
        '说话清晰、语速适中',
        '可以随时暂停和继续录制'
      ]
    },
    {
      id: 'photo-albums',
      title: '相册功能',
      icon: <Camera className="w-6 h-6" />,
      description: '创建和管理您的照片相册',
      items: [
        '上传和整理照片',
        '为照片添加语音解说',
        '创建主题相册',
        '分享相册给家人朋友'
      ]
    },
    {
      id: 'settings',
      title: '设置和个性化',
      icon: <Settings className="w-6 h-6" />,
      description: '自定义您的使用体验',
      items: [
        '调整字体大小',
        '设置语音播放速度',
        '管理通知偏好',
        '导出和备份数据'
      ]
    }
  ]

  const faqItems: FAQItem[] = [
    {
      id: 'faq-1',
      question: '如何开始创建我的第一个回忆录？',
      answer: '点击首页的"创建新回忆录"按钮，选择一个适合的模板，然后按照提示开始录制您的故事。您可以随时暂停、编辑和继续录制。',
      category: 'getting-started'
    },
    {
      id: 'faq-2',
      question: '语音录制时有什么技巧吗？',
      answer: '建议在安静的环境中录制，保持手机距离嘴部15-20厘米，说话清晰、语速适中。如果录制有误，可以重新录制该段内容。',
      category: 'voice-recording'
    },
    {
      id: 'faq-3',
      question: '我可以编辑已经录制的内容吗？',
      answer: '是的，您可以在"原始记录"标签页中查看所有录制内容，点击任意条目进行编辑、删除或重新录制。',
      category: 'editing'
    },
    {
      id: 'faq-4',
      question: '如何分享我的回忆录？',
      answer: '完成回忆录后，您可以在"整理预览"页面选择导出为PDF或音频文件，然后通过微信、邮件等方式分享给家人朋友。',
      category: 'sharing'
    },
    {
      id: 'faq-5',
      question: '访客模式有什么限制？',
      answer: '访客模式可以体验所有功能，但数据仅保存在本地。建议注册正式账户以获得云端同步和更好的数据安全保障。',
      category: 'account'
    },
    {
      id: 'faq-6',
      question: '我的数据安全吗？',
      answer: '我们非常重视您的隐私和数据安全。所有数据都经过加密处理，仅您本人可以访问。我们不会将您的个人内容用于其他用途。',
      category: 'privacy'
    }
  ]

  const toggleFAQ = (id: string) => {
    setExpandedFAQ(expandedFAQ === id ? null : id)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              <h1 className="text-xl font-semibold text-gray-900">帮助中心</h1>
            </div>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="max-w-4xl mx-auto px-4 py-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl text-white p-8 mb-8">
          <div className="text-center">
            <HelpCircle className="w-16 h-16 mx-auto mb-4 opacity-90" />
            <h2 className="text-2xl font-bold mb-2">需要帮助？</h2>
            <p className="text-blue-100 mb-6">
              我们为您准备了详细的使用指南和常见问题解答
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="tel:15810088428"
                className="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
              >
                <Phone className="w-5 h-5" />
                <span>电话咨询：15810088428</span>
              </a>
            </div>
          </div>
        </div>

        {/* Help Sections */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {helpSections.map((section) => (
            <div key={section.id} className="bg-white rounded-xl shadow-sm p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  {section.icon}
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
                  <p className="text-sm text-gray-600">{section.description}</p>
                </div>
              </div>
              <ul className="space-y-2">
                {section.items.map((item, index) => (
                  <li key={index} className="flex items-center space-x-2 text-sm text-gray-700">
                    <ChevronRight className="w-4 h-4 text-blue-600 flex-shrink-0" />
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* FAQ Section */}
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900">常见问题</h3>
            <p className="text-sm text-gray-600 mt-1">点击问题查看详细解答</p>
          </div>
          <div className="divide-y divide-gray-100">
            {faqItems.map((item) => (
              <div key={item.id} className="px-6 py-4">
                <button
                  onClick={() => toggleFAQ(item.id)}
                  className="w-full flex items-center justify-between text-left"
                >
                  <span className="font-medium text-gray-900 pr-4">{item.question}</span>
                  <ChevronDown
                    className={`w-5 h-5 text-gray-500 transition-transform ${
                      expandedFAQ === item.id ? 'rotate-180' : ''
                    }`}
                  />
                </button>
                {expandedFAQ === item.id && (
                  <div className="mt-3 text-gray-700 leading-relaxed">
                    {item.answer}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Contact Section */}
        <div className="mt-8 bg-amber-50 rounded-xl p-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">还有其他问题？</h3>
            <p className="text-gray-600 mb-4">
              我们的客服团队随时为您提供帮助
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="tel:15810088428"
                className="bg-amber-600 hover:bg-amber-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
              >
                <Phone className="w-5 h-5" />
                <span>联系客服</span>
              </a>
            </div>
            <p className="text-sm text-gray-500 mt-4">
              服务时间：周一至周日 9:00-18:00
            </p>
          </div>
        </div>

        {/* Company Info */}
        <div className="mt-6 text-center text-sm text-gray-500">
          <p>北京暮忆留声文化有限公司</p>
          <p>致力于为您提供最好的回忆录创作体验</p>
        </div>
      </main>
    </div>
  )
}
