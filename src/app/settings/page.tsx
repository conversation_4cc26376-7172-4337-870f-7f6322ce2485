'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import {
  ArrowLeft,
  User,
  Bell,
  Palette,
  Volume2,
  Download,
  Trash2,
  HelpCircle,
  LogOut,
  ChevronRight
} from 'lucide-react'
import { GuestAuthService, type GuestUser } from '@/lib/guest-auth'

interface SettingsSection {
  id: string
  title: string
  icon: React.ReactNode
  items: SettingsItem[]
}

interface SettingsItem {
  id: string
  title: string
  description?: string
  type: 'toggle' | 'select' | 'action' | 'info'
  value?: string | boolean | number
  options?: { label: string; value: string | boolean | number }[]
  action?: () => void
}

export default function SettingsPage() {
  const router = useRouter()
  const [user, setUser] = useState<GuestUser | null>(null)
  const [isGuest, setIsGuest] = useState(false)
  const [settings, setSettings] = useState({
    theme: 'light',
    fontSize: 'medium',
    voiceSpeed: 'normal',
    notifications: true,
    autoSave: true,
    language: 'zh-CN'
  })

  useEffect(() => {
    // 检查用户状态
    const guestUser = GuestAuthService.getCurrentGuestUser()

    if (guestUser) {
      setUser(guestUser)
      setIsGuest(true)
    } else {
      // 检查是否有其他类型的用户登录
      // 这里可以添加其他认证方式的检查
      router.push('/')
      return
    }

    // 加载设置
    const savedSettings = localStorage.getItem('app_settings')
    if (savedSettings) {
      setSettings({ ...settings, ...JSON.parse(savedSettings) })
    }
  }, [])

  const updateSetting = (key: string, value: string | boolean | number) => {
    const newSettings = { ...settings, [key]: value }
    setSettings(newSettings)
    localStorage.setItem('app_settings', JSON.stringify(newSettings))
  }

  const handleLogout = () => {
    if (window.confirm('确定要退出登录吗？')) {
      if (isGuest) {
        GuestAuthService.clearGuestSession()
      }
      window.location.href = '/'
    }
  }

  const handleExportData = () => {
    // TODO: 实现数据导出功能
    alert('数据导出功能正在开发中...')
  }

  const handleDeleteAccount = () => {
    if (window.confirm('确定要删除账户吗？此操作无法撤销，所有数据将被永久删除。')) {
      // TODO: 实现账户删除功能
      alert('账户删除功能正在开发中...')
    }
  }

  const settingsSections: SettingsSection[] = [
    {
      id: 'account',
      title: '账户设置',
      icon: <User className="w-5 h-5" />,
      items: [
        {
          id: 'profile',
          title: '个人资料',
          description: '编辑您的个人信息',
          type: 'action',
          action: () => router.push('/profile')
        },
        {
          id: 'account_type',
          title: '账户类型',
          description: isGuest ? '访客账户' : '正式账户',
          type: 'info'
        }
      ]
    },
    {
      id: 'appearance',
      title: '外观设置',
      icon: <Palette className="w-5 h-5" />,
      items: [
        {
          id: 'theme',
          title: '主题模式',
          description: '选择浅色或深色主题',
          type: 'select',
          value: settings.theme,
          options: [
            { label: '浅色', value: 'light' },
            { label: '深色', value: 'dark' },
            { label: '跟随系统', value: 'system' }
          ]
        },
        {
          id: 'fontSize',
          title: '字体大小',
          description: '调整界面字体大小',
          type: 'select',
          value: settings.fontSize,
          options: [
            { label: '小', value: 'small' },
            { label: '中等', value: 'medium' },
            { label: '大', value: 'large' },
            { label: '特大', value: 'extra-large' }
          ]
        }
      ]
    },
    {
      id: 'voice',
      title: '语音设置',
      icon: <Volume2 className="w-5 h-5" />,
      items: [
        {
          id: 'voiceSpeed',
          title: '语音播放速度',
          description: '调整语音播放的速度',
          type: 'select',
          value: settings.voiceSpeed,
          options: [
            { label: '慢速', value: 'slow' },
            { label: '正常', value: 'normal' },
            { label: '快速', value: 'fast' }
          ]
        }
      ]
    },
    {
      id: 'notifications',
      title: '通知设置',
      icon: <Bell className="w-5 h-5" />,
      items: [
        {
          id: 'notifications',
          title: '推送通知',
          description: '接收应用通知',
          type: 'toggle',
          value: settings.notifications
        },
        {
          id: 'autoSave',
          title: '自动保存',
          description: '自动保存您的工作',
          type: 'toggle',
          value: settings.autoSave
        }
      ]
    },
    {
      id: 'data',
      title: '数据管理',
      icon: <Download className="w-5 h-5" />,
      items: [
        {
          id: 'export',
          title: '导出数据',
          description: '导出您的所有数据',
          type: 'action',
          action: handleExportData
        },
        {
          id: 'storage',
          title: '存储空间',
          description: '查看存储使用情况',
          type: 'action',
          action: () => router.push('/storage')
        }
      ]
    },
    {
      id: 'support',
      title: '帮助与支持',
      icon: <HelpCircle className="w-5 h-5" />,
      items: [
        {
          id: 'help',
          title: '使用帮助',
          description: '查看使用指南',
          type: 'action',
          action: () => router.push('/help')
        },
        {
          id: 'contact',
          title: '联系我们',
          description: '获取技术支持',
          type: 'action',
          action: () => window.open('tel:15810088428')
        }
      ]
    }
  ]

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              <h1 className="text-xl font-semibold text-gray-900">设置</h1>
            </div>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="max-w-4xl mx-auto px-4 py-6">
        {/* User Info */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
              <User className="w-8 h-8 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                {isGuest ? GuestAuthService.getGuestDisplayName(user!) : '用户'}
              </h2>
              <p className="text-sm text-gray-600">
                {isGuest ? '访客账户' : '正式用户'}
              </p>
            </div>
          </div>
        </div>

        {/* Settings Sections */}
        <div className="space-y-6">
          {settingsSections.map((section) => (
            <div key={section.id} className="bg-white rounded-xl shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-100">
                <div className="flex items-center space-x-3">
                  {section.icon}
                  <h3 className="text-lg font-medium text-gray-900">{section.title}</h3>
                </div>
              </div>
              <div className="divide-y divide-gray-100">
                {section.items.map((item) => (
                  <SettingsItem
                    key={item.id}
                    item={item}
                    onUpdate={(value) => updateSetting(item.id, value)}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Logout Button */}
        <div className="mt-8">
          <button
            onClick={handleLogout}
            className="w-full bg-red-50 hover:bg-red-100 text-red-600 p-4 rounded-xl font-medium transition-colors flex items-center justify-center space-x-2"
          >
            <LogOut className="w-5 h-5" />
            <span>退出登录</span>
          </button>
        </div>

        {/* Danger Zone */}
        {!isGuest && (
          <div className="mt-6 bg-red-50 rounded-xl p-6">
            <h3 className="text-lg font-medium text-red-900 mb-4">危险操作</h3>
            <button
              onClick={handleDeleteAccount}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
            >
              <Trash2 className="w-4 h-4" />
              <span>删除账户</span>
            </button>
          </div>
        )}
      </main>
    </div>
  )
}

// Settings Item Component
function SettingsItem({
  item,
  onUpdate
}: {
  readonly item: SettingsItem
  readonly onUpdate: (value: string | boolean | number) => void
}) {
  const renderControl = () => {
    switch (item.type) {
      case 'toggle':
        return (
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={item.value}
              onChange={(e) => onUpdate(e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        )
      
      case 'select':
        return (
          <select
            value={item.value}
            onChange={(e) => onUpdate(e.target.value)}
            className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2"
          >
            {item.options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        )
      
      case 'action':
        return (
          <button
            onClick={item.action}
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            <ChevronRight className="w-5 h-5" />
          </button>
        )
      
      case 'info':
        return (
          <span className="text-sm text-gray-500">{item.description}</span>
        )
      
      default:
        return null
    }
  }

  return (
    <div className="px-6 py-4 flex items-center justify-between">
      <div className="flex-1">
        <h4 className="text-sm font-medium text-gray-900">{item.title}</h4>
        {item.description && item.type !== 'info' && (
          <p className="text-sm text-gray-500 mt-1">{item.description}</p>
        )}
      </div>
      <div className="ml-4">
        {renderControl()}
      </div>
    </div>
  )
}
