'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { User } from '@supabase/supabase-js'
import AuthComponent from '@/components/auth/AuthComponent'
import WelcomeScreen from '@/components/WelcomeScreen'
import TemplateSelection from '@/components/TemplateSelection'

export default function Home() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [showTemplates, setShowTemplates] = useState(false)
  const [isGuestMode, setIsGuestMode] = useState(false)

  useEffect(() => {
    // 检查访客模式
    const checkGuestMode = () => {
      if (typeof window !== 'undefined') {
        const guestMode = localStorage.getItem('guest_mode')
        const guestUserId = localStorage.getItem('guest_user_id')

        if (guestMode === 'true' && guestUserId) {
          setIsGuestMode(true)
          // 创建一个模拟的访客用户对象
          const mockGuestUser: User = {
            id: guestUserId,
            aud: 'authenticated',
            role: 'authenticated',
            email: `${guestUserId}@guest.local`,
            email_confirmed_at: new Date().toISOString(),
            phone: '',
            confirmed_at: new Date().toISOString(),
            last_sign_in_at: new Date().toISOString(),
            app_metadata: {},
            user_metadata: {
              auth_method: 'guest',
              is_guest: true,
              full_name: '访客用户'
            },
            identities: [],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
          setUser(mockGuestUser)
          setLoading(false)
          return
        }
      }

      // 正常的认证流程
      supabase.auth.getSession().then(({ data: { session } }) => {
        setUser(session?.user ?? null)
        setLoading(false)
      })
    }

    checkGuestMode()

    // Listen for auth changes (只在非访客模式下)
    if (!isGuestMode) {
      const {
        data: { subscription },
      } = supabase.auth.onAuthStateChange((_event, session) => {
        setUser(session?.user ?? null)
        setLoading(false)
      })

      return () => subscription.unsubscribe()
    }
  }, [isGuestMode])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg">加载中...</p>
        </div>
      </div>
    )
  }

  if (!user && !isGuestMode) {
    return <AuthComponent />
  }

  if (showTemplates) {
    return <TemplateSelection onBack={() => setShowTemplates(false)} />
  }

  return (
    <WelcomeScreen
      user={user!}
      onCreateMemoir={() => setShowTemplates(true)}
    />
  )
}
