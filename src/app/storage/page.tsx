'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import {
  ArrowLeft,
  HardDrive,
  FileText,
  Image,
  Mic,
  Trash2,
  Download,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { ProjectManager } from '@/lib/project-manager'
import { AlbumManager } from '@/lib/album-manager'

interface StorageInfo {
  totalSize: number
  usedSize: number
  availableSize: number
  items: StorageItem[]
}

interface StorageItem {
  id: string
  name: string
  type: 'memoir' | 'album' | 'audio' | 'image'
  size: number
  lastModified: Date
  canDelete: boolean
}

export default function StoragePage() {
  const router = useRouter()
  const [storageInfo, setStorageInfo] = useState<StorageInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedItems, setSelectedItems] = useState<string[]>([])

  useEffect(() => {
    loadStorageInfo()
  }, [])

  const loadStorageInfo = () => {
    setLoading(true)
    
    try {
      // 模拟存储信息计算
      const items: StorageItem[] = []
      
      // 获取回忆录项目
      const projects = ProjectManager.getProjectSummaries()
      projects.forEach(project => {
        items.push({
          id: project.id,
          name: project.title,
          type: 'memoir',
          size: Math.random() * 10 * 1024 * 1024, // 随机大小，实际应该计算真实大小
          lastModified: project.lastModified,
          canDelete: true
        })
      })

      // 获取相册
      const albums = AlbumManager.getAlbumSummaries()
      albums.forEach(album => {
        items.push({
          id: album.id,
          name: album.title,
          type: 'album',
          size: album.totalPhotos * 2 * 1024 * 1024, // 估算每张照片2MB
          lastModified: album.lastModified,
          canDelete: true
        })
      })

      // 计算总大小
      const usedSize = items.reduce((total, item) => total + item.size, 0)
      const totalSize = 100 * 1024 * 1024 * 1024 // 100GB 总空间
      const availableSize = totalSize - usedSize

      setStorageInfo({
        totalSize,
        usedSize,
        availableSize,
        items: items.sort((a, b) => b.size - a.size)
      })
    } catch (error) {
      console.error('Failed to load storage info:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getUsagePercentage = (): number => {
    if (!storageInfo) return 0
    return (storageInfo.usedSize / storageInfo.totalSize) * 100
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'memoir':
        return <FileText className="w-5 h-5 text-blue-600" />
      case 'album':
        return <Image className="w-5 h-5 text-green-600" />
      case 'audio':
        return <Mic className="w-5 h-5 text-purple-600" />
      default:
        return <HardDrive className="w-5 h-5 text-gray-600" />
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'memoir':
        return '回忆录'
      case 'album':
        return '相册'
      case 'audio':
        return '音频'
      case 'image':
        return '图片'
      default:
        return '其他'
    }
  }

  const handleSelectItem = (itemId: string) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  const handleDeleteSelected = () => {
    if (selectedItems.length === 0) return
    
    if (window.confirm(`确定要删除选中的 ${selectedItems.length} 个项目吗？此操作无法撤销。`)) {
      selectedItems.forEach(itemId => {
        const item = storageInfo?.items.find(i => i.id === itemId)
        if (item) {
          if (item.type === 'memoir') {
            ProjectManager.deleteProject(itemId)
          } else if (item.type === 'album') {
            AlbumManager.deleteAlbum(itemId)
          }
        }
      })
      
      setSelectedItems([])
      loadStorageInfo()
    }
  }

  const handleCleanup = () => {
    if (window.confirm('确定要清理临时文件和缓存吗？这将释放一些存储空间。')) {
      // 清理localStorage中的临时数据
      const keysToRemove = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && (key.startsWith('temp_') || key.startsWith('cache_'))) {
          keysToRemove.push(key)
        }
      }
      
      keysToRemove.forEach(key => localStorage.removeItem(key))
      
      alert('清理完成！已释放临时文件占用的空间。')
      loadStorageInfo()
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!storageInfo) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <p className="text-gray-600">无法加载存储信息</p>
        </div>
      </div>
    )
  }

  const usagePercentage = getUsagePercentage()

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              <h1 className="text-xl font-semibold text-gray-900">存储管理</h1>
            </div>
            {selectedItems.length > 0 && (
              <button
                onClick={handleDeleteSelected}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
              >
                <Trash2 className="w-4 h-4" />
                <span>删除选中 ({selectedItems.length})</span>
              </button>
            )}
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="max-w-4xl mx-auto px-4 py-6">
        {/* Storage Overview */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
          <div className="flex items-center space-x-3 mb-4">
            <HardDrive className="w-6 h-6 text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900">存储概览</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{formatSize(storageInfo.usedSize)}</p>
              <p className="text-sm text-gray-600">已使用</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{formatSize(storageInfo.availableSize)}</p>
              <p className="text-sm text-gray-600">可用空间</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{formatSize(storageInfo.totalSize)}</p>
              <p className="text-sm text-gray-600">总空间</p>
            </div>
          </div>

          {/* Usage Bar */}
          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>存储使用率</span>
              <span>{usagePercentage.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className={`h-3 rounded-full transition-all duration-300 ${
                  usagePercentage > 90 ? 'bg-red-500' :
                  usagePercentage > 70 ? 'bg-yellow-500' :
                  'bg-green-500'
                }`}
                style={{ width: `${Math.min(usagePercentage, 100)}%` }}
              ></div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-wrap gap-3">
            <button
              onClick={handleCleanup}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
            >
              <CheckCircle className="w-4 h-4" />
              <span>清理缓存</span>
            </button>
            <button
              onClick={() => alert('导出功能正在开发中...')}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>导出数据</span>
            </button>
          </div>
        </div>

        {/* Storage Items */}
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900">存储项目</h3>
            <p className="text-sm text-gray-600 mt-1">管理您的回忆录和相册文件</p>
          </div>
          
          {storageInfo.items.length === 0 ? (
            <div className="p-8 text-center">
              <HardDrive className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">暂无存储项目</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-100">
              {storageInfo.items.map((item) => (
                <div key={item.id} className="px-6 py-4 flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <input
                      type="checkbox"
                      checked={selectedItems.includes(item.id)}
                      onChange={() => handleSelectItem(item.id)}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <div className="flex items-center space-x-3">
                      {getTypeIcon(item.type)}
                      <div>
                        <h4 className="font-medium text-gray-900">{item.name}</h4>
                        <p className="text-sm text-gray-600">
                          {getTypeLabel(item.type)} • {formatSize(item.size)} • {item.lastModified.toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  {item.canDelete && (
                    <button
                      onClick={() => handleSelectItem(item.id)}
                      className="text-gray-400 hover:text-red-600 transition-colors"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
