import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // 检查环境变量是否配置
    const hasApiKey = !!process.env.GEMINI_API_KEY
    
    if (!hasApiKey) {
      return NextResponse.json(
        { 
          status: 'error', 
          message: 'AI服务未配置' 
        },
        { status: 503 }
      )
    }

    return NextResponse.json({
      status: 'ok',
      message: 'AI服务正常',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Health check error:', error)
    return NextResponse.json(
      { 
        status: 'error', 
        message: '健康检查失败' 
      },
      { status: 500 }
    )
  }
}
