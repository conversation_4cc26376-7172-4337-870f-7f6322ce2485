import { NextRequest, NextResponse } from 'next/server'
import { GeminiAI } from '@/lib/gemini-ai'
import { MockAI } from '@/lib/mock-ai'

export async function POST(request: NextRequest) {
  try {
    const { transcript, context } = await request.json()

    if (!transcript || typeof transcript !== 'string') {
      return NextResponse.json(
        { error: '请提供有效的语音转录内容' },
        { status: 400 }
      )
    }

    let processedContent: string

    // 尝试使用Gemini AI，如果失败则使用模拟AI
    try {
      const geminiAI = new GeminiAI(process.env.GEMINI_API_KEY)

      if (!geminiAI.isConfigured()) {
        throw new Error('Gemini API not configured')
      }

      processedContent = await geminiAI.processVoiceTranscript(transcript, context)
    } catch (geminiError) {
      console.warn('Gemini AI failed, falling back to mock AI:', geminiError)

      // 使用模拟AI作为后备
      const mockAI = new MockAI()
      processedContent = await mockAI.processVoiceTranscript(transcript, context)

      // 添加一个提示说明使用了模拟处理
      processedContent += '\n\n*注：当前使用演示模式处理内容*'
    }

    return NextResponse.json({
      success: true,
      processedContent
    })

  } catch (error) {
    console.error('AI processing error:', error)
    
    // 处理不同类型的错误
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { error: 'AI服务配置错误' },
          { status: 500 }
        )
      }
      
      if (error.message.includes('quota') || error.message.includes('limit')) {
        return NextResponse.json(
          { error: 'AI服务使用量已达上限，请稍后重试' },
          { status: 429 }
        )
      }
      
      if (error.message.includes('network') || error.message.includes('fetch')) {
        return NextResponse.json(
          { error: '网络连接错误，请检查网络后重试' },
          { status: 503 }
        )
      }
    }

    return NextResponse.json(
      { error: 'AI处理失败，请重试' },
      { status: 500 }
    )
  }
}

// 处理OPTIONS请求（CORS预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
