import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { DEFAULT_GUEST_LIMITS } from '@/lib/guest-auth'

export async function POST(request: NextRequest) {
  try {
    const { userId, action } = await request.json()

    if (!userId || !action) {
      return NextResponse.json(
        { error: '参数不完整' },
        { status: 400 }
      )
    }

    // 获取用户信息
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()

    if (profileError || !profile) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 检查是否为访客用户
    if (profile.auth_method !== 'guest') {
      return NextResponse.json({
        allowed: true,
        message: '正式用户无限制'
      })
    }

    // 检查访客账户是否过期
    const expiresAt = new Date(profile.guest_expires_at)
    const now = new Date()
    
    if (now > expiresAt) {
      return NextResponse.json({
        allowed: false,
        reason: '访客账户已过期，请注册正式账户'
      })
    }

    // 根据操作类型检查限制
    switch (action) {
      case 'create_memoir': {
        // 检查回忆录数量限制
        const { data: memoirs, error: memoirsError } = await supabase
          .from('memoirs')
          .select('id')
          .eq('user_id', userId)

        if (memoirsError) {
          return NextResponse.json(
            { error: '检查回忆录数量失败' },
            { status: 500 }
          )
        }

        const currentCount = memoirs?.length || 0
        const allowed = currentCount < DEFAULT_GUEST_LIMITS.maxMemoirs

        return NextResponse.json({
          allowed,
          reason: allowed ? undefined : `访客模式最多只能创建${DEFAULT_GUEST_LIMITS.maxMemoirs}个回忆录`,
          currentCount,
          maxCount: DEFAULT_GUEST_LIMITS.maxMemoirs,
          remainingDays: Math.ceil((expiresAt.getTime() - now.getTime()) / (24 * 60 * 60 * 1000))
        })
      }

      case 'create_chapter': {
        const { memoirId } = await request.json()
        
        if (!memoirId) {
          return NextResponse.json(
            { error: '回忆录ID不能为空' },
            { status: 400 }
          )
        }

        // 检查章节数量限制
        const { data: chapters, error: chaptersError } = await supabase
          .from('chapters')
          .select('id')
          .eq('memoir_id', memoirId)

        if (chaptersError) {
          return NextResponse.json(
            { error: '检查章节数量失败' },
            { status: 500 }
          )
        }

        const currentCount = chapters?.length || 0
        const allowed = currentCount < DEFAULT_GUEST_LIMITS.maxChaptersPerMemoir

        return NextResponse.json({
          allowed,
          reason: allowed ? undefined : `访客模式每个回忆录最多只能创建${DEFAULT_GUEST_LIMITS.maxChaptersPerMemoir}个章节`,
          currentCount,
          maxCount: DEFAULT_GUEST_LIMITS.maxChaptersPerMemoir
        })
      }

      case 'create_content_block': {
        const { chapterId } = await request.json()
        
        if (!chapterId) {
          return NextResponse.json(
            { error: '章节ID不能为空' },
            { status: 400 }
          )
        }

        // 检查内容块数量限制
        const { data: contentBlocks, error: contentError } = await supabase
          .from('content_blocks')
          .select('id')
          .eq('chapter_id', chapterId)

        if (contentError) {
          return NextResponse.json(
            { error: '检查内容块数量失败' },
            { status: 500 }
          )
        }

        const currentCount = contentBlocks?.length || 0
        const allowed = currentCount < DEFAULT_GUEST_LIMITS.maxContentBlocksPerChapter

        return NextResponse.json({
          allowed,
          reason: allowed ? undefined : `访客模式每个章节最多只能创建${DEFAULT_GUEST_LIMITS.maxContentBlocksPerChapter}个内容块`,
          currentCount,
          maxCount: DEFAULT_GUEST_LIMITS.maxContentBlocksPerChapter
        })
      }

      default:
        return NextResponse.json({
          allowed: true,
          message: '未知操作，默认允许'
        })
    }

  } catch (error) {
    console.error('检查访客限制失败:', error)
    return NextResponse.json(
      { error: '检查权限失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 处理OPTIONS请求（CORS预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
