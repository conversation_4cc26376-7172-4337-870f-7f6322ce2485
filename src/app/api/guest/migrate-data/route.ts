import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const { guestUserId, newUserId } = await request.json()

    if (!guestUserId || !newUserId) {
      return NextResponse.json(
        { error: '用户ID不能为空' },
        { status: 400 }
      )
    }

    // 验证访客用户存在
    const { data: guestProfile, error: guestError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', guestUserId)
      .eq('auth_method', 'guest')
      .single()

    if (guestError || !guestProfile) {
      return NextResponse.json(
        { error: '访客用户不存在' },
        { status: 404 }
      )
    }

    // 验证新用户存在
    const { data: newProfile, error: newError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', newUserId)
      .single()

    if (newError || !newProfile) {
      return NextResponse.json(
        { error: '新用户不存在' },
        { status: 404 }
      )
    }

    const migratedItems = {
      memoirs: 0,
      chapters: 0,
      contentBlocks: 0,
      photoAlbums: 0
    }

    // 开始数据迁移事务
    try {
      // 1. 迁移回忆录
      const { data: memoirs, error: memoirsError } = await supabase
        .from('memoirs')
        .select('*')
        .eq('user_id', guestUserId)

      if (memoirsError) {
        throw new Error('获取访客回忆录失败')
      }

      if (memoirs && memoirs.length > 0) {
        const { error: updateMemoirsError } = await supabase
          .from('memoirs')
          .update({ user_id: newUserId })
          .eq('user_id', guestUserId)

        if (updateMemoirsError) {
          throw new Error('迁移回忆录失败')
        }

        migratedItems.memoirs = memoirs.length

        // 2. 迁移章节（通过回忆录关联）
        for (const memoir of memoirs) {
          const { data: chapters, error: chaptersError } = await supabase
            .from('chapters')
            .select('*')
            .eq('memoir_id', memoir.id)

          if (chaptersError) {
            console.warn(`获取回忆录 ${memoir.id} 的章节失败:`, chaptersError)
            continue
          }

          if (chapters && chapters.length > 0) {
            migratedItems.chapters += chapters.length

            // 3. 迁移内容块（通过章节关联）
            for (const chapter of chapters) {
              const { data: contentBlocks, error: contentError } = await supabase
                .from('content_blocks')
                .select('*')
                .eq('chapter_id', chapter.id)

              if (contentError) {
                console.warn(`获取章节 ${chapter.id} 的内容块失败:`, contentError)
                continue
              }

              if (contentBlocks && contentBlocks.length > 0) {
                migratedItems.contentBlocks += contentBlocks.length
              }
            }
          }
        }
      }

      // 4. 迁移相册
      const { data: photoAlbums, error: albumsError } = await supabase
        .from('photo_albums')
        .select('*')
        .eq('user_id', guestUserId)

      if (albumsError) {
        throw new Error('获取访客相册失败')
      }

      if (photoAlbums && photoAlbums.length > 0) {
        const { error: updateAlbumsError } = await supabase
          .from('photo_albums')
          .update({ user_id: newUserId })
          .eq('user_id', guestUserId)

        if (updateAlbumsError) {
          throw new Error('迁移相册失败')
        }

        migratedItems.photoAlbums = photoAlbums.length
      }

      // 5. 删除访客用户profile（保留auth.users记录以防回滚）
      const { error: deleteProfileError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', guestUserId)

      if (deleteProfileError) {
        console.warn('删除访客profile失败:', deleteProfileError)
        // 不抛出错误，因为数据已经迁移成功
      }

      // 6. 删除访客用户的auth记录
      const { error: deleteAuthError } = await supabase.auth.admin.deleteUser(guestUserId)

      if (deleteAuthError) {
        console.warn('删除访客auth用户失败:', deleteAuthError)
        // 不抛出错误，因为数据已经迁移成功
      }

      return NextResponse.json({
        success: true,
        message: '数据迁移成功',
        migratedItems
      })

    } catch (migrationError) {
      console.error('数据迁移过程中出错:', migrationError)
      return NextResponse.json(
        { 
          error: migrationError instanceof Error ? migrationError.message : '数据迁移失败',
          migratedItems // 返回已迁移的部分数据统计
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('数据迁移失败:', error)
    return NextResponse.json(
      { error: '数据迁移失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 处理OPTIONS请求（CORS预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
