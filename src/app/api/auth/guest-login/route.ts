import { NextRequest, NextResponse } from 'next/server'
import { DEFAULT_GUEST_LIMITS } from '@/lib/guest-auth'

export async function POST(request: NextRequest) {
  try {
    const { deviceFingerprint } = await request.json()

    if (!deviceFingerprint) {
      return NextResponse.json(
        { error: '设备标识不能为空' },
        { status: 400 }
      )
    }

    // 为了演示，我们直接返回成功
    // 在实际应用中，这里应该创建真正的访客用户
    const mockUserId = `guest_${deviceFingerprint}`

    return NextResponse.json({
      success: true,
      message: '访客账户创建成功',
      isNewGuest: true,
      userId: mockUserId,
      sessionUrl: `${process.env.NEXT_PUBLIC_APP_URL}?guest=${deviceFingerprint}`,
      limits: DEFAULT_GUEST_LIMITS
    })

  } catch (error) {
    console.error('访客登录失败:', error)
    return NextResponse.json(
      { error: '访客登录失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 处理OPTIONS请求（CORS预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
