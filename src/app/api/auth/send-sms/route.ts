import { NextRequest, NextResponse } from 'next/server'
import { createSMSService } from '@/lib/sms-service'
import { validateChinesePhoneNumber } from '@/lib/phone-validation'

export async function POST(request: NextRequest) {
  try {
    const { phone } = await request.json()

    // 验证手机号格式
    if (!phone || !validateChinesePhoneNumber(phone)) {
      return NextResponse.json(
        { error: '请输入有效的中国大陆手机号' },
        { status: 400 }
      )
    }

    // 简单的频率限制（实际应用中应该使用Redis等）
    // const rateLimitKey = `sms_${phone}`
    // 这里应该检查发送频率，防止恶意刷短信

    const smsService = createSMSService()
    const result = await smsService.sendVerificationCode(phone)

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message
      })
    } else {
      return NextResponse.json(
        { error: result.message },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('发送短信验证码失败:', error)
    return NextResponse.json(
      { error: '发送验证码失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 处理OPTIONS请求（CORS预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
