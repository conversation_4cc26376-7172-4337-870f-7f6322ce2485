import { NextRequest, NextResponse } from 'next/server'
import { createSMSService } from '@/lib/sms-service'
import { validateChinesePhoneNumber } from '@/lib/phone-validation'
import { supabase } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const { phone, code } = await request.json()

    // 验证输入
    if (!phone || !validateChinesePhoneNumber(phone)) {
      return NextResponse.json(
        { error: '请输入有效的中国大陆手机号' },
        { status: 400 }
      )
    }

    if (!code || code.length !== 6) {
      return NextResponse.json(
        { error: '请输入6位验证码' },
        { status: 400 }
      )
    }

    // 验证短信验证码
    const smsService = createSMSService()
    const verifyResult = await smsService.verifyCode(phone, code)

    if (!verifyResult.success) {
      return NextResponse.json(
        { error: verifyResult.message },
        { status: 400 }
      )
    }

    // 验证成功，检查用户是否已存在
    const { data: existingUser, error: queryError } = await supabase
      .from('profiles')
      .select('*')
      .eq('phone', phone)
      .single()

    if (queryError && queryError.code !== 'PGRST116') {
      console.error('查询用户失败:', queryError)
      return NextResponse.json(
        { error: '登录失败，请重试' },
        { status: 500 }
      )
    }

    let userId: string
    let isNewUser = false

    if (existingUser) {
      // 用户已存在，直接登录
      userId = existingUser.id
    } else {
      // 新用户，创建账户
      const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
        phone,
        phone_confirm: true,
        user_metadata: {
          phone,
          auth_method: 'phone'
        }
      })

      if (createError || !newUser.user) {
        console.error('创建用户失败:', createError)
        return NextResponse.json(
          { error: '注册失败，请重试' },
          { status: 500 }
        )
      }

      userId = newUser.user.id
      isNewUser = true

      // 创建用户profile
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: userId,
          phone,
          auth_method: 'phone',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (profileError) {
        console.error('创建用户profile失败:', profileError)
        // 不返回错误，因为用户已经创建成功
      }
    }

    // 生成登录会话
    const { data: sessionData, error: sessionError } = await supabase.auth.admin.generateLink({
      type: 'magiclink',
      email: `${phone}@phone.local`, // 临时邮箱格式
      options: {
        redirectTo: process.env.NEXT_PUBLIC_APP_URL
      }
    })

    if (sessionError || !sessionData) {
      console.error('生成登录会话失败:', sessionError)
      return NextResponse.json(
        { error: '登录失败，请重试' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: verifyResult.message,
      isNewUser,
      sessionUrl: sessionData.properties?.action_link
    })

  } catch (error) {
    console.error('验证短信验证码失败:', error)
    return NextResponse.json(
      { error: '验证失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 处理OPTIONS请求（CORS预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
