'use client'

import { useState } from 'react'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import VoiceRecorder from '@/components/voice/VoiceRecorder'
import AIProcessor from '@/components/ai/AIProcessor'

export default function DemoPage() {
  const [transcript, setTranscript] = useState('')
  const [processedContent, setProcessedContent] = useState('')

  const handleTranscriptChange = (newTranscript: string) => {
    setTranscript(newTranscript)
  }

  const handleRecordingComplete = (finalTranscript: string, audioBlob?: Blob) => {
    console.log('Recording completed:', finalTranscript)
    if (audioBlob) {
      console.log('Audio blob size:', audioBlob.size)
    }
  }

  const handleProcessedContent = (content: string) => {
    setProcessedContent(content)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* <PERSON>er */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-4 flex items-center">
          <Link
            href="/"
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors mr-4"
          >
            <ArrowLeft className="w-5 h-5" />
          </Link>
          <div>
            <h1 className="text-xl font-bold text-gray-900">语音录制演示</h1>
            <p className="text-sm text-gray-600">体验语音转文字功能</p>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 py-8">
        {/* Introduction */}
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            语音录制 + AI整理演示
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            体验完整的语音转文字 + AI内容整理流程。先录制语音，然后使用AI将口语化内容
            整理成流畅的回忆录文本。这个功能使用最新的Gemini 2.0 Flash模型，
            专为中老年用户的回忆录创作优化。
          </p>
        </div>

        {/* Voice Recorder */}
        <div className="max-w-2xl mx-auto mb-8">
          <VoiceRecorder
            onTranscriptChange={handleTranscriptChange}
            onRecordingComplete={handleRecordingComplete}
            placeholder="点击下方的麦克风按钮开始录音，您说的话会实时显示在这里..."
          />
        </div>

        {/* AI Processing */}
        {transcript && (
          <div className="max-w-2xl mx-auto mb-8">
            <AIProcessor
              transcript={transcript}
              onProcessedContent={handleProcessedContent}
              context={{
                chapterTitle: "语音演示",
                memoirType: "功能测试"
              }}
            />
          </div>
        )}

        {/* Transcript Output */}
        {transcript && (
          <div className="max-w-2xl mx-auto mb-8">
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                原始识别结果
              </h3>
              <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                <p className="text-gray-900 leading-relaxed">{transcript}</p>
              </div>
              <div className="mt-4 text-sm text-gray-600">
                <p>字数统计: {transcript.length} 字</p>
              </div>
            </div>
          </div>
        )}

        {/* Processed Content Output */}
        {processedContent && (
          <div className="max-w-2xl mx-auto mb-8">
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                AI整理结果
              </h3>
              <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                <p className="text-gray-900 leading-relaxed whitespace-pre-wrap">{processedContent}</p>
              </div>
              <div className="mt-4 text-sm text-gray-600">
                <p>整理后字数: {processedContent.length} 字</p>
              </div>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="max-w-2xl mx-auto">
          <div className="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-2xl p-6">
            <h4 className="text-lg font-semibold text-amber-900 mb-3">
              💡 使用说明
            </h4>
            <ul className="space-y-2 text-amber-800">
              <li>• 首次使用时，浏览器会请求麦克风权限，请点击&ldquo;允许&rdquo;</li>
              <li>• 录音时请保持环境相对安静</li>
              <li>• 说话时语速适中，吐字清晰</li>
              <li>• 录音完成后，点击&ldquo;开始AI整理&rdquo;处理内容</li>
              <li>• AI会将口语化表达转换为流畅的书面语</li>
              <li>• 可以多次录制和整理，不断完善内容</li>
            </ul>
          </div>
        </div>

        {/* Browser Compatibility */}
        <div className="max-w-2xl mx-auto mt-8">
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
            <h4 className="text-lg font-semibold text-blue-900 mb-3">
              🌐 浏览器兼容性
            </h4>
            <div className="text-blue-800 space-y-2">
              <p><strong>推荐浏览器:</strong></p>
              <ul className="ml-4 space-y-1">
                <li>• Chrome (推荐)</li>
                <li>• Edge</li>
                <li>• Safari (iOS 14.5+)</li>
              </ul>
              <p className="mt-3">
                <strong>注意:</strong> 语音识别功能需要 HTTPS 连接或本地环境才能正常工作。
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
