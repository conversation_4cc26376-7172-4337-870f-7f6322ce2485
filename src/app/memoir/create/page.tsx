'use client'

import { useState, useEffect, useCallback, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { ArrowLeft, MessageCircle, FileText, Eye, Settings } from 'lucide-react'
import Link from 'next/link'
import { MemoirTemplate, getMemoirTemplateById } from '@/lib/memoir-templates'
import ConversationalCreation from '@/components/memoir/ConversationalCreation'
import RawRecords from '@/components/memoir/RawRecords'
import OrganizationPreview from '@/components/memoir/OrganizationPreview'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'

type TabType = 'conversation' | 'records' | 'preview'

interface TabConfig {
  id: TabType
  name: string
  icon: React.ReactNode
  description: string
}

const tabs: TabConfig[] = [
  {
    id: 'conversation',
    name: '对话创作',
    icon: <MessageCircle className="w-5 h-5" />,
    description: '通过AI对话和语音输入创作内容'
  },
  {
    id: 'records',
    name: '原始记录',
    icon: <FileText className="w-5 h-5" />,
    description: '管理语音录音、照片和文字记录'
  },
  {
    id: 'preview',
    name: '整理预览',
    icon: <Eye className="w-5 h-5" />,
    description: '整理编辑内容并预览最终效果'
  }
]

function MemoirCreateContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [activeTab, setActiveTab] = useState<TabType>('conversation')
  const [template, setTemplate] = useState<MemoirTemplate | null>(null)
  const [memoirTitle, setMemoirTitle] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  useEffect(() => {
    // 获取模板信息
    const templateId = searchParams.get('template')
    if (templateId) {
      const selectedTemplate = getMemoirTemplateById(templateId)
      if (selectedTemplate) {
        setTemplate(selectedTemplate)
        setMemoirTitle(`我的${selectedTemplate.name}`)
      }
    } else {
      // 尝试从localStorage获取
      const storedTemplate = localStorage.getItem('selected_template')
      if (storedTemplate) {
        try {
          const parsedTemplate = JSON.parse(storedTemplate)
          setTemplate(parsedTemplate)
          setMemoirTitle(`我的${parsedTemplate.name}`)
        } catch (error) {
          console.error('Failed to parse stored template:', error)
        }
      }
    }
    setIsLoading(false)
  }, [searchParams])

  // 自动保存功能
  useEffect(() => {
    const autoSave = () => {
      try {
        const memoirData = {
          template,
          title: memoirTitle,
          activeTab,
          lastModified: new Date().toISOString()
        }
        localStorage.setItem('memoir_autosave', JSON.stringify(memoirData))
        setLastSaved(new Date())
      } catch (error) {
        console.error('Auto-save failed:', error)
      }
    }

    // 设置自动保存间隔（每30秒）
    const autoSaveInterval = setInterval(autoSave, 30000)

    return () => clearInterval(autoSaveInterval)
  }, [template, memoirTitle, activeTab])

  const handleTabChange = (tabId: TabType) => {
    setActiveTab(tabId)
  }

  const handleBack = () => {
    router.back()
  }

  // 内容变更时触发自动保存
  const handleContentChange = useCallback(() => {
    try {
      const memoirData = {
        template,
        title: memoirTitle,
        activeTab,
        lastModified: new Date().toISOString(),
        timestamp: Date.now() // 添加时间戳避免重复
      }
      localStorage.setItem('memoir_autosave', JSON.stringify(memoirData))
      setLastSaved(new Date())
    } catch (error) {
      console.error('Auto-save failed:', error)
    }
  }, [template, memoirTitle, activeTab])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg">加载中...</p>
        </div>
      </div>
    )
  }

  if (!template) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-center">模板未找到</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600 mb-4">请先选择一个回忆录模板</p>
            <Link href="/">
              <Button>返回首页</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'conversation':
        return (
          <ConversationalCreation
            template={template}
            onContentChange={handleContentChange}
          />
        )
      case 'records':
        return <RawRecords />
      case 'preview':
        return (
          <OrganizationPreview
            template={template}
            onContentChange={handleContentChange}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">{memoirTitle}</h1>
                <p className="text-sm text-gray-600">基于 {template.name} 模板</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              {lastSaved && (
                <span className="text-sm text-green-600 font-medium">
                  已自动保存 {lastSaved.toLocaleTimeString()}
                </span>
              )}
              <Button
                variant="ghost"
                size="sm"
                icon={<Settings className="w-4 h-4" />}
              >
                设置
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  {tab.icon}
                  <span className="hidden sm:inline">{tab.name}</span>
                </div>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Description */}
      <div className="bg-blue-50 border-b border-blue-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <p className="text-sm text-blue-700">
            {tabs.find(tab => tab.id === activeTab)?.description}
          </p>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderTabContent()}
      </main>
    </div>
  )
}

export default function MemoirCreatePage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg">加载中...</p>
        </div>
      </div>
    }>
      <MemoirCreateContent />
    </Suspense>
  )
}
