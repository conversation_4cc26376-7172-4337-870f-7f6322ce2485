@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

/* Mobile-first optimizations for elderly users */
@layer utilities {
  /* Hide scrollbars for horizontal scroll */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }

  /* Smooth scrolling for mobile */
  .scroll-smooth {
    scroll-behavior: smooth;
  }

  /* Touch-friendly spacing */
  .touch-spacing {
    padding: 1rem;
    margin: 0.5rem;
  }

  /* Large touch targets for elderly users */
  .touch-target {
    min-height: 48px;
    min-width: 48px;
  }

  /* Enhanced focus states for accessibility */
  .focus-enhanced:focus {
    outline: 3px solid #3B82F6;
    outline-offset: 2px;
  }

  /* Mobile-optimized card spacing */
  .mobile-card-spacing {
    @apply p-4 m-2 md:p-6 md:m-4;
  }

  /* Horizontal scroll container */
  .horizontal-scroll {
    display: flex;
    overflow-x: auto;
    gap: 1rem;
    padding-bottom: 1rem;
    scroll-snap-type: x mandatory;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .horizontal-scroll::-webkit-scrollbar {
    display: none;
  }

  /* Snap scroll items */
  .snap-item {
    scroll-snap-align: center;
    flex-shrink: 0;
  }
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
