@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

/* Mobile-first optimizations for elderly users */
@layer utilities {
  /* Hide scrollbars for horizontal scroll */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }

  /* Smooth scrolling for mobile */
  .scroll-smooth {
    scroll-behavior: smooth;
  }

  /* Touch-friendly spacing */
  .touch-spacing {
    padding: 1rem;
    margin: 0.5rem;
  }

  /* Large touch targets for elderly users */
  .touch-target {
    min-height: 48px;
    min-width: 48px;
  }

  /* Enhanced focus states for accessibility */
  .focus-enhanced:focus {
    outline: 3px solid #3B82F6;
    outline-offset: 2px;
  }

  /* Mobile-optimized card spacing */
  .mobile-card-spacing {
    @apply p-4 m-2 md:p-6 md:m-4;
  }

  /* Horizontal scroll container */
  .horizontal-scroll {
    display: flex;
    overflow-x: auto;
    gap: 1rem;
    padding-bottom: 1rem;
    scroll-snap-type: x mandatory;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .horizontal-scroll::-webkit-scrollbar {
    display: none;
  }

  /* Snap scroll items */
  .snap-item {
    scroll-snap-align: center;
    flex-shrink: 0;
  }

  /* Mobile-specific optimizations */
  @media (max-width: 768px) {
    .mobile-text-lg {
      font-size: 1.125rem;
      line-height: 1.75rem;
    }

    .mobile-text-xl {
      font-size: 1.25rem;
      line-height: 1.75rem;
    }

    .mobile-spacing {
      padding: 1rem;
      margin: 0.75rem 0;
    }

    .mobile-button {
      min-height: 48px;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      font-weight: 500;
    }
  }

  /* Smooth transitions */
  .smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Card hover effects */
  .card-hover {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  }

  .card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Loading animations */
  @keyframes pulse-gentle {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  .pulse-gentle {
    animation: pulse-gentle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Gesture feedback */
  .gesture-feedback {
    transition: transform 0.1s ease-out;
  }

  .gesture-feedback:active {
    transform: scale(0.98);
  }
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
