import { useEffect, useRef, useState } from 'react'

interface SwipeGestureOptions {
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onSwipeUp?: () => void
  onSwipeDown?: () => void
  threshold?: number
  preventDefaultTouchmoveEvent?: boolean
}

interface TouchPosition {
  x: number
  y: number
}

export function useSwipeGesture(options: SwipeGestureOptions) {
  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    threshold = 50,
    preventDefaultTouchmoveEvent = false
  } = options

  const [touchStart, setTouchStart] = useState<TouchPosition | null>(null)
  const [touchEnd, setTouchEnd] = useState<TouchPosition | null>(null)
  const elementRef = useRef<HTMLDivElement>(null)

  // 最小滑动距离
  const minSwipeDistance = threshold

  const onTouchStart = (e: TouchEvent) => {
    setTouchEnd(null) // 重置touchEnd
    setTouchStart({
      x: e.targetTouches[0].clientX,
      y: e.targetTouches[0].clientY
    })
  }

  const onTouchMove = (e: TouchEvent) => {
    if (preventDefaultTouchmoveEvent) {
      e.preventDefault()
    }
    setTouchEnd({
      x: e.targetTouches[0].clientX,
      y: e.targetTouches[0].clientY
    })
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const distanceX = touchStart.x - touchEnd.x
    const distanceY = touchStart.y - touchEnd.y
    const isLeftSwipe = distanceX > minSwipeDistance
    const isRightSwipe = distanceX < -minSwipeDistance
    const isUpSwipe = distanceY > minSwipeDistance
    const isDownSwipe = distanceY < -minSwipeDistance

    // 确定主要滑动方向
    if (Math.abs(distanceX) > Math.abs(distanceY)) {
      // 水平滑动
      if (isLeftSwipe && onSwipeLeft) {
        onSwipeLeft()
      }
      if (isRightSwipe && onSwipeRight) {
        onSwipeRight()
      }
    } else {
      // 垂直滑动
      if (isUpSwipe && onSwipeUp) {
        onSwipeUp()
      }
      if (isDownSwipe && onSwipeDown) {
        onSwipeDown()
      }
    }
  }

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    // 添加触摸事件监听器
    element.addEventListener('touchstart', onTouchStart, { passive: true })
    element.addEventListener('touchmove', onTouchMove, { passive: !preventDefaultTouchmoveEvent })
    element.addEventListener('touchend', onTouchEnd, { passive: true })

    return () => {
      element.removeEventListener('touchstart', onTouchStart)
      element.removeEventListener('touchmove', onTouchMove)
      element.removeEventListener('touchend', onTouchEnd)
    }
  }, [touchStart, touchEnd, onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown])

  return elementRef
}

// 简化版本，专门用于标签页切换
export function useTabSwipe(tabs: string[], activeTab: string, onTabChange: (tab: string) => void) {
  const currentIndex = tabs.indexOf(activeTab)
  
  const swipeRef = useSwipeGesture({
    onSwipeLeft: () => {
      // 向左滑动，切换到下一个标签页
      if (currentIndex < tabs.length - 1) {
        onTabChange(tabs[currentIndex + 1])
      }
    },
    onSwipeRight: () => {
      // 向右滑动，切换到上一个标签页
      if (currentIndex > 0) {
        onTabChange(tabs[currentIndex - 1])
      }
    },
    threshold: 80 // 标签页切换需要更大的滑动距离
  })

  return swipeRef
}

// 用于项目卡片的水平滚动手势
export function useHorizontalScroll() {
  const scrollRef = useRef<HTMLDivElement>(null)
  
  const swipeRef = useSwipeGesture({
    onSwipeLeft: () => {
      if (scrollRef.current) {
        scrollRef.current.scrollBy({
          left: 320, // 卡片宽度 + 间距
          behavior: 'smooth'
        })
      }
    },
    onSwipeRight: () => {
      if (scrollRef.current) {
        scrollRef.current.scrollBy({
          left: -320,
          behavior: 'smooth'
        })
      }
    },
    threshold: 30 // 较小的阈值，更容易触发滚动
  })

  // 合并refs
  const combinedRef = useRef<HTMLDivElement>(null)
  
  useEffect(() => {
    if (combinedRef.current) {
      scrollRef.current = combinedRef.current
      if (swipeRef.current) {
        swipeRef.current = combinedRef.current
      }
    }
  }, [])

  return combinedRef
}
