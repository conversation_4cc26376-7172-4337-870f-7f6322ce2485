// 照片上传服务
export interface PhotoUploadOptions {
  readonly maxFileSize?: number // 最大文件大小（字节）
  readonly allowedTypes?: string[] // 允许的文件类型
  readonly quality?: number // 压缩质量 (0-1)
  readonly maxWidth?: number // 最大宽度
  readonly maxHeight?: number // 最大高度
  readonly generateThumbnail?: boolean // 是否生成缩略图
}

export interface PhotoMetadata {
  readonly originalName: string
  readonly size: number
  readonly type: string
  readonly dimensions: {
    readonly width: number
    readonly height: number
  }
  readonly lastModified: number
}

export interface ProcessedPhoto {
  readonly file: File
  readonly url: string
  readonly thumbnail?: string
  readonly metadata: PhotoMetadata
}

export interface UploadProgress {
  readonly loaded: number
  readonly total: number
  readonly percentage: number
}

export class PhotoUploadService {
  private static readonly DEFAULT_OPTIONS: Required<PhotoUploadOptions> = {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/heic'],
    quality: 0.8,
    maxWidth: 1920,
    maxHeight: 1080,
    generateThumbnail: true
  }

  // 验证文件
  static validateFile(file: File, options: PhotoUploadOptions = {}): string | null {
    const opts = { ...this.DEFAULT_OPTIONS, ...options }

    // 检查文件类型
    if (!opts.allowedTypes.includes(file.type)) {
      return `不支持的文件类型。支持的格式：${opts.allowedTypes.join(', ')}`
    }

    // 检查文件大小
    if (file.size > opts.maxFileSize) {
      const maxSizeMB = Math.round(opts.maxFileSize / (1024 * 1024))
      return `文件太大。最大支持 ${maxSizeMB}MB`
    }

    return null
  }

  // 处理单个照片
  static async processPhoto(
    file: File, 
    options: PhotoUploadOptions = {}
  ): Promise<ProcessedPhoto> {
    const opts = { ...this.DEFAULT_OPTIONS, ...options }

    // 验证文件
    const validationError = this.validateFile(file, options)
    if (validationError) {
      throw new Error(validationError)
    }

    // 获取图片尺寸
    const dimensions = await this.getImageDimensions(file)
    
    // 压缩图片（如果需要）
    const processedFile = await this.compressImage(file, opts, dimensions)
    
    // 生成预览URL
    const url = URL.createObjectURL(processedFile)
    
    // 生成缩略图
    let thumbnail: string | undefined
    if (opts.generateThumbnail) {
      thumbnail = await this.generateThumbnail(processedFile)
    }

    // 构建元数据
    const metadata: PhotoMetadata = {
      originalName: file.name,
      size: processedFile.size,
      type: processedFile.type,
      dimensions: {
        width: dimensions.width,
        height: dimensions.height
      },
      lastModified: file.lastModified
    }

    return {
      file: processedFile,
      url,
      thumbnail,
      metadata
    }
  }

  // 批量处理照片
  static async processPhotos(
    files: FileList | File[],
    options: PhotoUploadOptions = {},
    onProgress?: (progress: UploadProgress, index: number) => void
  ): Promise<ProcessedPhoto[]> {
    const fileArray = Array.from(files)
    const results: ProcessedPhoto[] = []
    
    for (let i = 0; i < fileArray.length; i++) {
      const file = fileArray[i]
      
      try {
        // 报告进度
        onProgress?.({
          loaded: i,
          total: fileArray.length,
          percentage: Math.round((i / fileArray.length) * 100)
        }, i)

        const processedPhoto = await this.processPhoto(file, options)
        results.push(processedPhoto)
        
        // 报告完成进度
        onProgress?.({
          loaded: i + 1,
          total: fileArray.length,
          percentage: Math.round(((i + 1) / fileArray.length) * 100)
        }, i)
      } catch (error) {
        console.error(`处理照片 ${file.name} 失败:`, error)
        // 继续处理其他文件
      }
    }

    return results
  }

  // 获取图片尺寸
  private static getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      const url = URL.createObjectURL(file)
      
      img.onload = () => {
        URL.revokeObjectURL(url)
        resolve({
          width: img.naturalWidth,
          height: img.naturalHeight
        })
      }
      
      img.onerror = () => {
        URL.revokeObjectURL(url)
        reject(new Error('无法读取图片尺寸'))
      }
      
      img.src = url
    })
  }

  // 压缩图片
  private static async compressImage(
    file: File,
    options: Required<PhotoUploadOptions>,
    dimensions: { width: number; height: number }
  ): Promise<File> {
    // 如果图片尺寸在限制内且质量为1，直接返回原文件
    if (dimensions.width <= options.maxWidth && 
        dimensions.height <= options.maxHeight && 
        options.quality === 1) {
      return file
    }

    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (!ctx) {
        reject(new Error('无法创建canvas上下文'))
        return
      }

      const img = new Image()
      const url = URL.createObjectURL(file)

      img.onload = () => {
        URL.revokeObjectURL(url)

        // 计算新尺寸
        const { width: newWidth, height: newHeight } = this.calculateNewDimensions(
          dimensions.width,
          dimensions.height,
          options.maxWidth,
          options.maxHeight
        )

        // 设置canvas尺寸
        canvas.width = newWidth
        canvas.height = newHeight

        // 绘制压缩后的图片
        ctx.drawImage(img, 0, 0, newWidth, newHeight)

        // 转换为Blob
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: file.lastModified
              })
              resolve(compressedFile)
            } else {
              reject(new Error('图片压缩失败'))
            }
          },
          file.type,
          options.quality
        )
      }

      img.onerror = () => {
        URL.revokeObjectURL(url)
        reject(new Error('图片加载失败'))
      }

      img.src = url
    })
  }

  // 生成缩略图
  private static async generateThumbnail(file: File, size = 200): Promise<string> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (!ctx) {
        reject(new Error('无法创建canvas上下文'))
        return
      }

      const img = new Image()
      const url = URL.createObjectURL(file)

      img.onload = () => {
        URL.revokeObjectURL(url)

        // 计算缩略图尺寸（保持宽高比）
        const { width, height } = this.calculateNewDimensions(
          img.naturalWidth,
          img.naturalHeight,
          size,
          size
        )

        canvas.width = width
        canvas.height = height

        // 绘制缩略图
        ctx.drawImage(img, 0, 0, width, height)

        // 转换为DataURL
        const thumbnailUrl = canvas.toDataURL('image/jpeg', 0.7)
        resolve(thumbnailUrl)
      }

      img.onerror = () => {
        URL.revokeObjectURL(url)
        reject(new Error('缩略图生成失败'))
      }

      img.src = url
    })
  }

  // 计算新尺寸（保持宽高比）
  private static calculateNewDimensions(
    originalWidth: number,
    originalHeight: number,
    maxWidth: number,
    maxHeight: number
  ): { width: number; height: number } {
    const aspectRatio = originalWidth / originalHeight

    let newWidth = originalWidth
    let newHeight = originalHeight

    // 如果超出最大宽度
    if (newWidth > maxWidth) {
      newWidth = maxWidth
      newHeight = newWidth / aspectRatio
    }

    // 如果超出最大高度
    if (newHeight > maxHeight) {
      newHeight = maxHeight
      newWidth = newHeight * aspectRatio
    }

    return {
      width: Math.round(newWidth),
      height: Math.round(newHeight)
    }
  }

  // 清理URL
  static revokeObjectURL(url: string): void {
    URL.revokeObjectURL(url)
  }

  // 检查浏览器支持
  static isSupported(): boolean {
    return !!(
      typeof FileReader !== 'undefined' &&
      typeof URL !== 'undefined' &&
      typeof URL.createObjectURL === 'function' &&
      typeof HTMLCanvasElement !== 'undefined'
    )
  }
}
