// 内容预览服务
export interface PreviewOptions {
  readonly theme?: 'default' | 'classic' | 'modern' | 'elegant'
  readonly fontSize?: 'small' | 'medium' | 'large' | 'extra-large'
  readonly lineHeight?: 'compact' | 'normal' | 'relaxed'
  readonly showMetadata?: boolean
  readonly showAudio?: boolean
  readonly showImages?: boolean
}

export interface ContentSuggestion {
  readonly id: string
  readonly type: 'grammar' | 'style' | 'structure' | 'enhancement'
  readonly severity: 'low' | 'medium' | 'high'
  readonly message: string
  readonly suggestion: string
  readonly originalText: string
  readonly suggestedText: string
  readonly position: {
    readonly start: number
    readonly end: number
  }
}

export interface PreviewData {
  readonly content: string
  readonly wordCount: number
  readonly readingTime: number
  readonly suggestions: ContentSuggestion[]
  readonly metadata: {
    readonly lastModified: string
    readonly author: string
    readonly chapter?: string
    readonly tags?: string[]
  }
}

export class ContentPreviewService {
  private static readonly DEFAULT_OPTIONS: Required<PreviewOptions> = {
    theme: 'default',
    fontSize: 'medium',
    lineHeight: 'normal',
    showMetadata: true,
    showAudio: true,
    showImages: true
  }

  // 生成内容预览
  static generatePreview(content: string): PreviewData {
    const wordCount = this.calculateWordCount(content)
    const readingTime = this.calculateReadingTime(wordCount)
    const suggestions = this.generateSuggestions(content)

    return {
      content: this.formatContent(content),
      wordCount,
      readingTime,
      suggestions,
      metadata: {
        lastModified: new Date().toISOString(),
        author: '用户',
        tags: this.extractTags(content)
      }
    }
  }

  // 计算字数
  private static calculateWordCount(content: string): number {
    // 中文字符计数
    const chineseChars = (content.match(/[\u4e00-\u9fff]/g) || []).length
    // 英文单词计数
    const englishWords = content
      .replace(/[\u4e00-\u9fff]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 0).length
    
    return chineseChars + englishWords
  }

  // 计算阅读时间（分钟）
  private static calculateReadingTime(wordCount: number): number {
    // 中文阅读速度约为每分钟300-400字
    const wordsPerMinute = 350
    return Math.ceil(wordCount / wordsPerMinute)
  }

  // 格式化内容
  private static formatContent(content: string): string {
    let formatted = content

    // 段落分隔
    formatted = formatted.replace(/\n\s*\n/g, '\n\n')
    
    // 移除多余空格
    formatted = formatted.replace(/[ \t]+/g, ' ')
    
    // 确保句号后有适当间距
    formatted = formatted.replace(/([。！？])\s*([^\s])/g, '$1 $2')

    return formatted.trim()
  }

  // 生成内容建议
  private static generateSuggestions(content: string): ContentSuggestion[] {
    const suggestions: ContentSuggestion[] = []

    // 检查段落长度
    const paragraphs = content.split('\n\n')
    paragraphs.forEach((paragraph, index) => {
      if (paragraph.length > 500) {
        suggestions.push({
          id: `long-paragraph-${index}`,
          type: 'structure',
          severity: 'medium',
          message: '段落过长，建议分割',
          suggestion: '考虑将长段落分成几个较短的段落，提高可读性',
          originalText: paragraph.substring(0, 50) + '...',
          suggestedText: '分割为多个段落',
          position: {
            start: content.indexOf(paragraph),
            end: content.indexOf(paragraph) + paragraph.length
          }
        })
      }
    })

    // 检查重复词汇
    const words = content.match(/[\u4e00-\u9fff]{2,}/g) || []
    const wordCount: Record<string, number> = {}
    words.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1
    })

    Object.entries(wordCount).forEach(([word, count]) => {
      if (count > 5 && word.length > 1) {
        suggestions.push({
          id: `repeated-word-${word}`,
          type: 'style',
          severity: 'low',
          message: `词汇"${word}"使用频率较高`,
          suggestion: '考虑使用同义词来增加文本的多样性',
          originalText: word,
          suggestedText: '使用同义词替换',
          position: {
            start: content.indexOf(word),
            end: content.indexOf(word) + word.length
          }
        })
      }
    })

    // 检查句子长度
    const sentences = content.split(/[。！？]/).filter(s => s.trim().length > 0)
    sentences.forEach((sentence, index) => {
      if (sentence.length > 100) {
        const start = content.indexOf(sentence)
        suggestions.push({
          id: `long-sentence-${index}`,
          type: 'structure',
          severity: 'medium',
          message: '句子过长，可能影响理解',
          suggestion: '考虑将长句分解为几个短句',
          originalText: sentence.substring(0, 30) + '...',
          suggestedText: '分解为短句',
          position: {
            start,
            end: start + sentence.length
          }
        })
      }
    })

    // 检查标点符号
    if (!/[。！？]$/.test(content.trim())) {
      suggestions.push({
        id: 'missing-punctuation',
        type: 'grammar',
        severity: 'low',
        message: '内容末尾缺少标点符号',
        suggestion: '在内容末尾添加适当的标点符号',
        originalText: content.slice(-10),
        suggestedText: content.slice(-10) + '。',
        position: {
          start: content.length - 1,
          end: content.length
        }
      })
    }

    return suggestions
  }

  // 提取标签
  private static extractTags(content: string): string[] {
    const tags: string[] = []
    
    // 提取时间相关标签
    if (/\d{4}年|\d{1,2}月|\d{1,2}日|春天|夏天|秋天|冬天/.test(content)) {
      tags.push('时间')
    }
    
    // 提取地点相关标签
    if (/北京|上海|广州|深圳|家里|学校|公司|医院/.test(content)) {
      tags.push('地点')
    }
    
    // 提取人物相关标签
    if (/爸爸|妈妈|父亲|母亲|儿子|女儿|朋友|同事/.test(content)) {
      tags.push('人物')
    }
    
    // 提取情感相关标签
    if (/高兴|快乐|开心|难过|伤心|激动|感动/.test(content)) {
      tags.push('情感')
    }

    return tags
  }

  // 应用建议
  static applySuggestion(content: string, suggestion: ContentSuggestion): string {
    const { position, suggestedText } = suggestion
    
    return content.substring(0, position.start) + 
           suggestedText + 
           content.substring(position.end)
  }

  // 获取主题样式
  static getThemeStyles(theme: PreviewOptions['theme']): Record<string, string> {
    const themes = {
      default: {
        backgroundColor: '#ffffff',
        color: '#1f2937',
        fontFamily: 'system-ui, -apple-system, sans-serif',
        lineHeight: '1.6'
      },
      classic: {
        backgroundColor: '#fefbf3',
        color: '#2d1810',
        fontFamily: 'Georgia, serif',
        lineHeight: '1.8'
      },
      modern: {
        backgroundColor: '#f8fafc',
        color: '#0f172a',
        fontFamily: 'Inter, sans-serif',
        lineHeight: '1.7'
      },
      elegant: {
        backgroundColor: '#fafafa',
        color: '#262626',
        fontFamily: 'Crimson Text, serif',
        lineHeight: '1.75'
      }
    }

    return themes[theme || 'default']
  }

  // 获取字体大小样式
  static getFontSizeStyles(fontSize: PreviewOptions['fontSize']): Record<string, string> {
    const sizes = {
      small: { fontSize: '14px' },
      medium: { fontSize: '16px' },
      large: { fontSize: '18px' },
      'extra-large': { fontSize: '20px' }
    }

    return sizes[fontSize || 'medium']
  }

  // 导出预览内容
  static exportPreview(previewData: PreviewData, format: 'html' | 'text' | 'markdown'): string {
    switch (format) {
      case 'html':
        return this.exportAsHTML(previewData)
      case 'markdown':
        return this.exportAsMarkdown(previewData)
      case 'text':
      default:
        return previewData.content
    }
  }

  private static exportAsHTML(previewData: PreviewData): string {
    const { content, metadata } = previewData
    
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回忆录预览</title>
    <style>
        body { font-family: system-ui, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
        .metadata { color: #666; font-size: 14px; margin-bottom: 20px; }
        .content { white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="metadata">
        <p>作者：${metadata.author}</p>
        <p>最后修改：${new Date(metadata.lastModified).toLocaleString('zh-CN')}</p>
        ${metadata.tags ? `<p>标签：${metadata.tags.join(', ')}</p>` : ''}
    </div>
    <div class="content">${content}</div>
</body>
</html>
    `.trim()
  }

  private static exportAsMarkdown(previewData: PreviewData): string {
    const { content, metadata } = previewData
    
    let markdown = `# 回忆录内容\n\n`
    markdown += `**作者：** ${metadata.author}\n`
    markdown += `**最后修改：** ${new Date(metadata.lastModified).toLocaleString('zh-CN')}\n`
    
    if (metadata.tags && metadata.tags.length > 0) {
      markdown += `**标签：** ${metadata.tags.join(', ')}\n`
    }
    
    markdown += `\n---\n\n${content}`
    
    return markdown
  }
}
