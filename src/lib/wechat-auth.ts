// 微信登录相关接口和类型
export interface WeChatConfig {
  appId: string
  appSecret: string
  redirectUri: string
}

export interface WeChatUserInfo {
  openid: string
  nickname: string
  sex: number
  province: string
  city: string
  country: string
  headimgurl: string
  unionid?: string
}

export interface WeChatAuthResult {
  success: boolean
  userInfo?: WeChatUserInfo
  error?: string
}

export class WeChatAuthService {
  private config: WeChatConfig
  
  constructor(config: WeChatConfig) {
    this.config = config
  }
  
  // 生成微信授权URL
  generateAuthUrl(state?: string): string {
    const params = new URLSearchParams({
      appid: this.config.appId,
      redirect_uri: encodeURIComponent(this.config.redirectUri),
      response_type: 'code',
      scope: 'snsapi_userinfo',
      state: state || 'STATE'
    })
    
    return `https://open.weixin.qq.com/connect/oauth2/authorize?${params.toString()}#wechat_redirect`
  }
  
  // 跳转到微信授权页面
  redirectToWeChatAuth(state?: string): void {
    const authUrl = this.generateAuthUrl(state)
    window.location.href = authUrl
  }
  
  // 处理微信授权回调
  async handleAuthCallback(code: string): Promise<WeChatAuthResult> {
    try {
      // 第一步：通过code获取access_token
      const tokenResponse = await this.getAccessToken(code)
      if (!tokenResponse.success) {
        return { success: false, error: tokenResponse.error }
      }
      
      // 第二步：通过access_token获取用户信息
      const userInfoResponse = await this.getUserInfo(
        tokenResponse.accessToken!,
        tokenResponse.openid!
      )
      
      return userInfoResponse
    } catch (error) {
      console.error('微信授权回调处理失败:', error)
      return {
        success: false,
        error: '微信登录失败，请重试'
      }
    }
  }
  
  // 获取access_token
  private async getAccessToken(code: string): Promise<{
    success: boolean
    accessToken?: string
    openid?: string
    error?: string
  }> {
    try {
      const params = new URLSearchParams({
        appid: this.config.appId,
        secret: this.config.appSecret,
        code,
        grant_type: 'authorization_code'
      })
      
      const response = await fetch(
        `https://api.weixin.qq.com/sns/oauth2/access_token?${params.toString()}`
      )
      
      const data = await response.json()
      
      if (data.errcode) {
        return {
          success: false,
          error: `微信API错误: ${data.errmsg}`
        }
      }
      
      return {
        success: true,
        accessToken: data.access_token,
        openid: data.openid
      }
    } catch (error) {
      console.error('获取微信access_token失败:', error)
      return {
        success: false,
        error: '获取微信授权信息失败'
      }
    }
  }
  
  // 获取用户信息
  private async getUserInfo(accessToken: string, openid: string): Promise<WeChatAuthResult> {
    try {
      const params = new URLSearchParams({
        access_token: accessToken,
        openid,
        lang: 'zh_CN'
      })
      
      const response = await fetch(
        `https://api.weixin.qq.com/sns/userinfo?${params.toString()}`
      )
      
      const data = await response.json()
      
      if (data.errcode) {
        return {
          success: false,
          error: `获取用户信息失败: ${data.errmsg}`
        }
      }
      
      return {
        success: true,
        userInfo: data as WeChatUserInfo
      }
    } catch (error) {
      console.error('获取微信用户信息失败:', error)
      return {
        success: false,
        error: '获取用户信息失败'
      }
    }
  }
}

// 模拟微信登录服务（用于开发和演示）
export class MockWeChatAuthService {
  async simulateWeChatLogin(): Promise<WeChatAuthResult> {
    // 模拟登录延迟
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟用户信息
    const mockUserInfo: WeChatUserInfo = {
      openid: 'mock_openid_' + Date.now(),
      nickname: '微信用户',
      sex: 1,
      province: '广东',
      city: '深圳',
      country: '中国',
      headimgurl: 'https://via.placeholder.com/100x100?text=微信头像',
      unionid: 'mock_unionid_' + Date.now()
    }
    
    return {
      success: true,
      userInfo: mockUserInfo
    }
  }
}

// 微信登录服务工厂
export function createWeChatAuthService(): WeChatAuthService | MockWeChatAuthService {
  const appId = process.env.NEXT_PUBLIC_WECHAT_APP_ID
  const appSecret = process.env.WECHAT_APP_SECRET
  const redirectUri = process.env.NEXT_PUBLIC_WECHAT_REDIRECT_URI
  
  // 如果配置了真实的微信应用信息，使用真实服务
  if (appId && appSecret && redirectUri) {
    return new WeChatAuthService({
      appId,
      appSecret,
      redirectUri
    })
  }
  
  // 否则使用模拟服务
  return new MockWeChatAuthService()
}

// 检查是否为微信浏览器
export function isWeChatBrowser(): boolean {
  if (typeof window === 'undefined') return false
  
  const userAgent = window.navigator.userAgent.toLowerCase()
  return userAgent.includes('micromessenger')
}
