import { ConversationSessionManager, ConversationSession } from './conversation-sessions'
import { MemoirTemplate } from './memoir-templates'

export interface ContentBlock {
  id: string
  chapterIndex: number
  chapterTitle: string
  originalContent: string
  enhancedContent: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  suggestions: string[]
  keyPoints: string[]
  timestamp: Date
}

export interface ProcessingResult {
  success: boolean
  contentBlocks: ContentBlock[]
  overallSummary: string
  completionPercentage: number
  suggestions: string[]
  error?: string
}

export interface BeforeAfterComparison {
  blockId: string
  chapterTitle: string
  original: string
  enhanced: string
  changes: {
    type: 'addition' | 'modification' | 'improvement'
    description: string
    position: number
  }[]
  userDecision: 'pending' | 'accepted' | 'rejected' | 'modified'
  userModifications?: string
}

export class AIContentProcessor {
  private static readonly STORAGE_KEY = 'memoir_processed_content'
  private static readonly COMPARISONS_KEY = 'memoir_content_comparisons'

  // 处理所有对话内容并生成整合的回忆录
  static async processAllContent(
    template: MemoirTemplate,
    onProgress?: (progress: number, status: string) => void
  ): Promise<ProcessingResult> {
    try {
      onProgress?.(10, '正在收集对话内容...')
      
      // 获取所有对话会话
      const sessions = ConversationSessionManager.getAllSessions()
      const templateSessions = sessions.filter(s => s.templateId === template.id)
      
      if (templateSessions.length === 0) {
        return {
          success: false,
          contentBlocks: [],
          overallSummary: '',
          completionPercentage: 0,
          suggestions: [],
          error: '没有找到相关的对话内容'
        }
      }

      onProgress?.(30, '正在分析内容结构...')

      // 按章节组织内容
      const contentByChapter = this.organizeContentByChapter(templateSessions, template)
      
      onProgress?.(50, '正在使用AI优化内容...')

      // 处理每个章节的内容
      const contentBlocks: ContentBlock[] = []
      let processedChapters = 0

      for (const [chapterIndex, chapterContent] of contentByChapter.entries()) {
        const chapter = template.chapters[chapterIndex]
        if (!chapter || !chapterContent.length) continue

        const originalContent = this.combineChapterContent(chapterContent)
        const enhancedContent = await this.enhanceChapterContent(
          originalContent,
          chapter.title,
          chapter.description
        )

        const contentBlock: ContentBlock = {
          id: `chapter_${chapterIndex}_${Date.now()}`,
          chapterIndex,
          chapterTitle: chapter.title,
          originalContent,
          enhancedContent,
          status: 'completed',
          suggestions: this.generateSuggestions(originalContent),
          keyPoints: this.extractKeyPoints(enhancedContent),
          timestamp: new Date()
        }

        contentBlocks.push(contentBlock)
        processedChapters++
        
        const progress = 50 + (processedChapters / contentByChapter.size) * 40
        onProgress?.(progress, `正在处理第${processedChapters}章...`)
      }

      onProgress?.(95, '正在生成整体摘要...')

      // 生成整体摘要和建议
      const overallSummary = this.generateOverallSummary(contentBlocks)
      const suggestions = this.generateOverallSuggestions(contentBlocks, template)
      const completionPercentage = this.calculateCompletionPercentage(contentBlocks, template)

      // 保存处理结果
      this.saveProcessedContent({
        success: true,
        contentBlocks,
        overallSummary,
        completionPercentage,
        suggestions
      })

      onProgress?.(100, '内容处理完成!')

      return {
        success: true,
        contentBlocks,
        overallSummary,
        completionPercentage,
        suggestions
      }

    } catch (error) {
      console.error('AI content processing failed:', error)
      return {
        success: false,
        contentBlocks: [],
        overallSummary: '',
        completionPercentage: 0,
        suggestions: [],
        error: error instanceof Error ? error.message : '内容处理失败'
      }
    }
  }

  // 按章节组织内容
  private static organizeContentByChapter(
    sessions: ConversationSession[],
    template: MemoirTemplate
  ): Map<number, string[]> {
    const contentByChapter = new Map<number, string[]>()

    // 初始化所有章节
    template.chapters.forEach((_, index) => {
      contentByChapter.set(index, [])
    })

    // 按章节分组对话内容
    sessions.forEach(session => {
      const chapterContent = contentByChapter.get(session.chapterIndex) || []
      
      // 提取用户的回答内容
      const userMessages = session.messages
        .filter(msg => msg.type === 'user')
        .map(msg => msg.content)
        .filter(content => content && content.trim() !== '语音消息')

      if (userMessages.length > 0) {
        chapterContent.push(...userMessages)
        contentByChapter.set(session.chapterIndex, chapterContent)
      }
    })

    return contentByChapter
  }

  // 合并章节内容
  private static combineChapterContent(contents: string[]): string {
    return contents
      .filter(content => content && content.trim())
      .join('\n\n')
  }

  // AI增强章节内容
  private static async enhanceChapterContent(
    originalContent: string,
    chapterTitle: string,
    chapterDescription: string
  ): Promise<string> {
    // 模拟AI处理 - 在实际应用中这里会调用真实的AI API
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))

    if (!originalContent.trim()) {
      return `# ${chapterTitle}\n\n${chapterDescription}\n\n*此章节暂无内容，建议继续添加相关回忆和故事。*`
    }

    // 简单的内容增强逻辑
    const sentences = originalContent.split(/[。！？]/).filter(s => s.trim())
    const enhancedSentences = sentences.map(sentence => {
      const trimmed = sentence.trim()
      if (!trimmed) return ''
      
      // 添加一些连接词和修饰
      if (Math.random() > 0.7) {
        const connectors = ['那时候', '我记得', '当时', '后来', '接着']
        const connector = connectors[Math.floor(Math.random() * connectors.length)]
        return `${connector}，${trimmed}。`
      }
      return `${trimmed}。`
    })

    return `# ${chapterTitle}\n\n${enhancedSentences.join(' ')}\n\n*本章节内容已经过AI优化，保持了您的原始表达风格。*`
  }

  // 生成建议
  private static generateSuggestions(original: string): string[] {
    const suggestions = []
    
    if (original.length < 100) {
      suggestions.push('建议添加更多细节和具体的故事情节')
    }
    
    if (!original.includes('时间') && !original.includes('年') && !original.includes('岁')) {
      suggestions.push('可以添加具体的时间信息，让回忆更加生动')
    }
    
    if (!original.includes('地点') && !original.includes('在')) {
      suggestions.push('描述具体的地点和环境，增强故事的画面感')
    }
    
    suggestions.push('考虑添加当时的感受和情感体验')
    suggestions.push('可以补充相关人物的更多信息')
    
    return suggestions.slice(0, 3) // 限制建议数量
  }

  // 提取关键要点
  private static extractKeyPoints(content: string): string[] {
    const sentences = content.split(/[。！？]/).filter(s => s.trim() && s.length > 10)
    return sentences.slice(0, 5).map(s => s.trim().substring(0, 50) + (s.length > 50 ? '...' : ''))
  }

  // 生成整体摘要
  private static generateOverallSummary(contentBlocks: ContentBlock[]): string {
    const totalBlocks = contentBlocks.length
    const completedBlocks = contentBlocks.filter(b => b.status === 'completed').length
    const totalWords = contentBlocks.reduce((sum, block) => sum + block.enhancedContent.length, 0)

    return `您的回忆录包含 ${totalBlocks} 个章节，其中 ${completedBlocks} 个章节已完成内容创作。总计约 ${Math.floor(totalWords / 500)} 页内容。AI已对内容进行优化，保持了您的原始表达风格，同时增强了叙述的连贯性和可读性。`
  }

  // 生成整体建议
  private static generateOverallSuggestions(contentBlocks: ContentBlock[], template: MemoirTemplate): string[] {
    const suggestions = []
    const emptyChapters = template.chapters.length - contentBlocks.length
    
    if (emptyChapters > 0) {
      suggestions.push(`还有 ${emptyChapters} 个章节需要添加内容`)
    }
    
    const shortBlocks = contentBlocks.filter(b => b.originalContent.length < 200).length
    if (shortBlocks > 0) {
      suggestions.push(`有 ${shortBlocks} 个章节内容较少，建议补充更多细节`)
    }
    
    suggestions.push('考虑添加照片和相关文档来丰富回忆录')
    suggestions.push('可以邀请家人朋友补充相关回忆')
    suggestions.push('建议定期备份和保存您的回忆录内容')
    
    return suggestions.slice(0, 4)
  }

  // 计算完成百分比
  private static calculateCompletionPercentage(contentBlocks: ContentBlock[], template: MemoirTemplate): number {
    const totalChapters = template.chapters.length
    const completedChapters = contentBlocks.filter(b => b.originalContent.length > 100).length
    return Math.floor((completedChapters / totalChapters) * 100)
  }

  // 保存处理结果
  private static saveProcessedContent(result: ProcessingResult): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(result))
    } catch (error) {
      console.error('Failed to save processed content:', error)
    }
  }

  // 获取处理结果
  static getProcessedContent(): ProcessingResult | null {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      return stored ? JSON.parse(stored) : null
    } catch (error) {
      console.error('Failed to load processed content:', error)
      return null
    }
  }

  // 生成对比数据
  static generateComparisons(contentBlocks: ContentBlock[]): BeforeAfterComparison[] {
    return contentBlocks.map(block => ({
      blockId: block.id,
      chapterTitle: block.chapterTitle,
      original: block.originalContent,
      enhanced: block.enhancedContent,
      changes: this.analyzeChanges(block.originalContent, block.enhancedContent),
      userDecision: 'pending'
    }))
  }

  // 分析内容变化
  private static analyzeChanges(original: string, enhanced: string): BeforeAfterComparison['changes'] {
    const changes = []
    
    if (enhanced.length > original.length * 1.2) {
      changes.push({
        type: 'addition' as const,
        description: '添加了连接词和过渡语句，增强了内容的流畅性',
        position: 0
      })
    }
    
    if (enhanced.includes('*') && !original.includes('*')) {
      changes.push({
        type: 'addition' as const,
        description: '添加了章节标题和格式化',
        position: 0
      })
    }
    
    changes.push({
      type: 'improvement' as const,
      description: '优化了语言表达，保持了原始风格',
      position: Math.floor(enhanced.length / 2)
    })
    
    return changes
  }

  // 保存用户决定
  static saveUserDecision(blockId: string, decision: BeforeAfterComparison['userDecision'], modifications?: string): void {
    try {
      const comparisons = this.getComparisons()
      const comparison = comparisons.find(c => c.blockId === blockId)
      if (comparison) {
        comparison.userDecision = decision
        if (modifications) {
          comparison.userModifications = modifications
        }
        localStorage.setItem(this.COMPARISONS_KEY, JSON.stringify(comparisons))
      }
    } catch (error) {
      console.error('Failed to save user decision:', error)
    }
  }

  // 获取对比数据
  static getComparisons(): BeforeAfterComparison[] {
    try {
      const stored = localStorage.getItem(this.COMPARISONS_KEY)
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      console.error('Failed to load comparisons:', error)
      return []
    }
  }

  // 保存对比数据
  static saveComparisons(comparisons: BeforeAfterComparison[]): void {
    try {
      localStorage.setItem(this.COMPARISONS_KEY, JSON.stringify(comparisons))
    } catch (error) {
      console.error('Failed to save comparisons:', error)
    }
  }

  // 清理数据
  static clearProcessedData(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY)
      localStorage.removeItem(this.COMPARISONS_KEY)
    } catch (error) {
      console.error('Failed to clear processed data:', error)
    }
  }
}
