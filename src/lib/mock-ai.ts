// 模拟AI处理功能，用于演示和测试
export class MockAI {
  async processVoiceTranscript(transcript: string, context?: {
    chapterTitle?: string
    previousContent?: string
    memoirType?: string
  }): Promise<string> {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 简单的文本处理逻辑
    const processed = this.enhanceText(transcript, context)
    return processed
  }

  private enhanceText(text: string, context?: {
    chapterTitle?: string
    previousContent?: string
    memoirType?: string
  }): string {
    // 基本的文本清理和格式化
    let enhanced = text.trim()
    
    // 移除多余的空格
    enhanced = enhanced.replace(/\s+/g, ' ')
    
    // 添加适当的标点符号
    if (!enhanced.endsWith('。') && !enhanced.endsWith('！') && !enhanced.endsWith('？')) {
      enhanced += '。'
    }
    
    // 根据上下文添加一些润色
    if (context?.chapterTitle) {
      enhanced = `关于${context.chapterTitle}，${enhanced}`
    }
    
    // 添加一些常见的回忆录风格表达
    const enhancements = [
      '那时候，',
      '我记得，',
      '回想起来，',
      '现在想想，',
      '那段时光里，'
    ]
    
    // 随机选择一个开头（如果文本还没有这样的开头）
    const hasTimeExpression = enhancements.some(exp => enhanced.includes(exp))
    if (!hasTimeExpression && Math.random() > 0.5) {
      const randomEnhancement = enhancements[Math.floor(Math.random() * enhancements.length)]
      enhanced = randomEnhancement + enhanced.charAt(0).toLowerCase() + enhanced.slice(1)
    }
    
    // 分段处理（如果文本较长）
    if (enhanced.length > 100) {
      const sentences = enhanced.split(/[。！？]/).filter(s => s.trim())
      if (sentences.length > 2) {
        const midPoint = Math.floor(sentences.length / 2)
        const firstPart = sentences.slice(0, midPoint).join('。') + '。'
        const secondPart = sentences.slice(midPoint).join('。') + '。'
        enhanced = firstPart + '\n\n' + secondPart
      }
    }
    
    return enhanced
  }

  async generateChapterSuggestions(memoirType: string, existingChapters: string[]): Promise<string[]> {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const suggestions: Record<string, string[]> = {
      '人生历程': [
        '童年的记忆',
        '求学时光',
        '初入社会',
        '成家立业',
        '中年感悟',
        '退休生活',
        '人生智慧'
      ],
      '家族传承': [
        '祖辈的故事',
        '父母的教诲',
        '兄弟姐妹',
        '家族传统',
        '传家之宝',
        '家训家规',
        '后代寄语'
      ],
      '职业传奇': [
        '职业选择',
        '初入职场',
        '事业发展',
        '重要项目',
        '团队合作',
        '行业变迁',
        '职业感悟'
      ],
      '旅行回忆': [
        '第一次旅行',
        '难忘的风景',
        '异国文化',
        '旅途奇遇',
        '美食记忆',
        '旅伴故事',
        '旅行感悟'
      ]
    }
    
    const typeChapters = suggestions[memoirType] || suggestions['人生历程']
    return typeChapters.filter(chapter => !existingChapters.includes(chapter)).slice(0, 5)
  }

  async generateWritingPrompts(chapterTitle: string, memoirType: string): Promise<string[]> {
    await new Promise(resolve => setTimeout(resolve, 800))
    
    const prompts = [
      `关于" ${memoirType} - ${chapterTitle}"，您最深刻的记忆是什么？`,
      `在"${chapterTitle}"这个阶段，发生了什么重要的事情？`,
      `"${chapterTitle}"给您带来了什么样的感受和体验？`,
      `回想"${chapterTitle}"，有什么特别想要记录的细节吗？`,
      `"${chapterTitle}"对您的人生产生了什么影响？`
    ]
    
    return prompts
  }

  async summarizeContent(content: string, maxLength: number = 200): Promise<string> {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    if (content.length <= maxLength) {
      return content
    }
    
    // 简单的摘要逻辑：取前面的内容并确保在句号处截断
    let summary = content.substring(0, maxLength)
    const lastPeriod = summary.lastIndexOf('。')
    
    if (lastPeriod > maxLength * 0.7) {
      summary = summary.substring(0, lastPeriod + 1)
    } else {
      summary = summary.substring(0, maxLength - 3) + '...'
    }
    
    return summary
  }

  isConfigured(): boolean {
    return true // 模拟AI总是可用
  }
}

export default MockAI
