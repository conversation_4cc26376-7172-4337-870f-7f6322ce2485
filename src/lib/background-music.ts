export interface BackgroundMusic {
  id: string
  name: string
  description: string
  genre: 'warm' | 'nostalgic' | 'cheerful' | 'peaceful' | 'romantic' | 'family'
  duration: number // 秒
  url: string
  preview: string // 预览音频URL
  artist?: string
  isDefault: boolean
  isCustom: boolean
}

// 预设背景音乐列表
export const defaultBackgroundMusic: BackgroundMusic[] = [
  {
    id: 'warm-memories',
    name: '温暖回忆',
    description: '温馨的钢琴曲，适合家庭照片',
    genre: 'warm',
    duration: 180,
    url: 'https://www.soundjay.com/misc/sounds/warm-memories.mp3',
    preview: 'https://www.soundjay.com/misc/sounds/warm-memories-preview.mp3',
    artist: '音乐工作室',
    isDefault: true,
    isCustom: false
  },
  {
    id: 'golden-years',
    name: '黄金岁月',
    description: '怀旧的弦乐，唤起美好往昔',
    genre: 'nostalgic',
    duration: 210,
    url: 'https://www.soundjay.com/misc/sounds/golden-years.mp3',
    preview: 'https://www.soundjay.com/misc/sounds/golden-years-preview.mp3',
    artist: '经典音乐',
    isDefault: true,
    isCustom: false
  },
  {
    id: 'happy-moments',
    name: '快乐时光',
    description: '轻快的吉他曲，展现欢乐瞬间',
    genre: 'cheerful',
    duration: 165,
    url: 'https://www.soundjay.com/misc/sounds/happy-moments.mp3',
    preview: 'https://www.soundjay.com/misc/sounds/happy-moments-preview.mp3',
    artist: '阳光音乐',
    isDefault: true,
    isCustom: false
  },
  {
    id: 'peaceful-garden',
    name: '宁静花园',
    description: '舒缓的自然音乐，带来内心平静',
    genre: 'peaceful',
    duration: 240,
    url: 'https://www.soundjay.com/misc/sounds/peaceful-garden.mp3',
    preview: 'https://www.soundjay.com/misc/sounds/peaceful-garden-preview.mp3',
    artist: '自然之声',
    isDefault: true,
    isCustom: false
  },
  {
    id: 'love-story',
    name: '爱的故事',
    description: '浪漫的小提琴曲，诉说爱情故事',
    genre: 'romantic',
    duration: 195,
    url: 'https://www.soundjay.com/misc/sounds/love-story.mp3',
    preview: 'https://www.soundjay.com/misc/sounds/love-story-preview.mp3',
    artist: '浪漫音乐',
    isDefault: true,
    isCustom: false
  },
  {
    id: 'family-bond',
    name: '家的温暖',
    description: '温馨的合奏，表达家庭的温暖',
    genre: 'family',
    duration: 220,
    url: 'https://www.soundjay.com/misc/sounds/family-bond.mp3',
    preview: 'https://www.soundjay.com/misc/sounds/family-bond-preview.mp3',
    artist: '家庭音乐',
    isDefault: true,
    isCustom: false
  },
  {
    id: 'childhood-dreams',
    name: '童年梦想',
    description: '纯真的音乐盒旋律，回忆童年时光',
    genre: 'nostalgic',
    duration: 155,
    url: 'https://www.soundjay.com/misc/sounds/childhood-dreams.mp3',
    preview: 'https://www.soundjay.com/misc/sounds/childhood-dreams-preview.mp3',
    artist: '童真音乐',
    isDefault: true,
    isCustom: false
  },
  {
    id: 'sunset-memories',
    name: '夕阳回忆',
    description: '温柔的萨克斯，如夕阳般温暖',
    genre: 'warm',
    duration: 185,
    url: 'https://www.soundjay.com/misc/sounds/sunset-memories.mp3',
    preview: 'https://www.soundjay.com/misc/sounds/sunset-memories-preview.mp3',
    artist: '黄昏音乐',
    isDefault: true,
    isCustom: false
  }
]

// 音乐管理类
export class BackgroundMusicManager {
  private static readonly STORAGE_KEY = 'album_background_music'
  private static readonly CUSTOM_MUSIC_KEY = 'custom_background_music'

  // 获取所有可用的背景音乐
  static getAllMusic(): BackgroundMusic[] {
    const customMusic = this.getCustomMusic()
    return [...defaultBackgroundMusic, ...customMusic]
  }

  // 获取自定义音乐
  static getCustomMusic(): BackgroundMusic[] {
    try {
      const stored = localStorage.getItem(this.CUSTOM_MUSIC_KEY)
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      console.error('Failed to load custom music:', error)
      return []
    }
  }

  // 添加自定义音乐
  static async addCustomMusic(file: File): Promise<BackgroundMusic> {
    return new Promise((resolve, reject) => {
      // 验证文件类型
      if (!file.type.startsWith('audio/')) {
        reject(new Error('请选择音频文件'))
        return
      }

      // 验证文件大小 (最大10MB)
      const maxSize = 10 * 1024 * 1024
      if (file.size > maxSize) {
        reject(new Error('文件大小不能超过10MB'))
        return
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const audioUrl = e.target?.result as string
          const customMusic: BackgroundMusic = {
            id: `custom-${Date.now()}`,
            name: file.name.replace(/\.[^/.]+$/, ''), // 移除文件扩展名
            description: '自定义音乐',
            genre: 'family',
            duration: 0, // 需要通过Audio对象获取实际时长
            url: audioUrl,
            preview: audioUrl,
            isDefault: false,
            isCustom: true
          }

          // 获取音频时长
          const audio = new Audio(audioUrl)
          audio.addEventListener('loadedmetadata', () => {
            customMusic.duration = Math.floor(audio.duration)
            this.saveCustomMusic(customMusic)
            resolve(customMusic)
          })

          audio.addEventListener('error', () => {
            reject(new Error('无法加载音频文件'))
          })

        } catch {
          reject(new Error('文件处理失败'))
        }
      }

      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }

      reader.readAsDataURL(file)
    })
  }

  // 保存自定义音乐
  private static saveCustomMusic(music: BackgroundMusic): void {
    try {
      const customMusic = this.getCustomMusic()
      customMusic.push(music)
      localStorage.setItem(this.CUSTOM_MUSIC_KEY, JSON.stringify(customMusic))
    } catch (error) {
      console.error('Failed to save custom music:', error)
    }
  }

  // 删除自定义音乐
  static deleteCustomMusic(musicId: string): void {
    try {
      const customMusic = this.getCustomMusic()
      const filtered = customMusic.filter(music => music.id !== musicId)
      localStorage.setItem(this.CUSTOM_MUSIC_KEY, JSON.stringify(filtered))
    } catch (error) {
      console.error('Failed to delete custom music:', error)
    }
  }

  // 根据类型筛选音乐
  static getMusicByGenre(genre: BackgroundMusic['genre']): BackgroundMusic[] {
    return this.getAllMusic().filter(music => music.genre === genre)
  }

  // 获取音乐详情
  static getMusicById(id: string): BackgroundMusic | undefined {
    return this.getAllMusic().find(music => music.id === id)
  }

  // 格式化时长显示
  static formatDuration(seconds: number): string {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  // 获取类型标签
  static getGenreLabel(genre: BackgroundMusic['genre']): string {
    const labels = {
      warm: '温馨',
      nostalgic: '怀旧',
      cheerful: '欢快',
      peaceful: '宁静',
      romantic: '浪漫',
      family: '家庭'
    }
    return labels[genre] || '其他'
  }

  // 获取类型颜色
  static getGenreColor(genre: BackgroundMusic['genre']): string {
    const colors = {
      warm: 'bg-orange-100 text-orange-800',
      nostalgic: 'bg-amber-100 text-amber-800',
      cheerful: 'bg-yellow-100 text-yellow-800',
      peaceful: 'bg-green-100 text-green-800',
      romantic: 'bg-pink-100 text-pink-800',
      family: 'bg-blue-100 text-blue-800'
    }
    return colors[genre] || 'bg-gray-100 text-gray-800'
  }
}

// 音频播放器管理
export class AudioPlayerManager {
  private static currentAudio: HTMLAudioElement | null = null
  private static currentMusicId: string | null = null

  // 播放音乐
  static async playMusic(music: BackgroundMusic, volume: number = 0.5): Promise<void> {
    try {
      // 停止当前播放的音乐
      this.stopMusic()

      const audio = new Audio(music.url)
      audio.volume = volume
      audio.loop = true

      await audio.play()
      
      this.currentAudio = audio
      this.currentMusicId = music.id
    } catch (error) {
      console.error('Failed to play music:', error)
      throw new Error('音乐播放失败')
    }
  }

  // 停止音乐
  static stopMusic(): void {
    if (this.currentAudio) {
      this.currentAudio.pause()
      this.currentAudio.currentTime = 0
      this.currentAudio = null
      this.currentMusicId = null
    }
  }

  // 暂停音乐
  static pauseMusic(): void {
    if (this.currentAudio) {
      this.currentAudio.pause()
    }
  }

  // 恢复播放
  static resumeMusic(): void {
    if (this.currentAudio) {
      this.currentAudio.play().catch(console.error)
    }
  }

  // 设置音量
  static setVolume(volume: number): void {
    if (this.currentAudio) {
      this.currentAudio.volume = Math.max(0, Math.min(1, volume))
    }
  }

  // 获取当前播放状态
  static getPlaybackState(): {
    isPlaying: boolean
    currentMusicId: string | null
    volume: number
  } {
    return {
      isPlaying: this.currentAudio ? !this.currentAudio.paused : false,
      currentMusicId: this.currentMusicId,
      volume: this.currentAudio ? this.currentAudio.volume : 0.5
    }
  }
}
