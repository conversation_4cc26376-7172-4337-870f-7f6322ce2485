// 导出服务
export interface ExportOptions {
  readonly format: 'pdf' | 'html' | 'docx' | 'txt' | 'markdown'
  readonly includeImages?: boolean
  readonly includeAudio?: boolean
  readonly includeMetadata?: boolean
  readonly template?: 'default' | 'classic' | 'modern' | 'elegant'
  readonly pageSize?: 'A4' | 'A5' | 'Letter'
  readonly fontSize?: 'small' | 'medium' | 'large'
  readonly margins?: 'narrow' | 'normal' | 'wide'
  readonly coverPage?: boolean
  readonly tableOfContents?: boolean
  readonly watermark?: string
}

export interface ExportData {
  readonly title: string
  readonly author: string
  readonly content: string
  readonly chapters?: ChapterData[]
  readonly photos?: PhotoData[]
  readonly metadata?: {
    readonly createdAt: string
    readonly updatedAt: string
    readonly wordCount: number
    readonly readingTime: number
    readonly tags?: string[]
  }
}

export interface ChapterData {
  readonly id: string
  readonly title: string
  readonly content: string
  readonly order: number
  readonly wordCount: number
  readonly photos?: PhotoData[]
  readonly audioUrl?: string
}

export interface PhotoData {
  readonly id: string
  readonly url: string
  readonly caption?: string
  readonly description?: string
  readonly audioNarration?: string
}

export interface ExportResult {
  readonly success: boolean
  readonly data?: Blob | string
  readonly filename: string
  readonly mimeType: string
  readonly error?: string
}

export class ExportService {
  private static readonly DEFAULT_OPTIONS: Required<Omit<ExportOptions, 'watermark'>> = {
    format: 'pdf',
    includeImages: true,
    includeAudio: false,
    includeMetadata: true,
    template: 'default',
    pageSize: 'A4',
    fontSize: 'medium',
    margins: 'normal',
    coverPage: true,
    tableOfContents: true
  }

  // 导出内容
  static async exportContent(
    data: ExportData,
    options: Partial<ExportOptions> = {}
  ): Promise<ExportResult> {
    const opts = { ...this.DEFAULT_OPTIONS, ...options }

    try {
      switch (opts.format) {
        case 'pdf':
          return await this.exportToPDF(data, opts)
        case 'html':
          return this.exportToHTML(data, opts)
        case 'docx':
          return this.exportToDocx(data, opts)
        case 'txt':
          return this.exportToText(data, opts)
        case 'markdown':
          return this.exportToMarkdown(data, opts)
        default:
          throw new Error(`不支持的导出格式: ${opts.format}`)
      }
    } catch (error) {
      return {
        success: false,
        filename: '',
        mimeType: '',
        error: error instanceof Error ? error.message : '导出失败'
      }
    }
  }

  // 导出为PDF
  private static async exportToPDF(
    data: ExportData,
    options: Required<Omit<ExportOptions, 'watermark'>> & { watermark?: string }
  ): Promise<ExportResult> {
    // 使用浏览器的打印功能生成PDF
    const htmlContent = this.generateHTMLContent(data, options)
    
    // 创建临时窗口用于打印
    const printWindow = window.open('', '_blank')
    if (!printWindow) {
      throw new Error('无法打开打印窗口，请检查浏览器设置')
    }

    printWindow.document.write(htmlContent)
    printWindow.document.close()

    // 等待内容加载完成
    await new Promise(resolve => {
      printWindow.onload = resolve
      setTimeout(resolve, 1000) // 备用超时
    })

    // 触发打印对话框
    printWindow.print()
    
    // 注意：实际的PDF生成需要用户手动选择"保存为PDF"
    // 这里返回HTML内容作为备选
    const blob = new Blob([htmlContent], { type: 'text/html' })
    
    return {
      success: true,
      data: blob,
      filename: `${data.title || '回忆录'}.html`,
      mimeType: 'text/html'
    }
  }

  // 导出为HTML
  private static exportToHTML(
    data: ExportData,
    options: Required<Omit<ExportOptions, 'watermark'>> & { watermark?: string }
  ): ExportResult {
    const htmlContent = this.generateHTMLContent(data, options)
    const blob = new Blob([htmlContent], { type: 'text/html' })

    return {
      success: true,
      data: blob,
      filename: `${data.title || '回忆录'}.html`,
      mimeType: 'text/html'
    }
  }

  // 导出为文本
  private static exportToText(data: ExportData, options: Required<Omit<ExportOptions, 'watermark'>>): ExportResult {
    let content = ''

    // 标题
    if (data.title) {
      content += `${data.title}\n`
      content += '='.repeat(data.title.length) + '\n\n'
    }

    // 作者
    if (data.author) {
      content += `作者：${data.author}\n\n`
    }

    // 元数据
    if (options.includeMetadata && data.metadata) {
      content += '--- 文档信息 ---\n'
      content += `创建时间：${new Date(data.metadata.createdAt).toLocaleString('zh-CN')}\n`
      content += `更新时间：${new Date(data.metadata.updatedAt).toLocaleString('zh-CN')}\n`
      content += `字数：${data.metadata.wordCount}\n`
      content += `阅读时间：${data.metadata.readingTime} 分钟\n`
      if (data.metadata.tags && data.metadata.tags.length > 0) {
        content += `标签：${data.metadata.tags.join(', ')}\n`
      }
      content += '\n'
    }

    // 目录
    if (options.tableOfContents && data.chapters && data.chapters.length > 0) {
      content += '--- 目录 ---\n'
      data.chapters.forEach((chapter, index) => {
        content += `${index + 1}. ${chapter.title}\n`
      })
      content += '\n'
    }

    // 主要内容
    if (data.content) {
      content += data.content + '\n\n'
    }

    // 章节内容
    if (data.chapters && data.chapters.length > 0) {
      data.chapters.forEach((chapter, index) => {
        content += `第${index + 1}章 ${chapter.title}\n`
        content += '-'.repeat(chapter.title.length + 10) + '\n\n'
        content += chapter.content + '\n\n'
      })
    }

    const blob = new Blob([content], { type: 'text/plain; charset=utf-8' })

    return {
      success: true,
      data: blob,
      filename: `${data.title || '回忆录'}.txt`,
      mimeType: 'text/plain'
    }
  }

  // 导出为Markdown
  private static exportToMarkdown(data: ExportData, options: Required<Omit<ExportOptions, 'watermark'>>): ExportResult {
    let content = ''

    // 标题
    if (data.title) {
      content += `# ${data.title}\n\n`
    }

    // 作者
    if (data.author) {
      content += `**作者：** ${data.author}\n\n`
    }

    // 元数据
    if (options.includeMetadata && data.metadata) {
      content += '## 文档信息\n\n'
      content += `- **创建时间：** ${new Date(data.metadata.createdAt).toLocaleString('zh-CN')}\n`
      content += `- **更新时间：** ${new Date(data.metadata.updatedAt).toLocaleString('zh-CN')}\n`
      content += `- **字数：** ${data.metadata.wordCount}\n`
      content += `- **阅读时间：** ${data.metadata.readingTime} 分钟\n`
      if (data.metadata.tags && data.metadata.tags.length > 0) {
        content += `- **标签：** ${data.metadata.tags.join(', ')}\n`
      }
      content += '\n'
    }

    // 目录
    if (options.tableOfContents && data.chapters && data.chapters.length > 0) {
      content += '## 目录\n\n'
      data.chapters.forEach((chapter, index) => {
        content += `${index + 1}. [${chapter.title}](#第${index + 1}章-${chapter.title.replace(/\s+/g, '-').toLowerCase()})\n`
      })
      content += '\n'
    }

    // 主要内容
    if (data.content) {
      content += '## 内容\n\n'
      content += data.content + '\n\n'
    }

    // 章节内容
    if (data.chapters && data.chapters.length > 0) {
      data.chapters.forEach((chapter, index) => {
        content += `## 第${index + 1}章 ${chapter.title}\n\n`
        content += chapter.content + '\n\n'
        
        // 章节照片
        if (options.includeImages && chapter.photos && chapter.photos.length > 0) {
          content += '### 相关照片\n\n'
          chapter.photos.forEach(photo => {
            content += `![${photo.caption || '照片'}](${photo.url})\n`
            if (photo.caption) {
              content += `*${photo.caption}*\n`
            }
            if (photo.description) {
              content += `\n${photo.description}\n`
            }
            content += '\n'
          })
        }
      })
    }

    const blob = new Blob([content], { type: 'text/markdown; charset=utf-8' })

    return {
      success: true,
      data: blob,
      filename: `${data.title || '回忆录'}.md`,
      mimeType: 'text/markdown'
    }
  }

  // 导出为DOCX（简化版本）
  private static exportToDocx(data: ExportData, options: Required<Omit<ExportOptions, 'watermark'>>): ExportResult {
    // 简化实现：生成HTML并提示用户在Word中打开
    const htmlContent = this.generateHTMLContent(data, options)
    const blob = new Blob([htmlContent], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })

    return {
      success: true,
      data: blob,
      filename: `${data.title || '回忆录'}.docx`,
      mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    }
  }

  // 生成HTML内容
  private static generateHTMLContent(
    data: ExportData,
    options: Required<Omit<ExportOptions, 'watermark'>> & { watermark?: string }
  ): string {
    const styles = this.getTemplateStyles(options.template, options.fontSize, options.margins)
    
    let html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${data.title || '回忆录'}</title>
    <style>${styles}</style>
</head>
<body>
`

    // 封面页
    if (options.coverPage) {
      html += `
    <div class="cover-page">
        <h1 class="cover-title">${data.title || '回忆录'}</h1>
        <p class="cover-author">作者：${data.author}</p>
        ${data.metadata ? `<p class="cover-date">${new Date(data.metadata.createdAt).toLocaleDateString('zh-CN')}</p>` : ''}
    </div>
    <div class="page-break"></div>
`
    }

    // 目录
    if (options.tableOfContents && data.chapters && data.chapters.length > 0) {
      html += `
    <div class="toc">
        <h2>目录</h2>
        <ul>
`
      data.chapters.forEach((chapter, index) => {
        html += `            <li><a href="#chapter-${index + 1}">${index + 1}. ${chapter.title}</a></li>\n`
      })
      html += `
        </ul>
    </div>
    <div class="page-break"></div>
`
    }

    // 主要内容
    if (data.content) {
      html += `
    <div class="main-content">
        <div class="content">${data.content.replace(/\n/g, '<br>')}</div>
    </div>
`
    }

    // 章节内容
    if (data.chapters && data.chapters.length > 0) {
      data.chapters.forEach((chapter, index) => {
        html += `
    <div class="chapter" id="chapter-${index + 1}">
        <h2 class="chapter-title">第${index + 1}章 ${chapter.title}</h2>
        <div class="chapter-content">${chapter.content.replace(/\n/g, '<br>')}</div>
        
        ${options.includeImages && chapter.photos && chapter.photos.length > 0 ? `
        <div class="chapter-photos">
            <h3>相关照片</h3>
            ${chapter.photos.map(photo => `
            <div class="photo">
                <img src="${photo.url}" alt="${photo.caption || '照片'}" />
                ${photo.caption ? `<p class="photo-caption">${photo.caption}</p>` : ''}
                ${photo.description ? `<p class="photo-description">${photo.description}</p>` : ''}
            </div>
            `).join('')}
        </div>
        ` : ''}
    </div>
`
      })
    }

    // 水印
    if (options.watermark) {
      html += `
    <div class="watermark">${options.watermark}</div>
`
    }

    html += `
</body>
</html>
`

    return html
  }

  // 获取模板样式
  private static getTemplateStyles(
    template: string,
    fontSize: string,
    margins: string
  ): string {
    const fontSizes = {
      small: '14px',
      medium: '16px',
      large: '18px'
    }

    const marginSizes = {
      narrow: '1cm',
      normal: '2cm',
      wide: '3cm'
    }

    const baseStyles = `
        @page {
            size: A4;
            margin: ${marginSizes[margins as keyof typeof marginSizes]};
        }
        
        body {
            font-family: 'Times New Roman', serif;
            font-size: ${fontSizes[fontSize as keyof typeof fontSizes]};
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        .cover-page {
            text-align: center;
            padding-top: 30%;
        }
        
        .cover-title {
            font-size: 2.5em;
            margin-bottom: 1em;
            font-weight: bold;
        }
        
        .cover-author {
            font-size: 1.2em;
            margin-bottom: 0.5em;
        }
        
        .cover-date {
            font-size: 1em;
            color: #666;
        }
        
        .toc {
            padding: 2em 0;
        }
        
        .toc h2 {
            border-bottom: 2px solid #333;
            padding-bottom: 0.5em;
        }
        
        .toc ul {
            list-style: none;
            padding: 0;
        }
        
        .toc li {
            margin: 0.5em 0;
            padding-left: 1em;
        }
        
        .chapter {
            margin: 2em 0;
        }
        
        .chapter-title {
            font-size: 1.5em;
            margin-bottom: 1em;
            border-bottom: 1px solid #ccc;
            padding-bottom: 0.5em;
        }
        
        .chapter-content {
            text-align: justify;
            margin-bottom: 2em;
        }
        
        .photo {
            margin: 1em 0;
            text-align: center;
        }
        
        .photo img {
            max-width: 100%;
            height: auto;
        }
        
        .photo-caption {
            font-weight: bold;
            margin: 0.5em 0;
        }
        
        .photo-description {
            font-style: italic;
            color: #666;
        }
        
        .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 3em;
            color: rgba(0, 0, 0, 0.1);
            z-index: -1;
            pointer-events: none;
        }
    `

    // 模板特定样式
    const templateStyles = {
      default: '',
      classic: `
        body { font-family: 'Georgia', serif; }
        .cover-page { background: #f9f7f4; }
      `,
      modern: `
        body { font-family: 'Arial', sans-serif; }
        .chapter-title { color: #2563eb; }
      `,
      elegant: `
        body { font-family: 'Crimson Text', serif; }
        .cover-title { font-style: italic; }
      `
    }

    return baseStyles + (templateStyles[template as keyof typeof templateStyles] || '')
  }

  // 下载文件
  static downloadFile(result: ExportResult): void {
    if (!result.success || !result.data) {
      throw new Error(result.error || '导出失败')
    }

    const url = URL.createObjectURL(result.data as Blob)
    const a = document.createElement('a')
    a.href = url
    a.download = result.filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
}
