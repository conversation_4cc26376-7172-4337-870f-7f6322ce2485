// 中国大陆手机号验证
export function validateChinesePhoneNumber(phone: string): boolean {
  // 移除所有空格和特殊字符
  const cleanPhone = phone.replace(/[\s-()]/g, '')
  
  // 中国大陆手机号正则表达式
  // 支持 13x, 14x, 15x, 16x, 17x, 18x, 19x 开头的11位数字
  const phoneRegex = /^1[3-9]\d{9}$/
  
  return phoneRegex.test(cleanPhone)
}

// 格式化手机号显示（添加空格分隔）
export function formatPhoneNumber(phone: string): string {
  const cleanPhone = phone.replace(/[\s-()]/g, '')
  
  if (cleanPhone.length <= 3) {
    return cleanPhone
  } else if (cleanPhone.length <= 7) {
    return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(3)}`
  } else {
    return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(3, 7)} ${cleanPhone.slice(7, 11)}`
  }
}

// 获取清理后的手机号（仅数字）
export function getCleanPhoneNumber(phone: string): string {
  return phone.replace(/[\s-()]/g, '')
}

// 手机号脱敏显示
export function maskPhoneNumber(phone: string): string {
  const cleanPhone = getCleanPhoneNumber(phone)
  if (cleanPhone.length !== 11) {
    return phone
  }
  
  return `${cleanPhone.slice(0, 3)}****${cleanPhone.slice(7)}`
}

// 验证码相关
export interface VerificationCodeState {
  sent: boolean
  countdown: number
  canResend: boolean
}

export class VerificationCodeManager {
  private countdownInterval: NodeJS.Timeout | null = null
  private onStateChange: (state: VerificationCodeState) => void
  
  constructor(onStateChange: (state: VerificationCodeState) => void) {
    this.onStateChange = onStateChange
  }
  
  startCountdown(seconds: number = 60): void {
    let remaining = seconds
    
    this.onStateChange({
      sent: true,
      countdown: remaining,
      canResend: false
    })
    
    this.countdownInterval = setInterval(() => {
      remaining -= 1
      
      if (remaining <= 0) {
        this.stopCountdown()
        this.onStateChange({
          sent: true,
          countdown: 0,
          canResend: true
        })
      } else {
        this.onStateChange({
          sent: true,
          countdown: remaining,
          canResend: false
        })
      }
    }, 1000)
  }
  
  stopCountdown(): void {
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval)
      this.countdownInterval = null
    }
  }
  
  reset(): void {
    this.stopCountdown()
    this.onStateChange({
      sent: false,
      countdown: 0,
      canResend: true
    })
  }
  
  destroy(): void {
    this.stopCountdown()
  }
}
