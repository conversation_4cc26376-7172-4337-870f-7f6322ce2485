import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Validate URL format
const isValidUrl = (url: string | undefined): boolean => {
  if (!url) return false
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// Create a mock client for development when env vars are not set
const createMockClient = () => ({
  auth: {
    getSession: () => Promise.resolve({ data: { session: null }, error: null }),
    onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } }),
    signInWithPassword: () => Promise.resolve({ data: null, error: { message: 'Please configure Supabase' } }),
    signUp: () => Promise.resolve({ data: null, error: { message: 'Please configure Supabase' } }),
    signOut: () => Promise.resolve({ error: null }),
  },
  from: () => ({
    select: () => Promise.resolve({ data: [], error: null }),
    insert: () => Promise.resolve({ data: null, error: null }),
    update: () => Promise.resolve({ data: null, error: null }),
    delete: () => Promise.resolve({ data: null, error: null }),
  }),
})

export const supabase = (isValidUrl(supabaseUrl) && supabaseAnonKey)
  ? createClient(supabaseUrl!, supabaseAnonKey)
  : createMockClient() as unknown as ReturnType<typeof createClient>

export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string | null
          phone: string | null
          full_name: string | null
          avatar_url: string | null
          auth_method: 'email' | 'phone' | 'wechat' | 'guest'
          wechat_openid: string | null
          wechat_unionid: string | null
          wechat_nickname: string | null
          device_fingerprint: string | null
          guest_expires_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email?: string | null
          phone?: string | null
          full_name?: string | null
          avatar_url?: string | null
          auth_method?: 'email' | 'phone' | 'wechat' | 'guest'
          wechat_openid?: string | null
          wechat_unionid?: string | null
          wechat_nickname?: string | null
          device_fingerprint?: string | null
          guest_expires_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string | null
          phone?: string | null
          full_name?: string | null
          avatar_url?: string | null
          auth_method?: 'email' | 'phone' | 'wechat' | 'guest'
          wechat_openid?: string | null
          wechat_unionid?: string | null
          wechat_nickname?: string | null
          device_fingerprint?: string | null
          guest_expires_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      memoirs: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          template_type: string
          status: 'draft' | 'published' | 'archived'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          template_type: string
          status?: 'draft' | 'published' | 'archived'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          template_type?: string
          status?: 'draft' | 'published' | 'archived'
          created_at?: string
          updated_at?: string
        }
      }
      chapters: {
        Row: {
          id: string
          memoir_id: string
          title: string
          description: string | null
          order_index: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          memoir_id: string
          title: string
          description?: string | null
          order_index: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          memoir_id?: string
          title?: string
          description?: string | null
          order_index?: number
          created_at?: string
          updated_at?: string
        }
      }
      content_blocks: {
        Row: {
          id: string
          chapter_id: string
          content: string
          content_type: 'text' | 'voice_transcription' | 'ai_processed'
          order_index: number
          voice_recording_url: string | null
          ai_processed: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          chapter_id: string
          content: string
          content_type: 'text' | 'voice_transcription' | 'ai_processed'
          order_index: number
          voice_recording_url?: string | null
          ai_processed?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          chapter_id?: string
          content?: string
          content_type?: 'text' | 'voice_transcription' | 'ai_processed'
          order_index?: number
          voice_recording_url?: string | null
          ai_processed?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      photo_albums: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          template_type: string
          status: 'draft' | 'published' | 'archived'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          template_type: string
          status?: 'draft' | 'published' | 'archived'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          template_type?: string
          status?: 'draft' | 'published' | 'archived'
          created_at?: string
          updated_at?: string
        }
      }
      album_photos: {
        Row: {
          id: string
          album_id: string
          photo_url: string
          caption: string | null
          voice_narration_url: string | null
          order_index: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          album_id: string
          photo_url: string
          caption?: string | null
          voice_narration_url?: string | null
          order_index: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          album_id?: string
          photo_url?: string
          caption?: string | null
          voice_narration_url?: string | null
          order_index?: number
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
