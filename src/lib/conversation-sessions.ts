export interface ConversationMessage {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
  audioUrl?: string
  transcription?: string
}

export interface ConversationSession {
  id: string
  title: string
  chapterTitle: string
  chapterIndex: number
  promptIndex: number
  startTime: Date
  endTime?: Date
  duration: number // in seconds
  status: 'ongoing' | 'completed' | 'paused'
  messages: ConversationMessage[]
  summary: string
  keyPoints: string[]
  completionPercentage: number
  templateId: string
}

export interface ConversationSummary {
  sessionId: string
  title: string
  chapterTitle: string
  date: Date
  duration: number
  status: 'ongoing' | 'completed' | 'paused'
  summary: string
  keyPoints: string[]
  messageCount: number
  completionPercentage: number
  canResume: boolean
}

export class ConversationSessionManager {
  private static readonly STORAGE_KEY = 'memoir_conversation_sessions'
  private static readonly MAX_SESSIONS = 50 // 限制存储的会话数量

  // 获取所有会话
  static getAllSessions(): ConversationSession[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (!stored) return []
      
      const sessions = JSON.parse(stored) as ConversationSession[]
      // 转换日期字符串为Date对象
      return sessions.map(session => ({
        ...session,
        startTime: new Date(session.startTime),
        endTime: session.endTime ? new Date(session.endTime) : undefined,
        messages: session.messages.map(msg => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }))
      }))
    } catch (error) {
      console.error('Failed to load conversation sessions:', error)
      return []
    }
  }

  // 保存会话
  static saveSession(session: ConversationSession): void {
    try {
      const sessions = this.getAllSessions()
      const existingIndex = sessions.findIndex(s => s.id === session.id)
      
      if (existingIndex >= 0) {
        sessions[existingIndex] = session
      } else {
        sessions.unshift(session) // 新会话添加到开头
        
        // 限制存储数量
        if (sessions.length > this.MAX_SESSIONS) {
          sessions.splice(this.MAX_SESSIONS)
        }
      }
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(sessions))
    } catch (error) {
      console.error('Failed to save conversation session:', error)
    }
  }

  // 获取单个会话
  static getSession(sessionId: string): ConversationSession | null {
    const sessions = this.getAllSessions()
    return sessions.find(s => s.id === sessionId) || null
  }

  // 删除会话
  static deleteSession(sessionId: string): void {
    try {
      const sessions = this.getAllSessions()
      const filteredSessions = sessions.filter(s => s.id !== sessionId)
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredSessions))
    } catch (error) {
      console.error('Failed to delete conversation session:', error)
    }
  }

  // 创建新会话
  static createSession(
    templateId: string,
    chapterTitle: string,
    chapterIndex: number,
    promptIndex: number
  ): ConversationSession {
    const session: ConversationSession = {
      id: `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      title: `${chapterTitle} - ${new Date().toLocaleDateString()}`,
      chapterTitle,
      chapterIndex,
      promptIndex,
      startTime: new Date(),
      duration: 0,
      status: 'ongoing',
      messages: [],
      summary: '',
      keyPoints: [],
      completionPercentage: 0,
      templateId
    }
    
    this.saveSession(session)
    return session
  }

  // 更新会话
  static updateSession(sessionId: string, updates: Partial<ConversationSession>): void {
    const session = this.getSession(sessionId)
    if (session) {
      const updatedSession = { ...session, ...updates }
      this.saveSession(updatedSession)
    }
  }

  // 添加消息到会话
  static addMessageToSession(sessionId: string, message: ConversationMessage): void {
    const session = this.getSession(sessionId)
    if (session) {
      session.messages.push(message)
      session.duration = Math.floor((Date.now() - session.startTime.getTime()) / 1000)
      
      // 更新完成百分比（基于消息数量的简单计算）
      session.completionPercentage = Math.min(100, (session.messages.length / 10) * 100)
      
      this.saveSession(session)
    }
  }

  // 完成会话
  static completeSession(sessionId: string, summary?: string, keyPoints?: string[]): void {
    const session = this.getSession(sessionId)
    if (session) {
      session.status = 'completed'
      session.endTime = new Date()
      session.duration = Math.floor((session.endTime.getTime() - session.startTime.getTime()) / 1000)
      session.completionPercentage = 100
      
      if (summary) session.summary = summary
      if (keyPoints) session.keyPoints = keyPoints
      
      this.saveSession(session)
    }
  }

  // 暂停会话
  static pauseSession(sessionId: string): void {
    this.updateSession(sessionId, { status: 'paused' })
  }

  // 恢复会话
  static resumeSession(sessionId: string): void {
    this.updateSession(sessionId, { status: 'ongoing' })
  }

  // 获取会话摘要列表（用于时间线显示）
  static getSessionSummaries(): ConversationSummary[] {
    const sessions = this.getAllSessions()
    
    return sessions.map(session => ({
      sessionId: session.id,
      title: session.title,
      chapterTitle: session.chapterTitle,
      date: session.startTime,
      duration: session.duration,
      status: session.status,
      summary: session.summary || this.generateAutoSummary(session),
      keyPoints: session.keyPoints,
      messageCount: session.messages.length,
      completionPercentage: session.completionPercentage,
      canResume: session.status !== 'completed'
    })).sort((a, b) => b.date.getTime() - a.date.getTime()) // 按时间倒序
  }

  // 自动生成摘要
  private static generateAutoSummary(session: ConversationSession): string {
    if (session.messages.length === 0) {
      return '尚未开始对话'
    }
    
    const userMessages = session.messages.filter(m => m.type === 'user')
    if (userMessages.length === 0) {
      return '等待用户回应'
    }
    
    // 取前几条用户消息的开头作为摘要
    const firstUserMessage = userMessages[0].content
    const summary = firstUserMessage.length > 50 
      ? firstUserMessage.substring(0, 50) + '...'
      : firstUserMessage
      
    return summary || '开始了对话'
  }

  // 搜索会话
  static searchSessions(query: string): ConversationSummary[] {
    const summaries = this.getSessionSummaries()
    const lowerQuery = query.toLowerCase()
    
    return summaries.filter(summary => 
      summary.title.toLowerCase().includes(lowerQuery) ||
      summary.chapterTitle.toLowerCase().includes(lowerQuery) ||
      summary.summary.toLowerCase().includes(lowerQuery) ||
      summary.keyPoints.some(point => point.toLowerCase().includes(lowerQuery))
    )
  }

  // 按章节筛选会话
  static getSessionsByChapter(chapterTitle: string): ConversationSummary[] {
    const summaries = this.getSessionSummaries()
    return summaries.filter(summary => summary.chapterTitle === chapterTitle)
  }

  // 按状态筛选会话
  static getSessionsByStatus(status: ConversationSession['status']): ConversationSummary[] {
    const summaries = this.getSessionSummaries()
    return summaries.filter(summary => summary.status === status)
  }

  // 获取统计信息
  static getStatistics(): {
    totalSessions: number
    completedSessions: number
    ongoingSessions: number
    pausedSessions: number
    totalDuration: number
    averageDuration: number
    totalMessages: number
  } {
    const sessions = this.getAllSessions()
    
    const completed = sessions.filter(s => s.status === 'completed').length
    const ongoing = sessions.filter(s => s.status === 'ongoing').length
    const paused = sessions.filter(s => s.status === 'paused').length
    const totalDuration = sessions.reduce((sum, s) => sum + s.duration, 0)
    const totalMessages = sessions.reduce((sum, s) => sum + s.messages.length, 0)
    
    return {
      totalSessions: sessions.length,
      completedSessions: completed,
      ongoingSessions: ongoing,
      pausedSessions: paused,
      totalDuration,
      averageDuration: sessions.length > 0 ? totalDuration / sessions.length : 0,
      totalMessages
    }
  }

  // 清理旧会话（保留最近的会话）
  static cleanupOldSessions(keepCount: number = 20): void {
    const sessions = this.getAllSessions()
    if (sessions.length > keepCount) {
      const recentSessions = sessions
        .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
        .slice(0, keepCount)
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(recentSessions))
    }
  }
}
