// 访客认证相关功能
export interface GuestUser {
  id: string
  deviceFingerprint: string
  createdAt: Date
  expiresAt: Date
  isExpired: boolean
}

export interface GuestLimits {
  maxMemoirs: number
  maxChaptersPerMemoir: number
  maxContentBlocksPerChapter: number
  validityDays: number
}

// 默认访客限制
export const DEFAULT_GUEST_LIMITS: GuestLimits = {
  maxMemoirs: 2,
  maxChaptersPerMemoir: 5,
  maxContentBlocksPerChapter: 10,
  validityDays: 7
}

export class GuestAuthService {
  private static readonly STORAGE_KEY = 'guest_user_info'
  
  // 生成设备指纹
  static generateDeviceFingerprint(): string {
    if (typeof window === 'undefined') {
      return 'server-' + Date.now()
    }
    
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    ctx?.fillText('fingerprint', 10, 10)
    const canvasFingerprint = canvas.toDataURL()
    
    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvasFingerprint.slice(-50), // 取canvas指纹的后50个字符
      Date.now().toString()
    ].join('|')
    
    // 简单哈希
    let hash = 0
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return 'guest_' + Math.abs(hash).toString(36) + '_' + Date.now().toString(36)
  }
  
  // 创建访客用户
  static createGuestUser(): GuestUser {
    const deviceFingerprint = this.generateDeviceFingerprint()
    const createdAt = new Date()
    const expiresAt = new Date(createdAt.getTime() + DEFAULT_GUEST_LIMITS.validityDays * 24 * 60 * 60 * 1000)
    
    const guestUser: GuestUser = {
      id: deviceFingerprint,
      deviceFingerprint,
      createdAt,
      expiresAt,
      isExpired: false
    }
    
    // 保存到本地存储
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify({
        ...guestUser,
        createdAt: guestUser.createdAt.toISOString(),
        expiresAt: guestUser.expiresAt.toISOString()
      }))
    }
    
    return guestUser
  }
  
  // 获取当前访客用户
  static getCurrentGuestUser(): GuestUser | null {
    if (typeof window === 'undefined') {
      return null
    }
    
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (!stored) {
        return null
      }
      
      const data = JSON.parse(stored)
      const guestUser: GuestUser = {
        ...data,
        createdAt: new Date(data.createdAt),
        expiresAt: new Date(data.expiresAt),
        isExpired: new Date() > new Date(data.expiresAt)
      }
      
      return guestUser
    } catch {
      return null
    }
  }
  
  // 检查访客用户是否过期
  static isGuestUserExpired(guestUser: GuestUser): boolean {
    return new Date() > guestUser.expiresAt
  }
  
  // 清理过期的访客用户数据
  static clearExpiredGuestUser(): void {
    const guestUser = this.getCurrentGuestUser()
    if (guestUser && this.isGuestUserExpired(guestUser)) {
      this.clearGuestUser()
    }
  }
  
  // 清理访客用户数据
  static clearGuestUser(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.STORAGE_KEY)
    }
  }

  // 完全清理访客会话（用于登出）
  static clearGuestSession(): void {
    if (typeof window !== 'undefined') {
      // 清理访客用户数据
      this.clearGuestUser()

      // 清理访客模式标识
      localStorage.removeItem('guest_mode')
      localStorage.removeItem('guest_user_id')

      // 清理所有访客相关的数据
      const keysToRemove: string[] = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && (
          key.startsWith('guest_') ||
          key.startsWith('memoir_') ||
          key.startsWith('conversation_') ||
          key.startsWith('share_') ||
          key.includes('processed_content') ||
          key.includes('content_comparisons')
        )) {
          keysToRemove.push(key)
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key))

      // 通知状态管理器
      GuestStateManager.notifyListeners(false)
    }
  }
  
  // 获取访客剩余天数
  static getRemainingDays(guestUser: GuestUser): number {
    const now = new Date()
    const remaining = guestUser.expiresAt.getTime() - now.getTime()
    return Math.max(0, Math.ceil(remaining / (24 * 60 * 60 * 1000)))
  }
  
  // 检查访客是否可以创建新回忆录
  static async canCreateMemoir(userId: string): Promise<{ allowed: boolean; reason?: string }> {
    try {
      const response = await fetch('/api/guest/check-limits', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, action: 'create_memoir' }),
      })
      
      const data = await response.json()
      return data
    } catch {
      return { allowed: false, reason: '检查权限失败' }
    }
  }
  
  // 生成访客用户显示名称
  static getGuestDisplayName(guestUser: GuestUser): string {
    const shortId = guestUser.id.slice(-8).toUpperCase()
    return `访客${shortId}`
  }
}

// 访客数据迁移服务
export class GuestDataMigrationService {
  // 将访客数据迁移到正式用户
  static async migrateGuestData(guestUserId: string, newUserId: string): Promise<{
    success: boolean
    message: string
    migratedItems?: {
      memoirs: number
      chapters: number
      contentBlocks: number
      photoAlbums: number
    }
  }> {
    try {
      const response = await fetch('/api/guest/migrate-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ guestUserId, newUserId }),
      })
      
      const data = await response.json()
      
      if (data.success) {
        // 清理本地访客数据
        GuestAuthService.clearGuestUser()
      }
      
      return data
    } catch (error) {
      console.error('数据迁移失败:', error)
      return {
        success: false,
        message: '数据迁移失败，请重试'
      }
    }
  }
}

// 访客状态管理
export class GuestStateManager {
  private static listeners: Array<(isGuest: boolean, guestUser?: GuestUser) => void> = []
  
  static addListener(callback: (isGuest: boolean, guestUser?: GuestUser) => void): void {
    this.listeners.push(callback)
  }
  
  static removeListener(callback: (isGuest: boolean, guestUser?: GuestUser) => void): void {
    this.listeners = this.listeners.filter(listener => listener !== callback)
  }
  
  static notifyListeners(isGuest: boolean, guestUser?: GuestUser): void {
    this.listeners.forEach(listener => listener(isGuest, guestUser))
  }
  
  static getCurrentState(): { isGuest: boolean; guestUser?: GuestUser } {
    const guestUser = GuestAuthService.getCurrentGuestUser()
    const isGuest = guestUser !== null && !GuestAuthService.isGuestUserExpired(guestUser)
    
    return { isGuest, guestUser: isGuest ? guestUser : undefined }
  }
}
