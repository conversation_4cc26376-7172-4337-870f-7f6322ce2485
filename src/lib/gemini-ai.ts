interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string
      }>
    }
  }>
}

interface GeminiRequest {
  contents: Array<{
    parts: Array<{
      text: string
    }>
  }>
  generationConfig?: {
    temperature?: number
    topK?: number
    topP?: number
    maxOutputTokens?: number
  }
}

export class GeminiAI {
  private readonly apiKey: string
  private readonly baseUrl = 'https://generativelanguage.googleapis.com/v1beta'
  private readonly model = 'gemini-1.5-flash'

  constructor(apiKey?: string) {
    this.apiKey = apiKey ?? process.env.GEMINI_API_KEY ?? ''
    if (!this.apiKey) {
      console.warn('Gemini API key not found. AI features will be disabled.')
    }
  }

  async generateContent(prompt: string, options?: {
    temperature?: number
    maxTokens?: number
  }): Promise<string> {
    if (!this.apiKey) {
      throw new Error('Gemini API key not configured')
    }

    const request: GeminiRequest = {
      contents: [{
        parts: [{
          text: prompt
        }]
      }],
      generationConfig: {
        temperature: options?.temperature ?? 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: options?.maxTokens ?? 2048
      }
    }

    try {
      const response = await fetch(
        `${this.baseUrl}/models/${this.model}:generateContent?key=${this.apiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(request)
        }
      )

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.error?.message ?? 'Unknown error'

        // 处理特定的错误类型
        if (errorMessage.includes('location is not supported')) {
          throw new Error('当前地区暂不支持AI功能，请稍后重试或联系管理员')
        }

        if (errorMessage.includes('quota') || errorMessage.includes('limit')) {
          throw new Error('AI服务使用量已达上限，请稍后重试')
        }

        if (errorMessage.includes('API key')) {
          throw new Error('AI服务配置错误，请联系管理员')
        }

        throw new Error(`AI服务错误: ${errorMessage}`)
      }

      const data: GeminiResponse = await response.json()
      
      if (!data.candidates?.[0]?.content?.parts?.[0]?.text) {
        throw new Error('Invalid response format from Gemini API')
      }

      return data.candidates[0].content.parts[0].text
    } catch (error) {
      console.error('Gemini API error:', error)
      throw error
    }
  }

  async processVoiceTranscript(transcript: string, context?: {
    chapterTitle?: string
    previousContent?: string
    memoirType?: string
  }): Promise<string> {
    const prompt = `
你是一个专业的回忆录编辑助手，专门帮助中老年用户整理他们的语音录制内容。

任务：将用户的语音转录文本整理成流畅、连贯的回忆录段落。

用户语音转录：
"${transcript}"

${context?.chapterTitle ? `章节主题：${context.chapterTitle}` : ''}
${context?.memoirType ? `回忆录类型：${context.memoirType}` : ''}
${context?.previousContent ? `之前的内容：${context.previousContent}` : ''}

请按照以下要求处理：

1. **保持原意**：不要改变用户想要表达的核心内容和情感
2. **语言润色**：将口语化的表达转换为书面语，但保持亲切自然的语调
3. **逻辑整理**：重新组织语句，使内容更加连贯流畅
4. **情感保留**：保持原有的情感色彩和个人特色
5. **适当补充**：在不改变原意的前提下，可以适当补充细节描述
6. **分段处理**：如果内容较长，请合理分段

输出格式：
- 直接输出整理后的文本，不需要额外的说明
- 保持中文表达习惯
- 语言温暖、真诚，符合回忆录的文体特点

整理后的内容：`

    return this.generateContent(prompt, {
      temperature: 0.7,
      maxTokens: 1024
    })
  }

  async generateChapterSuggestions(memoirType: string, existingChapters: string[]): Promise<string[]> {
    const prompt = `
作为回忆录创作助手，请为"${memoirType}"类型的回忆录推荐章节标题。

已有章节：
${existingChapters.map((chapter, index) => `${index + 1}. ${chapter}`).join('\n')}

请推荐5个新的章节标题，要求：
1. 与已有章节不重复
2. 符合"${memoirType}"的主题
3. 适合中老年用户的生活经历
4. 标题简洁明了，富有感情色彩

请只输出章节标题，每行一个，不需要编号：`

    const response = await this.generateContent(prompt, {
      temperature: 0.8,
      maxTokens: 512
    })

    return response
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .slice(0, 5)
  }

  async generateWritingPrompts(chapterTitle: string, memoirType: string): Promise<string[]> {
    const prompt = `
为回忆录章节"${chapterTitle}"（属于"${memoirType}"类型）生成5个引导性问题，帮助用户回忆和表达相关内容。

要求：
1. 问题要具体、有启发性
2. 适合中老年用户的生活经历
3. 能够引导用户说出具体的故事和细节
4. 语言亲切、易懂
5. 每个问题都能独立使用

请只输出问题，每行一个，不需要编号：`

    const response = await this.generateContent(prompt, {
      temperature: 0.8,
      maxTokens: 512
    })

    return response
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0 && line.includes('？'))
      .slice(0, 5)
  }

  async summarizeContent(content: string, maxLength: number = 200): Promise<string> {
    const prompt = `
请为以下回忆录内容生成一个简洁的摘要，长度控制在${maxLength}字以内：

内容：
"${content}"

要求：
1. 保留核心信息和情感
2. 语言简洁明了
3. 适合作为章节或段落的概述
4. 保持原文的语调和风格

摘要：`

    return this.generateContent(prompt, {
      temperature: 0.5,
      maxTokens: Math.min(maxLength * 2, 512)
    })
  }

  isConfigured(): boolean {
    return !!this.apiKey
  }
}

// Global instance
let globalGeminiAI: GeminiAI | null = null

export function getGeminiAI(): GeminiAI {
  globalGeminiAI ??= new GeminiAI()
  return globalGeminiAI
}

export default GeminiAI
