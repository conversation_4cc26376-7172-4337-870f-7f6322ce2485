export interface VoiceRecognitionOptions {
  language?: string
  continuous?: boolean
  interimResults?: boolean
  maxAlternatives?: number
}

export interface VoiceRecognitionResult {
  transcript: string
  confidence: number
  isFinal: boolean
}

export class VoiceRecognition {
  private recognition: SpeechRecognition | null = null
  private isSupported: boolean = false
  private isListening: boolean = false
  
  constructor(options: VoiceRecognitionOptions = {}) {
    this.isSupported = this.checkSupport()
    
    if (this.isSupported) {
      this.setupRecognition(options)
    }
  }
  
  private checkSupport(): boolean {
    return typeof window !== 'undefined' &&
           ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)
  }
  
  private setupRecognition(options: VoiceRecognitionOptions) {
    if (typeof window === 'undefined') return

    const SpeechRecognition = window.SpeechRecognition ?? window.webkitSpeechRecognition
    if (!SpeechRecognition) return

    this.recognition = new SpeechRecognition()

    // Configure recognition settings
    this.recognition.lang = options.language ?? 'zh-CN' // Default to Chinese
    this.recognition.continuous = options.continuous ?? true
    this.recognition.interimResults = options.interimResults ?? true
    this.recognition.maxAlternatives = options.maxAlternatives ?? 1
  }
  
  public isRecognitionSupported(): boolean {
    return this.isSupported
  }
  
  public isCurrentlyListening(): boolean {
    return this.isListening
  }
  
  public startListening(
    onResult: (result: VoiceRecognitionResult) => void,
    onError?: (error: string) => void,
    onEnd?: () => void
  ): void {
    if (!this.isSupported || !this.recognition) {
      onError?.('语音识别不支持')
      return
    }
    
    if (this.isListening) {
      onError?.('语音识别已在进行中')
      return
    }
    
    this.recognition.onresult = (event) => {
      const result = event.results[event.results.length - 1]
      const transcript = result[0].transcript
      const confidence = result[0].confidence
      const isFinal = result.isFinal
      
      onResult({
        transcript,
        confidence,
        isFinal
      })
    }
    
    this.recognition.onerror = (event) => {
      this.isListening = false

      const errorMessage = (() => {
        switch (event.error) {
          case 'no-speech':
            return '没有检测到语音，请重试'
          case 'audio-capture':
            return '无法访问麦克风，请检查权限'
          case 'not-allowed':
            return '麦克风权限被拒绝'
          case 'network':
            return '网络错误，请检查网络连接'
          case 'service-not-allowed':
            return '语音识别服务不可用'
          default:
            return `语音识别错误: ${event.error}`
        }
      })()

      onError?.(errorMessage)
    }
    
    this.recognition.onend = () => {
      this.isListening = false
      onEnd?.()
    }
    
    this.recognition.onstart = () => {
      this.isListening = true
    }
    
    try {
      this.recognition.start()
    } catch {
      this.isListening = false
      onError?.('启动语音识别失败')
    }
  }
  
  public stopListening(): void {
    if (this.recognition && this.isListening) {
      this.recognition.stop()
    }
  }
  
  public abortListening(): void {
    if (this.recognition && this.isListening) {
      this.recognition.abort()
      this.isListening = false
    }
  }
}

// Global voice recognition instance
let globalVoiceRecognition: VoiceRecognition | null = null

export function getVoiceRecognition(options?: VoiceRecognitionOptions): VoiceRecognition {
  globalVoiceRecognition ??= new VoiceRecognition(options)
  return globalVoiceRecognition
}

// Utility function to check if voice recognition is supported
export function isVoiceRecognitionSupported(): boolean {
  return typeof window !== 'undefined' &&
         ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)
}

// Type declarations for Speech Recognition API
declare global {
  interface Window {
    SpeechRecognition: new () => SpeechRecognition
    webkitSpeechRecognition: new () => SpeechRecognition
  }
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean
  grammars: SpeechGrammarList
  interimResults: boolean
  lang: string
  maxAlternatives: number
  serviceURI: string
  
  abort(): void
  start(): void
  stop(): void
  
  onaudioend: ((this: SpeechRecognition, ev: Event) => void) | null
  onaudiostart: ((this: SpeechRecognition, ev: Event) => void) | null
  onend: ((this: SpeechRecognition, ev: Event) => void) | null
  onerror: ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => void) | null
  onnomatch: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => void) | null
  onresult: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => void) | null
  onsoundend: ((this: SpeechRecognition, ev: Event) => void) | null
  onsoundstart: ((this: SpeechRecognition, ev: Event) => void) | null
  onspeechend: ((this: SpeechRecognition, ev: Event) => void) | null
  onspeechstart: ((this: SpeechRecognition, ev: Event) => void) | null
  onstart: ((this: SpeechRecognition, ev: Event) => void) | null
}

interface SpeechRecognitionEvent extends Event {
  readonly resultIndex: number
  readonly results: SpeechRecognitionResultList
}

interface SpeechRecognitionErrorEvent extends Event {
  readonly error: string
  readonly message: string
}

interface SpeechRecognitionResultList {
  readonly length: number
  item(index: number): SpeechRecognitionResult
  [index: number]: SpeechRecognitionResult
}

interface SpeechRecognitionResult {
  readonly isFinal: boolean
  readonly length: number
  item(index: number): SpeechRecognitionAlternative
  [index: number]: SpeechRecognitionAlternative
}

interface SpeechRecognitionAlternative {
  readonly confidence: number
  readonly transcript: string
}

interface SpeechGrammarList {
  readonly length: number
  addFromString(string: string, weight?: number): void
  addFromURI(src: string, weight?: number): void
  item(index: number): SpeechGrammar
  [index: number]: SpeechGrammar
}

interface SpeechGrammar {
  src: string
  weight: number
}
