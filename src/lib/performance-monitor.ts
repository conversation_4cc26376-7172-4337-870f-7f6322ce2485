// 性能监控服务
export interface PerformanceMetrics {
  readonly pageLoadTime: number
  readonly firstContentfulPaint: number
  readonly largestContentfulPaint: number
  readonly firstInputDelay: number
  readonly cumulativeLayoutShift: number
  readonly timeToInteractive: number
  readonly memoryUsage?: number
  readonly networkSpeed?: string
}

export interface UserInteraction {
  readonly type: 'click' | 'voice' | 'scroll' | 'input'
  readonly element: string
  readonly timestamp: number
  readonly duration?: number
  readonly success: boolean
  readonly errorMessage?: string
}

export interface AccessibilityIssue {
  readonly type: 'contrast' | 'focus' | 'aria' | 'keyboard' | 'text-size'
  readonly severity: 'low' | 'medium' | 'high' | 'critical'
  readonly element: string
  readonly description: string
  readonly suggestion: string
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: PerformanceMetrics | null = null
  private interactions: UserInteraction[] = []
  private accessibilityIssues: AccessibilityIssue[] = []
  private isMonitoring = false

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  // 开始监控
  startMonitoring(): void {
    if (typeof window === 'undefined' || this.isMonitoring) return

    this.isMonitoring = true
    this.collectPerformanceMetrics()
    this.setupInteractionTracking()
    this.checkAccessibility()
    this.monitorNetworkSpeed()
  }

  // 停止监控
  stopMonitoring(): void {
    this.isMonitoring = false
  }

  // 收集性能指标
  private collectPerformanceMetrics(): void {
    if (!window.performance) return

    // 等待页面加载完成
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        const paint = performance.getEntriesByType('paint')
        
        const fcp = paint.find(entry => entry.name === 'first-contentful-paint')?.startTime ?? 0
        
        this.collectMetricsAsync(navigation, fcp)
      }, 1000)
    })
  }

  // 异步收集指标
  private async collectMetricsAsync(navigation: PerformanceNavigationTiming, fcp: number): Promise<void> {
    const lcp = await this.getLCP()
    const fid = await this.getFID()
    const cls = this.getCLS()
    const tti = await this.getTTI()

    this.metrics = {
      pageLoadTime: navigation.loadEventEnd - navigation.fetchStart,
      firstContentfulPaint: fcp,
      largestContentfulPaint: lcp,
      firstInputDelay: fid,
      cumulativeLayoutShift: cls,
      timeToInteractive: tti,
      memoryUsage: this.getMemoryUsage(),
      networkSpeed: this.getNetworkSpeed()
    }

    this.reportMetrics()
  }

  // 获取最大内容绘制时间
  private getLCP(): Promise<number> {
    return new Promise<number>((resolve) => {
      try {
        new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries()
          const lastEntry = entries[entries.length - 1]
          resolve(lastEntry.startTime)
        }).observe({ entryTypes: ['largest-contentful-paint'] })

        // 超时返回0
        setTimeout(() => resolve(0), 5000)
      } catch {
        resolve(0)
      }
    })
  }

  // 获取首次输入延迟
  private getFID(): Promise<number> {
    return new Promise<number>((resolve) => {
      try {
        new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries()
          const firstEntry = entries[0] as PerformanceEventTiming
          resolve(firstEntry.processingStart - firstEntry.startTime)
        }).observe({ entryTypes: ['first-input'] })

        setTimeout(() => resolve(0), 5000)
      } catch {
        resolve(0)
      }
    })
  }

  // 获取累积布局偏移
  private getCLS(): number {
    let clsValue = 0
    try {
      new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          const layoutShiftEntry = entry as unknown as { hadRecentInput: boolean; value: number }
          if (!layoutShiftEntry.hadRecentInput) {
            clsValue += layoutShiftEntry.value
          }
        }
      }).observe({ entryTypes: ['layout-shift'] })
    } catch {
      // 忽略错误
    }

    return clsValue
  }

  // 获取可交互时间
  private getTTI(): Promise<number> {
    // 简化实现：使用DOMContentLoaded时间
    return new Promise<number>((resolve) => {
      if (document.readyState === 'interactive' || document.readyState === 'complete') {
        resolve(performance.now())
      } else {
        document.addEventListener('DOMContentLoaded', () => {
          resolve(performance.now())
        })
      }
    })
  }

  // 获取内存使用情况
  private getMemoryUsage(): number | undefined {
    if ('memory' in performance) {
      return (performance as { memory: { usedJSHeapSize: number } }).memory.usedJSHeapSize / 1024 / 1024 // MB
    }
    return undefined
  }

  // 获取网络速度
  private getNetworkSpeed(): string {
    if ('connection' in navigator) {
      const connection = (navigator as { connection: { effectiveType?: string } }).connection
      return connection.effectiveType ?? 'unknown'
    }
    return 'unknown'
  }

  // 监控网络速度
  private monitorNetworkSpeed(): void {
    if ('connection' in navigator) {
      const connection = (navigator as { connection: { addEventListener: (event: string, callback: () => void) => void; effectiveType: string } }).connection
      connection.addEventListener('change', () => {
        console.log('Network speed changed:', connection.effectiveType)
      })
    }
  }

  // 设置交互跟踪
  private setupInteractionTracking(): void {
    // 点击事件跟踪
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement
      this.trackInteraction({
        type: 'click',
        element: this.getElementSelector(target),
        timestamp: Date.now(),
        success: true
      })
    })

    // 滚动事件跟踪
    let scrollTimeout: NodeJS.Timeout
    document.addEventListener('scroll', () => {
      clearTimeout(scrollTimeout)
      scrollTimeout = setTimeout(() => {
        this.trackInteraction({
          type: 'scroll',
          element: 'window',
          timestamp: Date.now(),
          success: true
        })
      }, 100)
    })

    // 输入事件跟踪
    document.addEventListener('input', (event) => {
      const target = event.target as HTMLElement
      this.trackInteraction({
        type: 'input',
        element: this.getElementSelector(target),
        timestamp: Date.now(),
        success: true
      })
    })
  }

  // 跟踪用户交互
  trackInteraction(interaction: UserInteraction): void {
    this.interactions.push(interaction)
    
    // 限制存储的交互数量
    if (this.interactions.length > 100) {
      this.interactions = this.interactions.slice(-50)
    }
  }

  // 检查无障碍性
  private checkAccessibility(): void {
    setTimeout(() => {
      this.checkColorContrast()
      this.checkFocusability()
      this.checkAriaLabels()
      this.checkTextSize()
      this.checkKeyboardNavigation()
    }, 2000)
  }

  // 检查颜色对比度
  private checkColorContrast(): void {
    const elements = document.querySelectorAll('*')
    elements.forEach(element => {
      const styles = window.getComputedStyle(element)
      const color = styles.color
      const backgroundColor = styles.backgroundColor
      
      // 简化的对比度检查
      if (color && backgroundColor && color !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'rgba(0, 0, 0, 0)') {
        const contrast = this.calculateContrast()
        if (contrast < 4.5) {
          this.accessibilityIssues.push({
            type: 'contrast',
            severity: contrast < 3 ? 'high' : 'medium',
            element: this.getElementSelector(element as HTMLElement),
            description: `颜色对比度不足: ${contrast.toFixed(2)}`,
            suggestion: '增加颜色对比度至少4.5:1'
          })
        }
      }
    })
  }

  // 检查可聚焦性
  private checkFocusability(): void {
    const interactiveElements = document.querySelectorAll('button, a, input, select, textarea, [tabindex]')
    interactiveElements.forEach(element => {
      const styles = window.getComputedStyle(element)
      if (styles.outline === 'none' && !styles.boxShadow.includes('focus')) {
        this.accessibilityIssues.push({
          type: 'focus',
          severity: 'medium',
          element: this.getElementSelector(element as HTMLElement),
          description: '缺少焦点指示器',
          suggestion: '添加明显的焦点样式'
        })
      }
    })
  }

  // 检查ARIA标签
  private checkAriaLabels(): void {
    const buttons = document.querySelectorAll('button')
    buttons.forEach(button => {
      if (!button.textContent?.trim() && !button.getAttribute('aria-label') && !button.getAttribute('aria-labelledby')) {
        this.accessibilityIssues.push({
          type: 'aria',
          severity: 'high',
          element: this.getElementSelector(button),
          description: '按钮缺少可访问的名称',
          suggestion: '添加aria-label或文本内容'
        })
      }
    })
  }

  // 检查文字大小
  private checkTextSize(): void {
    const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6')
    textElements.forEach(element => {
      const styles = window.getComputedStyle(element)
      const fontSize = parseInt(styles.fontSize)
      if (fontSize < 14) {
        this.accessibilityIssues.push({
          type: 'text-size',
          severity: 'medium',
          element: this.getElementSelector(element as HTMLElement),
          description: `文字过小: ${fontSize}px`,
          suggestion: '建议最小字体大小为14px'
        })
      }
    })
  }

  // 检查键盘导航
  private checkKeyboardNavigation(): void {
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Tab') {
        const activeElement = document.activeElement
        if (activeElement && activeElement.tagName !== 'BODY') {
          this.trackInteraction({
            type: 'input',
            element: this.getElementSelector(activeElement as HTMLElement),
            timestamp: Date.now(),
            success: true
          })
        }
      }
    })
  }

  // 计算颜色对比度（简化版本）
  private calculateContrast(): number {
    // 这是一个简化的实现，实际应用中需要更复杂的颜色对比度计算
    return Math.random() * 10 + 1 // 占位符实现
  }

  // 获取元素选择器
  private getElementSelector(element: HTMLElement): string {
    if (element.id) return `#${element.id}`
    if (element.className) return `.${element.className.split(' ')[0]}`
    return element.tagName.toLowerCase()
  }

  // 报告性能指标
  private reportMetrics(): void {
    if (this.metrics) {
      console.group('🚀 Performance Metrics')
      console.log('Page Load Time:', `${this.metrics.pageLoadTime.toFixed(2)}ms`)
      console.log('First Contentful Paint:', `${this.metrics.firstContentfulPaint.toFixed(2)}ms`)
      console.log('Memory Usage:', this.metrics.memoryUsage ? `${this.metrics.memoryUsage.toFixed(2)}MB` : 'N/A')
      console.log('Network Speed:', this.metrics.networkSpeed)
      console.groupEnd()
    }
  }

  // 获取性能报告
  getPerformanceReport(): {
    metrics: PerformanceMetrics | null
    interactions: UserInteraction[]
    accessibilityIssues: AccessibilityIssue[]
  } {
    return {
      metrics: this.metrics,
      interactions: this.interactions.slice(),
      accessibilityIssues: this.accessibilityIssues.slice()
    }
  }

  // 获取性能评分
  getPerformanceScore(): number {
    if (!this.metrics) return 0

    let score = 100
    
    // 页面加载时间评分
    if (this.metrics.pageLoadTime > 3000) score -= 20
    else if (this.metrics.pageLoadTime > 2000) score -= 10
    
    // FCP评分
    if (this.metrics.firstContentfulPaint > 2000) score -= 15
    else if (this.metrics.firstContentfulPaint > 1000) score -= 8
    
    // 内存使用评分
    if (this.metrics.memoryUsage && this.metrics.memoryUsage > 50) score -= 10
    
    // 无障碍问题扣分
    this.accessibilityIssues.forEach(issue => {
      switch (issue.severity) {
        case 'critical': score -= 15; break
        case 'high': score -= 10; break
        case 'medium': score -= 5; break
        case 'low': score -= 2; break
      }
    })

    return Math.max(0, score)
  }
}
