// 短信服务接口
export interface SMSService {
  sendVerificationCode(phone: string): Promise<{ success: boolean; message: string }>
  verifyCode(phone: string, code: string): Promise<{ success: boolean; message: string }>
}

// 模拟短信服务（用于开发和演示）
export class MockSMSService implements SMSService {
  private codes: Map<string, { code: string; expires: number }> = new Map()
  
  async sendVerificationCode(phone: string): Promise<{ success: boolean; message: string }> {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 生成6位验证码
    const code = Math.floor(100000 + Math.random() * 900000).toString()
    
    // 设置5分钟过期
    const expires = Date.now() + 5 * 60 * 1000
    
    this.codes.set(phone, { code, expires })
    
    // 在开发环境中打印验证码到控制台
    if (process.env.NODE_ENV === 'development') {
      console.log(`📱 验证码已发送到 ${phone}: ${code}`)
    }
    
    return {
      success: true,
      message: `验证码已发送到 ${phone.slice(0, 3)}****${phone.slice(7)}`
    }
  }
  
  async verifyCode(phone: string, code: string): Promise<{ success: boolean; message: string }> {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const stored = this.codes.get(phone)
    
    if (!stored) {
      return {
        success: false,
        message: '请先获取验证码'
      }
    }
    
    if (Date.now() > stored.expires) {
      this.codes.delete(phone)
      return {
        success: false,
        message: '验证码已过期，请重新获取'
      }
    }
    
    if (stored.code !== code) {
      return {
        success: false,
        message: '验证码错误，请重新输入'
      }
    }
    
    // 验证成功后删除验证码
    this.codes.delete(phone)
    
    return {
      success: true,
      message: '验证成功'
    }
  }
}

// 阿里云短信服务（生产环境使用）
export class AliyunSMSService implements SMSService {
  private accessKeyId: string
  private accessKeySecret: string
  private signName: string
  private templateCode: string
  
  constructor(config: {
    accessKeyId: string
    accessKeySecret: string
    signName: string
    templateCode: string
  }) {
    this.accessKeyId = config.accessKeyId
    this.accessKeySecret = config.accessKeySecret
    this.signName = config.signName
    this.templateCode = config.templateCode
  }
  
  async sendVerificationCode(phone: string): Promise<{ success: boolean; message: string }> {
    try {
      // 生成6位验证码
      // const code = Math.floor(100000 + Math.random() * 900000).toString()
      
      // 这里应该调用阿里云短信API
      // 由于需要真实的API密钥，这里只是示例代码
      
      // const response = await fetch('https://dysmsapi.aliyuncs.com/', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/x-www-form-urlencoded',
      //   },
      //   body: new URLSearchParams({
      //     Action: 'SendSms',
      //     PhoneNumbers: phone,
      //     SignName: this.signName,
      //     TemplateCode: this.templateCode,
      //     TemplateParam: JSON.stringify({ code }),
      //     // 其他必需的阿里云API参数...
      //   })
      // })
      
      // 暂时返回成功（实际使用时需要处理真实的API响应）
      return {
        success: true,
        message: `验证码已发送到 ${phone.slice(0, 3)}****${phone.slice(7)}`
      }
    } catch (error) {
      console.error('发送短信失败:', error)
      return {
        success: false,
        message: '发送验证码失败，请稍后重试'
      }
    }
  }
  
  async verifyCode(): Promise<{ success: boolean; message: string }> {
    // 在实际应用中，验证码验证通常在服务端进行
    // 这里只是示例实现
    return {
      success: true,
      message: '验证成功'
    }
  }
}

// 短信服务工厂
export function createSMSService(): SMSService {
  // 根据环境变量决定使用哪种短信服务
  const useRealSMS = process.env.NODE_ENV === 'production' && 
                     process.env.ALIYUN_ACCESS_KEY_ID && 
                     process.env.ALIYUN_ACCESS_KEY_SECRET
  
  if (useRealSMS) {
    return new AliyunSMSService({
      accessKeyId: process.env.ALIYUN_ACCESS_KEY_ID!,
      accessKeySecret: process.env.ALIYUN_ACCESS_KEY_SECRET!,
      signName: process.env.SMS_SIGN_NAME || '我的回忆录',
      templateCode: process.env.SMS_TEMPLATE_CODE || 'SMS_123456789'
    })
  }
  
  return new MockSMSService()
}
