// 语音录制服务

export interface VoiceRecordingOptions {
  readonly sampleRate?: number
  readonly channels?: number
  readonly mimeType?: string
  readonly audioBitsPerSecond?: number
}

export interface VoiceRecordingResult {
  readonly audioBlob: Blob
  readonly duration: number
  readonly audioUrl: string
}

export interface TranscriptionResult {
  readonly text: string
  readonly confidence: number
  readonly isFinal: boolean
  readonly timestamp: number
}

export class VoiceRecordingService {
  private mediaRecorder: MediaRecorder | null = null
  private audioChunks: Blob[] = []
  private stream: MediaStream | null = null
  private startTime: number = 0
  private recognition: unknown = null
  private isRecording = false
  private onTranscriptionCallback?: (result: TranscriptionResult) => void
  private onRecordingStateChange?: (isRecording: boolean) => void

  constructor() {
    // 初始化语音识别
    if (typeof window !== 'undefined') {
      try {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition
        if (SpeechRecognition) {
          this.recognition = new SpeechRecognition()
          this.setupSpeechRecognition()
        }
      } catch (error) {
        console.warn('语音识别初始化失败:', error)
      }
    }
  }

  private setupSpeechRecognition() {
    if (!this.recognition) return

    try {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const recognition = this.recognition as any

      recognition.continuous = true
      recognition.interimResults = true
      recognition.lang = 'zh-CN'

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      recognition.onresult = (event: any) => {
        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i]
          const transcript = result[0].transcript
          const confidence = result[0].confidence ?? 0.8

          this.onTranscriptionCallback?.({
            text: transcript,
            confidence,
            isFinal: result.isFinal,
            timestamp: Date.now()
          })
        }
      }

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      recognition.onerror = (event: any) => {
        console.error('语音识别错误:', event.error)
      }

      recognition.onend = () => {
        // 如果还在录制中，重新启动识别
        if (this.isRecording) {
          try {
            recognition.start()
          } catch (error) {
            console.warn('重启语音识别失败:', error)
          }
        }
      }
    } catch (error) {
      console.warn('语音识别设置失败:', error)
    }
  }

  async startRecording(options: VoiceRecordingOptions = {}): Promise<void> {
    try {
      // 获取麦克风权限
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: options.sampleRate || 44100,
          channelCount: options.channels || 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      })

      // 设置录制器
      const mimeType = this.getSupportedMimeType(options.mimeType)
      this.mediaRecorder = new MediaRecorder(this.stream, {
        mimeType,
        audioBitsPerSecond: options.audioBitsPerSecond || 128000
      })

      this.audioChunks = []
      this.startTime = Date.now()
      this.isRecording = true

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data)
        }
      }

      this.mediaRecorder.start(1000) // 每秒收集一次数据

      // 启动语音识别
      if (this.recognition) {
        try {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (this.recognition as any).start()
        } catch (error) {
          console.warn('启动语音识别失败:', error)
        }
      }

      this.onRecordingStateChange?.(true)
    } catch (error) {
      console.error('启动录制失败:', error)
      throw new Error('无法访问麦克风，请检查权限设置')
    }
  }

  async stopRecording(): Promise<VoiceRecordingResult> {
    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder || !this.isRecording) {
        reject(new Error('没有正在进行的录制'))
        return
      }

      this.mediaRecorder.onstop = () => {
        const duration = Date.now() - this.startTime
        const audioBlob = new Blob(this.audioChunks, { 
          type: this.mediaRecorder?.mimeType || 'audio/webm' 
        })
        const audioUrl = URL.createObjectURL(audioBlob)

        resolve({
          audioBlob,
          duration,
          audioUrl
        })

        // 清理资源
        this.cleanup()
      }

      this.mediaRecorder.stop()
      this.isRecording = false

      // 停止语音识别
      if (this.recognition) {
        try {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (this.recognition as any).stop()
        } catch (error) {
          console.warn('停止语音识别失败:', error)
        }
      }

      this.onRecordingStateChange?.(false)
    })
  }

  private cleanup() {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop())
      this.stream = null
    }
    this.mediaRecorder = null
    this.audioChunks = []
  }

  private getSupportedMimeType(preferredType?: string): string {
    const types = [
      preferredType,
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/ogg;codecs=opus'
    ].filter(Boolean) as string[]

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type
      }
    }

    return 'audio/webm' // 默认类型
  }

  setTranscriptionCallback(callback: (result: TranscriptionResult) => void) {
    this.onTranscriptionCallback = callback
  }

  setRecordingStateCallback(callback: (isRecording: boolean) => void) {
    this.onRecordingStateChange = callback
  }

  getRecordingState(): boolean {
    return this.isRecording
  }

  // 检查浏览器支持
  static isSupported(): boolean {
    return !!(
      typeof navigator !== 'undefined' &&
      navigator.mediaDevices &&
      typeof navigator.mediaDevices.getUserMedia === 'function' &&
      typeof window !== 'undefined' &&
      window.MediaRecorder
    )
  }

  // 检查语音识别支持
  static isSpeechRecognitionSupported(): boolean {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return !!((window as any).SpeechRecognition || (window as any).webkitSpeechRecognition)
  }

  // 请求麦克风权限
  static async requestMicrophonePermission(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      stream.getTracks().forEach(track => track.stop())
      return true
    } catch {
      return false
    }
  }
}


