interface ProcessTranscriptRequest {
  transcript: string
  context?: {
    chapterTitle?: string
    previousContent?: string
    memoirType?: string
  }
}

interface ProcessTranscriptResponse {
  success: boolean
  processedContent?: string
  error?: string
}

export class AIClient {
  private readonly baseUrl: string
  private readonly maxRetries: number = 3
  private readonly retryDelay: number = 1000

  constructor() {
    // 在客户端使用相对路径，在服务端使用完整URL
    this.baseUrl = typeof window !== 'undefined' ? '' : (process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000')
  }

  // 重试逻辑
  private async retryWithBackoff<T>(
    operation: () => Promise<T>,
    retries: number = this.maxRetries
  ): Promise<T> {
    try {
      return await operation()
    } catch (error) {
      if (retries > 0 && this.isRetryableError(error)) {
        await new Promise(resolve => setTimeout(resolve, this.retryDelay * (this.maxRetries - retries + 1)))
        return this.retryWithBackoff(operation, retries - 1)
      }
      throw error
    }
  }

  // 判断是否为可重试的错误
  private isRetryableError(error: unknown): boolean {
    if (error instanceof Error) {
      const message = error.message.toLowerCase()
      // 网络错误、超时错误、服务器错误可以重试
      return message.includes('network') ||
             message.includes('timeout') ||
             message.includes('fetch') ||
             message.includes('503') ||
             message.includes('502') ||
             message.includes('500')
    }
    return false
  }

  async processTranscript(request: ProcessTranscriptRequest): Promise<string> {
    return this.retryWithBackoff(async () => {
      try {
        const response = await fetch(`${this.baseUrl}/api/ai/process-transcript`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(request),
        })

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          const errorMessage = errorData.error || `HTTP ${response.status}: ${response.statusText}`

          // 为不同的HTTP状态码提供更具体的错误信息
          if (response.status === 429) {
            throw new Error('AI服务使用量已达上限，请稍后重试')
          } else if (response.status === 503) {
            throw new Error('AI服务暂时不可用，请稍后重试')
          } else if (response.status >= 500) {
            throw new Error('AI服务器错误，请稍后重试')
          } else if (response.status === 400) {
            throw new Error(errorMessage || '请求参数错误')
          } else {
            throw new Error(errorMessage)
          }
        }

        const data: ProcessTranscriptResponse = await response.json()

        if (!data.success || !data.processedContent) {
          throw new Error(data.error || 'AI处理失败')
        }

        return data.processedContent
      } catch (error) {
        console.error('AI Client error:', error)

        if (error instanceof Error) {
          // 检查是否为网络错误
          if (error.name === 'TypeError' && error.message.includes('fetch')) {
            throw new Error('网络连接错误，请检查网络后重试')
          }

          // 重新抛出具体的错误信息
          throw error
        }

        throw new Error('AI处理失败，请重试')
      }
    })
  }

  // 健康检查
  async healthCheck(): Promise<{ status: string; message: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        return {
          status: 'error',
          message: `健康检查失败: HTTP ${response.status}`
        }
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('Health check error:', error)
      return {
        status: 'error',
        message: error instanceof Error ? error.message : '健康检查失败'
      }
    }
  }

  async generateChapterSuggestions(memoirType: string, existingChapters: string[]): Promise<string[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/generate-chapters`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ memoirType, existingChapters }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      return data.suggestions || []
    } catch (error) {
      console.error('AI Client error:', error)
      throw new Error('生成章节建议失败')
    }
  }

  async generateWritingPrompts(chapterTitle: string, memoirType: string): Promise<string[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/generate-prompts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ chapterTitle, memoirType }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      return data.prompts || []
    } catch (error) {
      console.error('AI Client error:', error)
      throw new Error('生成写作提示失败')
    }
  }

  async summarizeContent(content: string, maxLength: number = 200): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/summarize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content, maxLength }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      return data.summary || ''
    } catch (error) {
      console.error('AI Client error:', error)
      throw new Error('内容摘要生成失败')
    }
  }

  // 检查AI服务是否可用
  async checkAvailability(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/health`, {
        method: 'GET',
      })
      return response.ok
    } catch {
      return false
    }
  }
}

// 全局实例
let globalAIClient: AIClient | null = null

export function getAIClient(): AIClient {
  globalAIClient ??= new AIClient()
  return globalAIClient
}

export default AIClient
