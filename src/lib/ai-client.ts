interface ProcessTranscriptRequest {
  transcript: string
  context?: {
    chapterTitle?: string
    previousContent?: string
    memoirType?: string
  }
}

interface ProcessTranscriptResponse {
  success: boolean
  processedContent?: string
  error?: string
}

export class AIClient {
  private baseUrl: string

  constructor() {
    // 在客户端使用相对路径，在服务端使用完整URL
    this.baseUrl = typeof window !== 'undefined' ? '' : (process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000')
  }

  async processTranscript(request: ProcessTranscriptRequest): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/process-transcript`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      const data: ProcessTranscriptResponse = await response.json()

      if (!data.success || !data.processedContent) {
        throw new Error(data.error || 'AI处理失败')
      }

      return data.processedContent
    } catch (error) {
      console.error('AI Client error:', error)
      
      if (error instanceof Error) {
        // 重新抛出具体的错误信息
        throw error
      }
      
      throw new Error('AI处理失败，请重试')
    }
  }

  async generateChapterSuggestions(memoirType: string, existingChapters: string[]): Promise<string[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/generate-chapters`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ memoirType, existingChapters }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      return data.suggestions || []
    } catch (error) {
      console.error('AI Client error:', error)
      throw new Error('生成章节建议失败')
    }
  }

  async generateWritingPrompts(chapterTitle: string, memoirType: string): Promise<string[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/generate-prompts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ chapterTitle, memoirType }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      return data.prompts || []
    } catch (error) {
      console.error('AI Client error:', error)
      throw new Error('生成写作提示失败')
    }
  }

  async summarizeContent(content: string, maxLength: number = 200): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/summarize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content, maxLength }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      return data.summary || ''
    } catch (error) {
      console.error('AI Client error:', error)
      throw new Error('内容摘要生成失败')
    }
  }

  // 检查AI服务是否可用
  async checkAvailability(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/health`, {
        method: 'GET',
      })
      return response.ok
    } catch {
      return false
    }
  }
}

// 全局实例
let globalAIClient: AIClient | null = null

export function getAIClient(): AIClient {
  if (!globalAIClient) {
    globalAIClient = new AIClient()
  }
  return globalAIClient
}

export default AIClient
