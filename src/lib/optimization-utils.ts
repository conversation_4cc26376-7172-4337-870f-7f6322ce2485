// 设备性能类型
type DevicePerformance = {
  level: 'high' | 'medium' | 'low'
  cores: number
  memory: number
  connection: string
}

// 性能优化工具
export class OptimizationUtils {
  // 防抖函数
  static debounce<T extends (...args: unknown[]) => unknown>(
    func: T,
    wait: number,
    immediate = false
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout | null = null
    
    return function executedFunction(...args: Parameters<T>) {
      const later = () => {
        timeout = null
        if (!immediate) func(...args)
      }
      
      const callNow = immediate && !timeout
      
      if (timeout) clearTimeout(timeout)
      timeout = setTimeout(later, wait)
      
      if (callNow) func(...args)
    }
  }

  // 节流函数
  static throttle<T extends (...args: unknown[]) => unknown>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean
    
    return function executedFunction(...args: Parameters<T>) {
      if (!inThrottle) {
        func(...args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }

  // 图片懒加载
  static setupLazyLoading(): void {
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            if (img.dataset.src) {
              img.src = img.dataset.src
              img.classList.remove('lazy')
              observer.unobserve(img)
            }
          }
        })
      })

      document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img)
      })
    }
  }

  // 预加载关键资源
  static preloadCriticalResources(resources: string[]): void {
    resources.forEach(resource => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = resource
      
      if (resource.endsWith('.css')) {
        link.as = 'style'
      } else if (resource.endsWith('.js')) {
        link.as = 'script'
      } else if (/\.(jpg|jpeg|png|webp|svg)$/.test(resource)) {
        link.as = 'image'
      }
      
      document.head.appendChild(link)
    })
  }

  // 内存清理
  static cleanupMemory(): void {
    // 清理事件监听器
    const elements = document.querySelectorAll('[data-cleanup]')
    elements.forEach(element => {
      const events = element.getAttribute('data-cleanup')?.split(',') || []
      events.forEach(event => {
        element.removeEventListener(event.trim(), () => {})
      })
    })

    // 强制垃圾回收（如果可用）
    if ('gc' in window && typeof (window as { gc?: () => void }).gc === 'function') {
      (window as { gc: () => void }).gc()
    }
  }

  // 检测设备性能
  static getDevicePerformance(): DevicePerformance {
    const cores = navigator.hardwareConcurrency ?? 4
    const memory = (navigator as { deviceMemory?: number }).deviceMemory ?? 4
    const connection = (navigator as { connection?: { effectiveType?: string } }).connection?.effectiveType ?? '4g'

    let level: 'high' | 'medium' | 'low'

    if (cores >= 8 && memory >= 8) {
      level = 'high'
    } else if (cores >= 4 && memory >= 4) {
      level = 'medium'
    } else {
      level = 'low'
    }

    return { level, cores, memory, connection }
  }

  // 自适应质量设置
  static getAdaptiveSettings(): {
    imageQuality: 'high' | 'medium' | 'low'
    animationEnabled: boolean
    prefetchEnabled: boolean
    cacheSize: number
  } {
    const performance = this.getDevicePerformance()
    
    switch (performance.level) {
      case 'high':
        return {
          imageQuality: 'high',
          animationEnabled: true,
          prefetchEnabled: true,
          cacheSize: 100
        }
      case 'medium':
        return {
          imageQuality: 'medium',
          animationEnabled: true,
          prefetchEnabled: true,
          cacheSize: 50
        }
      case 'low':
        return {
          imageQuality: 'low',
          animationEnabled: false,
          prefetchEnabled: false,
          cacheSize: 20
        }
    }
  }

  // 网络状态监控
  static setupNetworkMonitoring(callback: (online: boolean, speed: string) => void): void {
    // 在线状态监控
    window.addEventListener('online', () => callback(true, 'unknown'))
    window.addEventListener('offline', () => callback(false, 'offline'))

    // 网络速度监控
    if ('connection' in navigator) {
      const connection = (navigator as { connection: { addEventListener: (event: string, callback: () => void) => void; effectiveType: string } }).connection
      connection.addEventListener('change', () => {
        callback(navigator.onLine, connection.effectiveType)
      })
    }
  }

  // 缓存管理
  static setupCacheManagement(): void {
    // 注意：ServiceWorker已禁用，避免404错误
    // 如需启用PWA功能，请创建public/sw.js文件

    // 本地存储清理
    this.cleanupLocalStorage()
  }

  // 清理本地存储
  static cleanupLocalStorage(): void {
    const maxAge = 7 * 24 * 60 * 60 * 1000 // 7天
    const now = Date.now()

    Object.keys(localStorage).forEach(key => {
      try {
        const item = localStorage.getItem(key)
        if (item) {
          const data = JSON.parse(item)
          if (data.timestamp && (now - data.timestamp) > maxAge) {
            localStorage.removeItem(key)
          }
        }
      } catch {
        // 忽略解析错误
      }
    })
  }

  // 图片压缩
  static compressImage(
    file: File,
    maxWidth: number = 1920,
    maxHeight: number = 1080,
    quality: number = 0.8
  ): Promise<Blob> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      const img = new Image()

      img.onload = () => {
        // 计算新尺寸
        let { width, height } = img
        
        if (width > maxWidth) {
          height = (height * maxWidth) / width
          width = maxWidth
        }
        
        if (height > maxHeight) {
          width = (width * maxHeight) / height
          height = maxHeight
        }

        canvas.width = width
        canvas.height = height

        // 绘制并压缩
        ctx.drawImage(img, 0, 0, width, height)
        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob)
          } else {
            resolve(new Blob())
          }
        }, 'image/jpeg', quality)
      }

      img.src = URL.createObjectURL(file)
    })
  }

  // 批量处理
  static async batchProcess<T, R>(
    items: T[],
    processor: (item: T) => Promise<R>,
    batchSize: number = 5,
    delay: number = 100
  ): Promise<R[]> {
    const results: R[] = []

    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize)
      const batchResults = await Promise.all(batch.map(processor))
      results.push(...batchResults)

      // 延迟以避免阻塞UI
      if (i + batchSize < items.length) {
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    return results
  }

  // 虚拟滚动
  static setupVirtualScrolling<T>(
    container: HTMLElement,
    itemHeight: number,
    items: T[],
    renderItem: (item: T, index: number) => HTMLElement
  ): void {
    const containerHeight = container.clientHeight
    const visibleCount = Math.ceil(containerHeight / itemHeight) + 2
    let startIndex = 0

    const updateVisibleItems = () => {
      const scrollTop = container.scrollTop
      startIndex = Math.floor(scrollTop / itemHeight)
      const endIndex = Math.min(startIndex + visibleCount, items.length)

      // 清空容器
      container.innerHTML = ''

      // 创建占位符
      const spacerTop = document.createElement('div')
      spacerTop.style.height = `${startIndex * itemHeight}px`
      container.appendChild(spacerTop)

      // 渲染可见项
      for (let i = startIndex; i < endIndex; i++) {
        const element = renderItem(items[i], i)
        container.appendChild(element)
      }

      // 创建底部占位符
      const spacerBottom = document.createElement('div')
      spacerBottom.style.height = `${(items.length - endIndex) * itemHeight}px`
      container.appendChild(spacerBottom)
    }

    container.addEventListener('scroll', this.throttle(updateVisibleItems, 16))
    updateVisibleItems()
  }

  // 预取数据
  static prefetchData(urls: string[]): void {
    const settings = this.getAdaptiveSettings()
    if (!settings.prefetchEnabled) return

    urls.forEach(url => {
      fetch(url, {
        method: 'GET'
        // priority: 'low' // 低优先级 - 暂时移除，因为类型不兼容
      }).catch(() => {
        // 忽略预取错误
      })
    })
  }

  // 资源提示
  static addResourceHints(): void {
    const hints: Array<{ rel: string; href: string; crossorigin?: string }> = [
      { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
      { rel: 'dns-prefetch', href: '//api.example.com' },
      { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: 'anonymous' }
    ]

    hints.forEach(hint => {
      const link = document.createElement('link')
      link.rel = hint.rel
      link.href = hint.href
      if (hint.crossorigin) {
        link.crossOrigin = hint.crossorigin
      }
      document.head.appendChild(link)
    })
  }

  // 初始化所有优化
  static initializeOptimizations(): void {
    // 设置懒加载
    this.setupLazyLoading()
    
    // 设置缓存管理
    this.setupCacheManagement()
    
    // 添加资源提示
    this.addResourceHints()
    
    // 清理本地存储
    this.cleanupLocalStorage()
    
    // 预加载关键资源
    this.preloadCriticalResources([
      '/fonts/main.woff2',
      '/images/logo.svg'
    ])

    console.log('🚀 Performance optimizations initialized')
  }
}
