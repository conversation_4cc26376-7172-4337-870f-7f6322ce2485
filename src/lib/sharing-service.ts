// 分享服务
export interface ShareOptions {
  readonly type: 'link' | 'social' | 'email' | 'print'
  readonly platform?: 'wechat' | 'weibo' | 'qq' | 'facebook' | 'twitter' | 'whatsapp'
  readonly privacy: 'public' | 'private' | 'family'
  readonly expiresAt?: string
  readonly password?: string
  readonly allowComments?: boolean
  readonly allowDownload?: boolean
  readonly customMessage?: string
}

export interface ShareData {
  readonly title: string
  readonly content: string
  readonly author: string
  readonly url?: string
  readonly imageUrl?: string
  readonly description?: string
}

export interface ShareResult {
  readonly success: boolean
  readonly shareUrl?: string
  readonly qrCode?: string
  readonly error?: string
  readonly platform?: string
}

export class SharingService {
  private static readonly BASE_URL = typeof window !== 'undefined' ? window.location.origin : ''

  // 生成分享链接
  static async generateShareLink(
    contentId: string,
    options: ShareOptions
  ): Promise<ShareResult> {
    try {
      // 生成唯一的分享ID
      const shareId = this.generateShareId()
      
      // 构建分享URL
      const shareUrl = `${this.BASE_URL}/share/${shareId}`
      
      // 存储分享配置（实际应用中应该保存到数据库）
      const shareConfig = {
        contentId,
        shareId,
        ...options,
        createdAt: new Date().toISOString(),
        accessCount: 0
      }
      
      // 保存到localStorage作为演示（实际应用中应该保存到数据库）
      localStorage.setItem(`share_${shareId}`, JSON.stringify(shareConfig))
      
      // 生成二维码
      const qrCode = await this.generateQRCode(shareUrl)
      
      return {
        success: true,
        shareUrl,
        qrCode,
        platform: 'link'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '生成分享链接失败'
      }
    }
  }

  // 社交媒体分享
  static async shareToSocial(
    data: ShareData,
    platform: NonNullable<ShareOptions['platform']>
  ): Promise<ShareResult> {
    try {
      const shareUrl = this.buildSocialShareUrl(data, platform)
      
      if (shareUrl) {
        // 打开分享窗口
        const width = 600
        const height = 400
        const left = (window.screen.width - width) / 2
        const top = (window.screen.height - height) / 2
        
        window.open(
          shareUrl,
          'share',
          `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`
        )
        
        return {
          success: true,
          shareUrl,
          platform
        }
      } else {
        throw new Error(`不支持的分享平台: ${platform}`)
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '社交媒体分享失败',
        platform
      }
    }
  }

  // 邮件分享
  static shareByEmail(data: ShareData, options: ShareOptions): ShareResult {
    try {
      const subject = encodeURIComponent(`分享回忆录：${data.title}`)
      const body = encodeURIComponent(
        `${options.customMessage || '我想与您分享这篇回忆录：'}\n\n` +
        `标题：${data.title}\n` +
        `作者：${data.author}\n\n` +
        `${data.description || data.content.substring(0, 200)}...\n\n` +
        `点击链接查看完整内容：${data.url || ''}`
      )
      
      const mailtoUrl = `mailto:?subject=${subject}&body=${body}`
      window.location.href = mailtoUrl
      
      return {
        success: true,
        shareUrl: mailtoUrl,
        platform: 'email'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '邮件分享失败',
        platform: 'email'
      }
    }
  }

  // 打印分享
  static printContent(data: ShareData): ShareResult {
    try {
      const printWindow = window.open('', '_blank')
      if (!printWindow) {
        throw new Error('无法打开打印窗口')
      }

      const printContent = this.generatePrintHTML(data)
      printWindow.document.write(printContent)
      printWindow.document.close()
      
      printWindow.onload = () => {
        printWindow.print()
        printWindow.close()
      }
      
      return {
        success: true,
        platform: 'print'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '打印失败',
        platform: 'print'
      }
    }
  }

  // 复制到剪贴板
  static async copyToClipboard(text: string): Promise<boolean> {
    try {
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(text)
        return true
      } else {
        // 备用方法
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        return true
      }
    } catch (error) {
      console.error('复制到剪贴板失败:', error)
      return false
    }
  }

  // 原生分享API
  static async nativeShare(data: ShareData): Promise<ShareResult> {
    try {
      if (navigator.share) {
        await navigator.share({
          title: data.title,
          text: data.description || data.content.substring(0, 200),
          url: data.url
        })
        
        return {
          success: true,
          platform: 'native'
        }
      } else {
        throw new Error('浏览器不支持原生分享功能')
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '原生分享失败',
        platform: 'native'
      }
    }
  }

  // 生成分享ID
  private static generateShareId(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15)
  }

  // 生成二维码
  private static async generateQRCode(url: string): Promise<string> {
    // 简化实现：返回一个占位符
    // 实际应用中可以使用qrcode库或API服务
    return `data:image/svg+xml;base64,${btoa(`
      <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
        <rect width="200" height="200" fill="white"/>
        <text x="100" y="100" text-anchor="middle" font-family="Arial" font-size="12">
          二维码
        </text>
        <text x="100" y="120" text-anchor="middle" font-family="Arial" font-size="8">
          ${url.substring(0, 30)}...
        </text>
      </svg>
    `)}`
  }

  // 构建社交媒体分享URL
  private static buildSocialShareUrl(data: ShareData, platform: string): string | null {
    const encodedTitle = encodeURIComponent(data.title)
    const encodedDescription = encodeURIComponent(data.description || data.content.substring(0, 200))
    const encodedUrl = encodeURIComponent(data.url || '')

    switch (platform) {
      case 'wechat':
        // 微信分享通常通过JS SDK实现，这里返回null
        return null
      
      case 'weibo':
        return `https://service.weibo.com/share/share.php?title=${encodedTitle}&url=${encodedUrl}&content=utf-8&searchPic=false&pic=${encodeURIComponent(data.imageUrl || '')}`
      
      case 'qq':
        return `https://connect.qq.com/widget/shareqq/index.html?title=${encodedTitle}&desc=${encodedDescription}&summary=${encodedDescription}&site=${encodedUrl}`
      
      case 'facebook':
        return `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedTitle}`
      
      case 'twitter':
        return `https://twitter.com/intent/tweet?text=${encodedTitle}&url=${encodedUrl}`
      
      case 'whatsapp':
        return `https://wa.me/?text=${encodedTitle}%20${encodedUrl}`
      
      default:
        return null
    }
  }

  // 生成打印HTML
  private static generatePrintHTML(data: ShareData): string {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>${data.title}</title>
    <style>
        @page {
            size: A4;
            margin: 2cm;
        }
        
        body {
            font-family: 'Times New Roman', serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2em;
            border-bottom: 2px solid #333;
            padding-bottom: 1em;
        }
        
        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 0.5em;
        }
        
        .author {
            font-size: 16px;
            color: #666;
        }
        
        .content {
            text-align: justify;
            white-space: pre-wrap;
        }
        
        .footer {
            margin-top: 2em;
            padding-top: 1em;
            border-top: 1px solid #ccc;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">${data.title}</div>
        <div class="author">作者：${data.author}</div>
    </div>
    
    <div class="content">${data.content}</div>
    
    <div class="footer">
        <p>打印时间：${new Date().toLocaleString('zh-CN')}</p>
        ${data.url ? `<p>原文链接：${data.url}</p>` : ''}
    </div>
</body>
</html>
    `
  }

  // 获取分享统计
  static getShareStats(shareId: string): {
    accessCount: number
    createdAt: string
    lastAccessed?: string
  } | null {
    try {
      const shareConfig = localStorage.getItem(`share_${shareId}`)
      if (shareConfig) {
        const config = JSON.parse(shareConfig)
        return {
          accessCount: config.accessCount || 0,
          createdAt: config.createdAt,
          lastAccessed: config.lastAccessed
        }
      }
      return null
    } catch {
      return null
    }
  }

  // 更新访问统计
  static updateShareAccess(shareId: string): void {
    try {
      const shareConfig = localStorage.getItem(`share_${shareId}`)
      if (shareConfig) {
        const config = JSON.parse(shareConfig)
        config.accessCount = (config.accessCount || 0) + 1
        config.lastAccessed = new Date().toISOString()
        localStorage.setItem(`share_${shareId}`, JSON.stringify(config))
      }
    } catch (error) {
      console.error('更新分享统计失败:', error)
    }
  }

  // 检查分享权限
  static checkSharePermission(shareId: string, password?: string): boolean {
    try {
      const shareConfig = localStorage.getItem(`share_${shareId}`)
      if (!shareConfig) return false

      const config = JSON.parse(shareConfig)
      
      // 检查过期时间
      if (config.expiresAt && new Date(config.expiresAt) < new Date()) {
        return false
      }
      
      // 检查密码
      if (config.password && config.password !== password) {
        return false
      }
      
      return true
    } catch {
      return false
    }
  }
}
