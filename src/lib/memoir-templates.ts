export interface MemoirTemplate {
  id: string
  name: string
  description: string
  icon: string
  category: 'life' | 'family' | 'career' | 'travel' | 'custom'
  estimatedTime: string
  difficulty: 'easy' | 'medium' | 'hard'
  tags: string[]
  chapters: ChapterTemplate[]
}

export interface ChapterTemplate {
  id: string
  title: string
  description: string
  prompts: string[]
  suggestedDuration: number // 建议录制时长（分钟）
  order: number
}

export const memoirTemplates: MemoirTemplate[] = [
  {
    id: 'life-journey',
    name: '人生历程',
    description: '记录您完整的人生故事，从童年到现在的重要时刻',
    icon: '📖',
    category: 'life',
    estimatedTime: '4-6周',
    difficulty: 'medium',
    tags: ['人生', '成长', '经历', '回忆'],
    chapters: [
      {
        id: 'childhood',
        title: '童年时光',
        description: '回忆您的童年生活、家庭环境和早期经历',
        order: 1,
        suggestedDuration: 15,
        prompts: [
          '您出生在哪里？当时的家庭环境是怎样的？',
          '您最早的记忆是什么？',
          '童年时最喜欢的游戏或活动是什么？',
          '您的父母是什么样的人？他们对您有什么影响？',
          '有什么童年趣事让您印象深刻？'
        ]
      },
      {
        id: 'school-years',
        title: '求学时代',
        description: '分享您的学习经历、老师同学和校园生活',
        order: 2,
        suggestedDuration: 18,
        prompts: [
          '您在哪里上学？学校是什么样子的？',
          '最喜欢的老师是谁？为什么？',
          '有什么难忘的同学友谊？',
          '学习中遇到过什么挑战？如何克服的？',
          '毕业时的心情和计划是什么？'
        ]
      },
      {
        id: 'career-life',
        title: '工作生涯',
        description: '记录您的职业发展、工作经历和成就',
        order: 3,
        suggestedDuration: 20,
        prompts: [
          '您的第一份工作是什么？感受如何？',
          '职业生涯中最重要的转折点是什么？',
          '遇到过什么工作上的挑战？如何解决的？',
          '最自豪的工作成就是什么？',
          '工作中结识了哪些重要的人？'
        ]
      },
      {
        id: 'family-life',
        title: '家庭生活',
        description: '分享您的婚姻、子女和家庭温馨时光',
        order: 4,
        suggestedDuration: 22,
        prompts: [
          '您是如何遇到伴侣的？',
          '结婚时的情景是怎样的？',
          '孩子们小时候有什么有趣的事情？',
          '家庭传统或习惯有哪些？',
          '最珍贵的家庭时光是什么？'
        ]
      },
      {
        id: 'life-wisdom',
        title: '人生感悟',
        description: '总结人生智慧、经验教训和对后代的寄语',
        order: 5,
        suggestedDuration: 25,
        prompts: [
          '人生中最重要的教训是什么？',
          '如果重新来过，您会做什么不同的选择？',
          '什么事情让您感到最自豪？',
          '您想对子孙后代说些什么？',
          '您希望被人们如何记住？'
        ]
      }
    ]
  },
  {
    id: 'family-heritage',
    name: '家族传承',
    description: '记录家族历史、传统文化和祖辈故事',
    icon: '🏠',
    category: 'family',
    estimatedTime: '3-4周',
    difficulty: 'medium',
    tags: ['家族', '传承', '历史', '文化'],
    chapters: [
      {
        id: 'ancestors',
        title: '祖辈故事',
        description: '记录您所了解的祖父母和更早祖辈的故事',
        order: 1,
        suggestedDuration: 20,
        prompts: [
          '您的祖父母是什么样的人？',
          '家族有什么传说或历史故事？',
          '祖辈们经历过什么重大历史事件？',
          '家族的起源和迁移历史是怎样的？',
          '有什么家族传统一直延续到现在？'
        ]
      },
      {
        id: 'family-traditions',
        title: '家族传统',
        description: '记录家族的节日习俗、传统技艺和文化传承',
        order: 2,
        suggestedDuration: 18,
        prompts: [
          '家族有什么特殊的节日庆祝方式？',
          '有什么传统手艺或技能在家族中传承？',
          '家族的家训或座右铭是什么？',
          '有什么特殊的家族食谱或菜肴？',
          '家族中有什么珍贵的传家宝？'
        ]
      },
      {
        id: 'family-members',
        title: '家族成员',
        description: '介绍重要的家族成员和他们的故事',
        order: 3,
        suggestedDuration: 22,
        prompts: [
          '家族中最有影响力的人是谁？',
          '兄弟姐妹们都有什么特点？',
          '家族中有什么有趣的人物？',
          '谁对您的人生影响最大？',
          '家族聚会时最难忘的时刻？'
        ]
      }
    ]
  },
  {
    id: 'career-legacy',
    name: '职业传奇',
    description: '专注记录您的职业生涯、专业成就和行业见解',
    icon: '💼',
    category: 'career',
    estimatedTime: '2-3周',
    difficulty: 'medium',
    tags: ['职业', '工作', '成就', '经验'],
    chapters: [
      {
        id: 'career-start',
        title: '职业起步',
        description: '记录您的职业选择和早期工作经历',
        order: 1,
        suggestedDuration: 15,
        prompts: [
          '为什么选择这个职业？',
          '第一天上班的情景是怎样的？',
          '早期工作中学到了什么重要技能？',
          '遇到过什么职场导师？',
          '最初的职业目标是什么？'
        ]
      },
      {
        id: 'professional-growth',
        title: '专业发展',
        description: '分享您的技能提升和职业成长历程',
        order: 2,
        suggestedDuration: 20,
        prompts: [
          '职业生涯中的重要转折点？',
          '如何不断学习和提升自己？',
          '面对过什么专业挑战？',
          '最自豪的专业成就是什么？',
          '如何平衡工作和生活？'
        ]
      },
      {
        id: 'industry-insights',
        title: '行业见解',
        description: '记录您对行业发展和未来趋势的看法',
        order: 3,
        suggestedDuration: 18,
        prompts: [
          '您见证了行业的哪些变化？',
          '对行业新人有什么建议？',
          '行业未来的发展趋势如何？',
          '什么是成功的关键因素？',
          '您想留下什么职业遗产？'
        ]
      }
    ]
  },
  {
    id: 'travel-memories',
    name: '旅行回忆',
    description: '记录您的旅行经历、见闻和感受',
    icon: '✈️',
    category: 'travel',
    estimatedTime: '1-2周',
    difficulty: 'easy',
    tags: ['旅行', '见闻', '文化', '冒险'],
    chapters: [
      {
        id: 'memorable-trips',
        title: '难忘旅程',
        description: '分享最难忘的旅行经历',
        order: 1,
        suggestedDuration: 20,
        prompts: [
          '最难忘的一次旅行是去哪里？',
          '旅行中遇到了什么有趣的人？',
          '有什么意外的发现或惊喜？',
          '旅行中学到了什么？',
          '哪个地方让您想要再次造访？'
        ]
      },
      {
        id: 'cultural-experience',
        title: '文化体验',
        description: '记录不同文化的体验和感受',
        order: 2,
        suggestedDuration: 15,
        prompts: [
          '体验过哪些不同的文化？',
          '最印象深刻的当地习俗是什么？',
          '尝试过什么特色美食？',
          '语言不通时如何沟通？',
          '旅行如何改变了您的世界观？'
        ]
      }
    ]
  }
]

// 模板服务类
export class MemoirTemplateService {
  // 获取所有模板
  static getAllTemplates(): MemoirTemplate[] {
    return memoirTemplates
  }

  // 根据分类获取模板
  static getTemplatesByCategory(category: MemoirTemplate['category']): MemoirTemplate[] {
    return memoirTemplates.filter(template => template.category === category)
  }

  // 根据ID获取模板
  static getTemplateById(id: string): MemoirTemplate | undefined {
    return memoirTemplates.find(template => template.id === id)
  }

  // 根据难度获取模板
  static getTemplatesByDifficulty(difficulty: MemoirTemplate['difficulty']): MemoirTemplate[] {
    return memoirTemplates.filter(template => template.difficulty === difficulty)
  }

  // 搜索模板
  static searchTemplates(query: string): MemoirTemplate[] {
    const lowercaseQuery = query.toLowerCase()
    return memoirTemplates.filter(template =>
      template.name.toLowerCase().includes(lowercaseQuery) ||
      template.description.toLowerCase().includes(lowercaseQuery) ||
      template.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    )
  }

  // 获取推荐模板
  static getRecommendedTemplates(userPreferences?: {
    categories?: MemoirTemplate['category'][]
    difficulty?: MemoirTemplate['difficulty']
  }): MemoirTemplate[] {
    let templates = memoirTemplates

    if (userPreferences?.categories) {
      templates = templates.filter(template =>
        userPreferences.categories!.includes(template.category)
      )
    }

    if (userPreferences?.difficulty) {
      templates = templates.filter(template =>
        template.difficulty === userPreferences.difficulty
      )
    }

    return templates.slice(0, 3)
  }

  // 计算模板完成进度
  static calculateProgress(template: MemoirTemplate, completedChapters: string[]): {
    percentage: number
    completedCount: number
    totalCount: number
  } {
    const totalCount = template.chapters.length
    const completedCount = completedChapters.length
    const percentage = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0

    return {
      percentage,
      completedCount,
      totalCount
    }
  }
}

// 保持向后兼容
export function getMemoirTemplate(id: string): MemoirTemplate | undefined {
  return MemoirTemplateService.getTemplateById(id)
}

export function getAllMemoirTemplates(): MemoirTemplate[] {
  return MemoirTemplateService.getAllTemplates()
}

export function getMemoirTemplateById(id: string): MemoirTemplate | null {
  return MemoirTemplateService.getTemplateById(id) || null
}
