export interface AlbumTemplate {
  id: string
  name: string
  description: string
  preview: string
  coverImage: string // 模板封面图片路径
  style: {
    layout: 'grid' | 'masonry' | 'timeline' | 'magazine' | 'minimal'
    columns: number
    spacing: 'tight' | 'normal' | 'loose'
    borderRadius: 'none' | 'small' | 'medium' | 'large'
    shadow: 'none' | 'small' | 'medium' | 'large'
    aspectRatio: 'auto' | 'square' | '4:3' | '16:9' | '3:2'
  }
  colors: {
    primary: string
    secondary: string
    background: string
    text: string
    accent: string
  }
  typography: {
    titleFont: string
    bodyFont: string
    titleSize: string
    bodySize: string
    titleWeight: string
    bodyWeight: string
  }
  effects: {
    transition: 'none' | 'fade' | 'slide' | 'zoom' | 'flip'
    duration: number
    hover: boolean
    parallax: boolean
  }
}

export const albumTemplates: AlbumTemplate[] = [
  {
    id: 'classic',
    name: '经典相册',
    description: '传统的网格布局，适合展示家庭照片',
    preview: '/templates/classic-preview.jpg',
    coverImage: '/images/templates/classic-cover.jpg',
    style: {
      layout: 'grid',
      columns: 3,
      spacing: 'normal',
      borderRadius: 'small',
      shadow: 'small',
      aspectRatio: 'auto'
    },
    colors: {
      primary: '#8B4513',
      secondary: '#D2B48C',
      background: '#FFF8DC',
      text: '#2F1B14',
      accent: '#CD853F'
    },
    typography: {
      titleFont: 'serif',
      bodyFont: 'sans-serif',
      titleSize: '1.5rem',
      bodySize: '1rem',
      titleWeight: '600',
      bodyWeight: '400'
    },
    effects: {
      transition: 'fade',
      duration: 300,
      hover: true,
      parallax: false
    }
  },
  {
    id: 'timeline',
    name: '时光轴',
    description: '按时间顺序展示照片，讲述时光故事',
    preview: '/templates/timeline-preview.jpg',
    coverImage: '/images/templates/timeline-cover.jpg',
    style: {
      layout: 'timeline',
      columns: 1,
      spacing: 'loose',
      borderRadius: 'medium',
      shadow: 'medium',
      aspectRatio: '16:9'
    },
    colors: {
      primary: '#4A90E2',
      secondary: '#7BB3F0',
      background: '#F8FBFF',
      text: '#2C3E50',
      accent: '#3498DB'
    },
    typography: {
      titleFont: 'sans-serif',
      bodyFont: 'sans-serif',
      titleSize: '1.75rem',
      bodySize: '1.1rem',
      titleWeight: '700',
      bodyWeight: '400'
    },
    effects: {
      transition: 'slide',
      duration: 500,
      hover: true,
      parallax: true
    }
  },
  {
    id: 'collage',
    name: '拼贴风格',
    description: '创意拼贴布局，展现艺术感',
    preview: '/templates/collage-preview.jpg',
    coverImage: '/images/templates/collage-cover.jpg',
    style: {
      layout: 'masonry',
      columns: 4,
      spacing: 'tight',
      borderRadius: 'large',
      shadow: 'large',
      aspectRatio: 'auto'
    },
    colors: {
      primary: '#E74C3C',
      secondary: '#F39C12',
      background: '#FFFEF7',
      text: '#2C3E50',
      accent: '#9B59B6'
    },
    typography: {
      titleFont: 'display',
      bodyFont: 'sans-serif',
      titleSize: '2rem',
      bodySize: '0.9rem',
      titleWeight: '800',
      bodyWeight: '300'
    },
    effects: {
      transition: 'zoom',
      duration: 400,
      hover: true,
      parallax: false
    }
  },
  {
    id: 'magazine',
    name: '杂志风格',
    description: '现代杂志布局，专业而优雅',
    preview: '/templates/magazine-preview.jpg',
    coverImage: '/images/templates/magazine-cover.jpg',
    style: {
      layout: 'magazine',
      columns: 2,
      spacing: 'normal',
      borderRadius: 'none',
      shadow: 'none',
      aspectRatio: '4:3'
    },
    colors: {
      primary: '#2C3E50',
      secondary: '#34495E',
      background: '#FFFFFF',
      text: '#2C3E50',
      accent: '#E67E22'
    },
    typography: {
      titleFont: 'sans-serif',
      bodyFont: 'serif',
      titleSize: '2.5rem',
      bodySize: '1.2rem',
      titleWeight: '300',
      bodyWeight: '400'
    },
    effects: {
      transition: 'flip',
      duration: 600,
      hover: false,
      parallax: true
    }
  },
  {
    id: 'minimal',
    name: '简约风格',
    description: '极简设计，突出照片本身',
    preview: '/templates/minimal-preview.jpg',
    style: {
      layout: 'grid',
      columns: 2,
      spacing: 'loose',
      borderRadius: 'none',
      shadow: 'none',
      aspectRatio: 'square'
    },
    colors: {
      primary: '#000000',
      secondary: '#666666',
      background: '#FFFFFF',
      text: '#333333',
      accent: '#000000'
    },
    typography: {
      titleFont: 'sans-serif',
      bodyFont: 'sans-serif',
      titleSize: '1.25rem',
      bodySize: '1rem',
      titleWeight: '400',
      bodyWeight: '300'
    },
    effects: {
      transition: 'none',
      duration: 0,
      hover: false,
      parallax: false
    }
  }
]

export const getTemplateById = (id: string): AlbumTemplate | undefined => {
  return albumTemplates.find(template => template.id === id)
}

export const getDefaultTemplate = (): AlbumTemplate => {
  return albumTemplates[0] // 返回经典相册模板作为默认
}

// 生成模板CSS样式
export const generateTemplateCSS = (template: AlbumTemplate): string => {
  const { style, colors, typography, effects } = template
  
  return `
    .album-container {
      background-color: ${colors.background};
      color: ${colors.text};
      font-family: ${typography.bodyFont};
      font-size: ${typography.bodySize};
      font-weight: ${typography.bodyWeight};
    }
    
    .album-title {
      color: ${colors.primary};
      font-family: ${typography.titleFont};
      font-size: ${typography.titleSize};
      font-weight: ${typography.titleWeight};
    }
    
    .photo-grid {
      display: ${style.layout === 'grid' ? 'grid' : 'flex'};
      ${style.layout === 'grid' ? `grid-template-columns: repeat(${style.columns}, 1fr);` : ''}
      gap: ${style.spacing === 'tight' ? '0.5rem' : style.spacing === 'loose' ? '2rem' : '1rem'};
      ${style.layout === 'masonry' ? 'flex-direction: column; flex-wrap: wrap;' : ''}
    }
    
    .photo-item {
      border-radius: ${
        style.borderRadius === 'none' ? '0' :
        style.borderRadius === 'small' ? '0.25rem' :
        style.borderRadius === 'medium' ? '0.5rem' : '1rem'
      };
      box-shadow: ${
        style.shadow === 'none' ? 'none' :
        style.shadow === 'small' ? '0 1px 3px rgba(0,0,0,0.1)' :
        style.shadow === 'medium' ? '0 4px 6px rgba(0,0,0,0.1)' :
        '0 10px 15px rgba(0,0,0,0.1)'
      };
      aspect-ratio: ${style.aspectRatio === 'auto' ? 'auto' : style.aspectRatio};
      transition: ${effects.transition !== 'none' ? `all ${effects.duration}ms ease` : 'none'};
    }
    
    ${effects.hover ? `
    .photo-item:hover {
      transform: ${
        effects.transition === 'zoom' ? 'scale(1.05)' :
        effects.transition === 'slide' ? 'translateY(-5px)' :
        'none'
      };
      box-shadow: ${
        style.shadow !== 'none' ? '0 20px 25px rgba(0,0,0,0.15)' : 'none'
      };
    }
    ` : ''}
    
    .accent-color {
      color: ${colors.accent};
    }
    
    .secondary-bg {
      background-color: ${colors.secondary};
    }
  `
}

// 响应式断点
export const getResponsiveColumns = (template: AlbumTemplate, screenWidth: number): number => {
  const baseColumns = template.style.columns
  
  if (screenWidth < 640) { // mobile
    return Math.min(baseColumns, 1)
  } else if (screenWidth < 768) { // tablet
    return Math.min(baseColumns, 2)
  } else if (screenWidth < 1024) { // small desktop
    return Math.min(baseColumns, 3)
  } else {
    return baseColumns
  }
}
