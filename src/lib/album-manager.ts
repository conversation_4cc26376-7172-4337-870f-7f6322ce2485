export interface AlbumPhoto {
  id: string
  fileName: string
  caption: string
  audioUrl?: string
  audioFileName?: string
  uploadedAt: Date
  fileSize: number
  dimensions?: {
    width: number
    height: number
  }
}

export interface Album {
  id: string
  title: string
  description: string
  coverPhotoId?: string
  photos: AlbumPhoto[]
  createdAt: Date
  lastModified: Date
  status: 'draft' | 'published' | 'archived'
  totalPhotos: number
  totalAudioNarrations: number
}

export interface AlbumSummary {
  id: string
  title: string
  description: string
  coverPhotoUrl?: string
  totalPhotos: number
  totalAudioNarrations: number
  createdAt: Date
  lastModified: Date
  status: 'draft' | 'published' | 'archived'
}

export class AlbumManager {
  private static readonly STORAGE_KEY = 'memoir_albums'
  private static readonly MAX_ALBUMS = 50

  // 获取所有相册
  static getAllAlbums(): Album[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (!stored) return []
      
      const albums = JSON.parse(stored) as Album[]
      return albums.map((album) => ({
        ...album,
        createdAt: new Date(album.createdAt),
        lastModified: new Date(album.lastModified),
        photos: album.photos.map(photo => ({
          ...photo,
          uploadedAt: new Date(photo.uploadedAt)
        }))
      }))
    } catch (error) {
      console.error('Failed to load albums:', error)
      return []
    }
  }

  // 获取相册摘要（用于首页显示）
  static getAlbumSummaries(): AlbumSummary[] {
    const albums = this.getAllAlbums()
    return albums
      .sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime())
      .slice(0, 10) // 只显示最近的10个相册
      .map(album => {
        const coverPhoto = album.coverPhotoId 
          ? album.photos.find(p => p.id === album.coverPhotoId)
          : album.photos[0]
        
        return {
          id: album.id,
          title: album.title,
          description: album.description,
          coverPhotoUrl: coverPhoto?.url,
          totalPhotos: album.totalPhotos,
          totalAudioNarrations: album.totalAudioNarrations,
          createdAt: album.createdAt,
          lastModified: album.lastModified,
          status: album.status
        }
      })
  }

  // 创建新相册
  static createAlbum(
    title: string,
    description: string = ''
  ): Album {
    const album: Album = {
      id: `album_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      title,
      description,
      photos: [],
      createdAt: new Date(),
      lastModified: new Date(),
      status: 'draft',
      totalPhotos: 0,
      totalAudioNarrations: 0
    }

    this.saveAlbum(album)
    return album
  }

  // 保存相册
  static saveAlbum(album: Album): void {
    try {
      const albums = this.getAllAlbums()
      const existingIndex = albums.findIndex(a => a.id === album.id)
      
      // 更新统计信息
      album.totalPhotos = album.photos.length
      album.totalAudioNarrations = album.photos.filter(p => p.audioUrl).length
      album.lastModified = new Date()
      
      if (existingIndex >= 0) {
        albums[existingIndex] = album
      } else {
        albums.push(album)
      }

      // 限制相册数量
      if (albums.length > this.MAX_ALBUMS) {
        albums.sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime())
        albums.splice(this.MAX_ALBUMS)
      }

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(albums))
    } catch (error) {
      console.error('Failed to save album:', error)
    }
  }

  // 获取单个相册
  static getAlbum(albumId: string): Album | null {
    const albums = this.getAllAlbums()
    return albums.find(a => a.id === albumId) || null
  }

  // 删除相册
  static deleteAlbum(albumId: string): void {
    try {
      const albums = this.getAllAlbums()
      const filteredAlbums = albums.filter(a => a.id !== albumId)
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredAlbums))
    } catch (error) {
      console.error('Failed to delete album:', error)
    }
  }

  // 添加照片到相册
  static addPhotoToAlbum(
    albumId: string,
    photo: Omit<AlbumPhoto, 'id' | 'uploadedAt'>
  ): AlbumPhoto | null {
    const album = this.getAlbum(albumId)
    if (!album) return null

    const newPhoto: AlbumPhoto = {
      ...photo,
      id: `photo_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      uploadedAt: new Date()
    }

    album.photos.push(newPhoto)
    this.saveAlbum(album)
    return newPhoto
  }

  // 从相册中删除照片
  static removePhotoFromAlbum(albumId: string, photoId: string): void {
    const album = this.getAlbum(albumId)
    if (!album) return

    album.photos = album.photos.filter(p => p.id !== photoId)
    
    // 如果删除的是封面照片，重置封面
    if (album.coverPhotoId === photoId) {
      album.coverPhotoId = album.photos.length > 0 ? album.photos[0].id : undefined
    }
    
    this.saveAlbum(album)
  }

  // 更新照片信息
  static updatePhoto(
    albumId: string,
    photoId: string,
    updates: Partial<Omit<AlbumPhoto, 'id' | 'uploadedAt'>>
  ): void {
    const album = this.getAlbum(albumId)
    if (!album) return

    const photoIndex = album.photos.findIndex(p => p.id === photoId)
    if (photoIndex === -1) return

    album.photos[photoIndex] = {
      ...album.photos[photoIndex],
      ...updates
    }

    this.saveAlbum(album)
  }

  // 设置封面照片
  static setCoverPhoto(albumId: string, photoId: string): void {
    const album = this.getAlbum(albumId)
    if (!album) return

    const photo = album.photos.find(p => p.id === photoId)
    if (!photo) return

    album.coverPhotoId = photoId
    this.saveAlbum(album)
  }

  // 获取相册统计信息
  static getAlbumStats(): {
    totalAlbums: number
    totalPhotos: number
    totalAudioNarrations: number
    recentActivity: Date | null
  } {
    const albums = this.getAllAlbums()
    
    const totalPhotos = albums.reduce((sum, album) => sum + album.totalPhotos, 0)
    const totalAudioNarrations = albums.reduce((sum, album) => sum + album.totalAudioNarrations, 0)
    const recentActivity = albums.length > 0 
      ? albums.reduce((latest, album) => 
          album.lastModified > latest ? album.lastModified : latest, 
          albums[0].lastModified
        )
      : null

    return {
      totalAlbums: albums.length,
      totalPhotos,
      totalAudioNarrations,
      recentActivity
    }
  }

  // 搜索相册
  static searchAlbums(query: string): Album[] {
    const albums = this.getAllAlbums()
    const lowercaseQuery = query.toLowerCase()
    
    return albums.filter(album => 
      album.title.toLowerCase().includes(lowercaseQuery) ||
      album.description.toLowerCase().includes(lowercaseQuery) ||
      album.photos.some(photo => 
        photo.caption.toLowerCase().includes(lowercaseQuery)
      )
    )
  }
}
